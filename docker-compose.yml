# NOTE: This docker-compose.yml is meant to be just an example guideline
# on how you can achieve the same. It is not intented to run out of the box
# and you must edit the below configurations to suit your needs.

version: "3.7"

x-app-defaults: &app-defaults
  restart: unless-stopped
  image: listmonk/listmonk:latest
  ports:
    - "9000:9000"
  networks:
    - listmonk
  environment:
    - TZ=Etc/UTC

x-db-defaults: &db-defaults
  image: postgres:13
  ports:
    - "9432:5432"
  networks:
    - listmonk
  environment:
    - POSTGRES_PASSWORD=listmonk
    - POSTGRES_USER=listmonk
    - POSTGRES_DB=listmonk
  restart: unless-stopped
  healthcheck:
    test: ["CMD-SHELL", "pg_isready -U listmonk"]
    interval: 10s
    timeout: 5s
    retries: 6

services:
  db:
    <<: *db-defaults
    container_name: listmonk_db
    volumes:
      - type: volume
        source: listmonk-data
        target: /var/lib/postgresql/data

  app:
    <<: *app-defaults
    container_name: listmonk_app
    depends_on:
      - db
    volumes:
      - ./config.toml:/listmonk/config.toml

  demo-db:
    container_name: listmonk_demo_db
    <<: *db-defaults

  demo-app:
    <<: *app-defaults
    container_name: listmonk_demo_app
    command: [sh, -c, "yes | ./listmonk --install --config config-demo.toml && ./listmonk --config config-demo.toml"]
    depends_on:
      - demo-db

networks:
  listmonk:

volumes:
  listmonk-data:
