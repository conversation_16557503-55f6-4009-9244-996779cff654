package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
	null "gopkg.in/volatiletech/null.v6"
)

const (
	// tplTag is the template tag that should be present in a template
	// as the placeholder for campaign bodies.
	tplTag = `{{ template "content" . }}`

	dummyTpl = `
		<p>Hi there</p>
		<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis et elit ac elit sollicitudin condimentum non a magna. Sed tempor mauris in facilisis vehicula. Aenean nisl urna, accumsan ac tincidunt vitae, interdum cursus massa. Interdum et malesuada fames ac ante ipsum primis in faucibus. Aliquam varius turpis et turpis lacinia placerat. Aenean id ligula a orci lacinia blandit at eu felis. Phasellus vel lobortis lacus. Suspendisse leo elit, luctus sed erat ut, venenatis fermentum ipsum. Donec bibendum neque quis.</p>

		<h3>Sub heading</h3>
		<p>Nam luctus dui non placerat mattis. Morbi non accumsan orci, vel interdum urna. Duis faucibus id nunc ut euismod. Curabitur et eros id erat feugiat fringilla in eget neque. Aliquam accumsan cursus eros sed faucibus.</p>

		<p>Here is a link to <a href="https://listmonk.app" target="_blank">listmonk</a>.</p>`
)

var (
	regexpTplTag = regexp.MustCompile(`{{(\s+)?template\s+?"content"(\s+)?\.(\s+)?}}`)
)

type getTemplatesResponse struct {
	Data []struct {
		ID                 int     `json:"id"`
		CreatedAt          string  `json:"created_at"`
		UpdatedAt          string  `json:"updated_at"`
		Name               string  `json:"name"`
		Subject            string  `json:"subject"`
		Type               string  `json:"type"`
		Body               string  `json:"body"`
		Category           int     `json:"category"`
		IsDefault          bool    `json:"is_default"`
		Keyval             *string `json:"keyval"`
		AdditionalValues   string  `json:"additional_values"`
		MessageType        string  `json:"message_type"`
		RequestParams      *string `json:"request_params"`
		URL                *string `json:"url"`
		Headers            *string `json:"headers"`
		RequestBody        *string `json:"request_body"`
		Method             *string `json:"method"`
		RegisteredTemplate *string `json:"registeredTemplate"`
		DefaultPrefs       struct {
			FCM   bool `json:"fcm"`
			SMS   bool `json:"sms"`
			Email bool `json:"email"`
		} `json:"DefaultPrefs"`
		TemplateParams struct{} `json:"template_params"`
	} `json:"data"`
}

// handleGetTemplates handles retrieval of templates.
// @Summary Get email templates
// @Description Fetches one or all email templates based on the provided parameters. If an ID is provided, it retrieves a specific template; otherwise, it returns a list of all templates.
// @Tags templates
// @Accept  json
// @Produce  json
// @Param   id      query    int  false  "Template ID"           // Query parameter for template ID (optional)
// @Param   no_body query    bool false  "No Body Flag"          // Query parameter to exclude the body content (optional)
// @Success 200     {object} getTemplatesResponse "Successful Operation"       // The response for a successful operation
// @Failure 400     {object} HTTPError "Bad Request"             // The response for an invalid request
// @Failure 500     {object} HTTPError "Internal Server Error"   // The response for an internal server error
// @Router  /api/templates [get]
func handleGetTemplates(c echo.Context) error {
	var (
		app = c.Get("app").(*App)

		id, _     = strconv.Atoi(c.Param("id"))
		noBody, _ = strconv.ParseBool(c.QueryParam("no_body"))
	)

	// Fetch one list.
	if id > 0 {
		out, err := app.core.GetTemplate(id, noBody)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, okResp{out})
	}

	out, err := app.core.GetTemplates("", noBody)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handlePreviewTemplate renders the HTML preview of a template.
func handlePreviewTemplate(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	tpl := models.Template{
		Type: c.FormValue("template_type"),
		Body: c.FormValue("body"),
	}

	// Body is posted.
	if tpl.Body != "" {
		if tpl.Type == "" {
			tpl.Type = models.TemplateTypeCampaign
		}

		if tpl.Type == models.TemplateTypeCampaign && !regexpTplTag.MatchString(tpl.Body) {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("templates.placeholderHelp", "placeholder", tplTag))
		}
	} else {
		// There is no body. Fetch the template.
		if id < 1 {
			return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
		}

		t, err := app.core.GetTemplate(id, false)
		if err != nil {
			return err
		}

		tpl = t
	}

	// Compile the campaign template.
	var out []byte
	if tpl.Type == models.TemplateTypeCampaign {
		camp := models.Campaign{
			UUID:         dummyUUID,
			Name:         app.i18n.T("templates.dummyName"),
			Subject:      app.i18n.T("templates.dummySubject"),
			FromEmail:    "<EMAIL>",
			TemplateBody: tpl.Body,
			Body:         dummyTpl,
		}

		if err := camp.CompileTemplate(app.manager.TemplateFuncs(&camp), false); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("templates.errorCompiling", "error", err.Error()))
		}

		// Render the message body.
		msg, err := app.manager.NewCampaignMessage(&camp, dummySubscriber, tracer.WrapEchoContextLogger(c), "")
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("templates.errorRendering", "error", err.Error()))
		}
		out = msg.Body()
	} else {
		// Compile transactional template.
		if err := tpl.Compile(app.manager.GenericTemplateFuncs()); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}

		m := models.TxMessage{
			Subject: tpl.Subject,
		}

		// Render the message.
		if err := m.Render(dummySubscriber, &tpl); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("globals.messages.errorFetching", "name"))
		}
		out = m.Body
	}

	return c.HTML(http.StatusOK, string(out))
}

// handleCreateTemplate handles template creation.
func handleCreateTemplate(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.Template{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		return err
	}

	if err := validateTemplate(o, app); err != nil {
		return err
	}

	var f template.FuncMap

	// Subject is only relevant for fixed tx templates. For campaigns,
	// the subject changes per campaign and is on models.Campaign.
	if o.Type == models.TemplateTypeCampaign {
		o.Subject = ""
		f = app.manager.TemplateFuncs(nil)
	} else {
		f = app.manager.GenericTemplateFuncs()
	}

	// Compile the template and validate.
	if err := o.Compile(f); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	if o.DeDuplication.Bool {
		if o.DuplicationLevel.IsZero() {
			o.DuplicationLevel = null.StringFrom("device")
		}
	}

	jsonData, err := json.Marshal(o.KeyVal)
	if err != nil {
		fmt.Println("error:", err)
	}

	err = validateTemplateSegments(&o, app)
	if err != nil {
		logger.Error().Msgf("error occured in tx api %v", err)
		return err
	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		o.CreatedBy = null.StringFrom(userID)
	}
	// Create the template the in the DB.
	out, err := app.core.CreateTemplate(o.Name, o.Type, o.Subject, []byte(o.Body), o.Category, string(jsonData), o.MessageType, o.TemplateParams, o.SegmentConfig, o.DeDuplication.Bool, o.DuplicationLevel.String, o.ProcessDuration, o.CreatedBy)
	if err != nil {
		return err
	}

	// If it's a transactional template, cache it in the manager
	// to be used for arbitrary incoming tx message pushes.
	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpdateTemplate handles template modification.
func handleUpdateTemplate(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var o models.Template
	if err := c.Bind(&o); err != nil {
		return err
	}

	if err := validateTemplate(o, app); err != nil {
		return err
	}

	var f template.FuncMap

	// Subject is only relevant for fixed tx templates. For campaigns,
	// the subject changes per campaign and is on models.Campaign.
	if o.Type == models.TemplateTypeCampaign {
		o.Subject = ""
		f = app.manager.TemplateFuncs(nil)
	} else {
		f = app.manager.GenericTemplateFuncs()
	}

	// Compile the template and validate.
	if err := o.Compile(f); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}
	jsonData, err := json.Marshal(o.KeyVal)
	if err != nil {
		fmt.Println("error:", err)
	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		o.UpdatedBy = null.StringFrom(userID)
	}
	out, err := app.core.UpdateTemplate(id, o.Name, o.Subject, []byte(o.Body), o.Category, string(jsonData), o.MessageType, o.TemplateParams, o.SegmentConfig, o.DeDuplication.Bool, o.DuplicationLevel.String, o.ProcessDuration, o.UpdatedBy)
	if err != nil {
		return err
	}

	if err := out.Compile(f); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// If it's a transactional template, ask every pod to cache it.
	if o.Type == models.TemplateTypeTx {
		//used for caching the template including sms details
		app.manager.GetSingleTemplate(o.ID)
		PublishStateChangeEvents(out.ID, "templateUpdated", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))

	}

	return c.JSON(http.StatusOK, okResp{out})

}

// handleTemplateSetDefault handles template modification.
func handleTemplateSetDefault(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var updatedBy null.String
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}

	if err := app.core.SetDefaultTemplate(id, updatedBy); err != nil {
		return err
	}

	return handleGetTemplates(c)
}

// handleDeleteTemplate handles template deletion.
func handleDeleteTemplate(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var updatedBy null.String
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}

	if err := app.core.DeleteTemplate(id, updatedBy); err != nil {
		return err
	}

	// Delete cached template.
	app.manager.DeleteTpl(id)

	return c.JSON(http.StatusOK, okResp{true})
}

// compileTemplate validates template fields.
func validateTemplate(o models.Template, app *App) error {
	if !strHasLen(o.Name, 1, stdInputMaxLen) {
		return errors.New(app.i18n.T("campaigns.fieldInvalidName"))
	}

	// if o.Type == models.TemplateTypeCampaign && !regexpTplTag.MatchString(o.Body) {
	// 	return echo.NewHTTPError(http.StatusBadRequest,
	// 		app.i18n.Ts("templates.placeholderHelp", "placeholder", tplTag))
	// }

	if o.Type == models.TemplateTypeTx && strings.TrimSpace(o.Subject) == "" {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.missingFields", "name", "subject"))
	}

	return nil
}

func validateTemplateSegments(o *models.Template, app *App) error {

	exclusionMap := make(map[string]bool)

	excludedSegments := o.SegmentConfig.ExcludedSegments
	includedSegments := o.SegmentConfig.IncludedSegments

	excludedSegmentList := []models.SegmentIdName{}
	includedSegmentList := []models.SegmentIdName{}

	if len(excludedSegments) > 0 {
		for i := range excludedSegments {
			if excludedSegments[i].SegmentId.String() != "" && excludedSegments[i].SegmentName != "" {
				exclusionMap[excludedSegments[i].SegmentId.String()] = true
				excludedSegmentList = append(excludedSegmentList, models.SegmentIdName{
					SegmentName: excludedSegments[i].SegmentName,
					SegmentId:   excludedSegments[i].SegmentId,
				})
			}
		}
	}

	if len(includedSegments) > 0 {
		for i := range includedSegments {

			if includedSegments[i].SegmentId.String() == "" || includedSegments[i].SegmentName == "" {
				continue
			}
			if _, has := exclusionMap[includedSegments[i].SegmentId.String()]; has {
				return echo.NewHTTPError(http.StatusBadRequest,
					app.i18n.Ts("globals.messages.segmentMismatch", "id", includedSegments[i].SegmentId.String()))
			}
			includedSegmentList = append(includedSegmentList, models.SegmentIdName{
				SegmentName: includedSegments[i].SegmentName,
				SegmentId:   includedSegments[i].SegmentId,
			})
		}
	}

	segmentConfig := models.TemplateSegmentConfig{}

	if len(includedSegmentList) > 0 || len(excludedSegmentList) > 0 {
		segmentConfig.ExcludedSegments = excludedSegmentList
		segmentConfig.IncludedSegments = includedSegmentList

	}
	o.SegmentConfig = segmentConfig

	if o.Type == models.TemplateTypeTx && strings.TrimSpace(o.Subject) == "" {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.missingFields", "name", "subject"))
	}

	return nil
}

func handleGetContentTempalte(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.ContentTemplateDetails{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("Error occured while creating contenttemplate details %v", err)
		return err
	}
	// Fetch one list.
	logger.Info().Msgf("Got request for GetContentTemplate with id is %v", o.ID)
	id, _ := strconv.Atoi(string(o.ID))

	if id > 0 {
		out, err := app.core.GetContentTeplate(id)
		if err != nil {
			logger.Error().Msgf("error occured while fetching content template is %v", err)
			return err
		}
		if len(out) == 0 {
			return c.JSON(http.StatusOK, models.ContentTemplateDetails{})
		}
		logger.Info().Msgf("Successfully return content template details")
		return c.JSON(http.StatusOK, out[0])
	}

	return c.JSON(http.StatusOK, models.ContentTemplateDetails{})

}

func handleCreateContentTemplate(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.ContentTemplateDetails{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("Error occured while creating Content Template %v", err)
		return err
	}

	logger.Info().Msgf("Got request for creating Content Template api: %v", o)

	out, err := app.core.CreateContentTemplate(o.Content, o.Name, o.HtmlData)
	if err != nil {
		logger.Error().Msgf("Error occured while creating content template %v", err)
		return err
	}

	return c.JSON(http.StatusOK, out)

}

func handleGetContentTemplateList(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
	)

	out, err := app.core.GetContentTeplate(0)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, out)
}

// handleUpdateGateway handles template modification.
func handleUpdateContentTemplate(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.ContentTemplateDetails{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("Error occured while binding content template object %v", err)
		return err
	}

	logger.Info().Msgf("Got request for updateContentTemplate api with id is %v", o.ID)
	id, _ := strconv.Atoi(string(o.ID))

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	payload, err := json.Marshal(o)

	if err != nil {
		logger.Error().Msgf("Error occured while marshalling content template object %v", err)
		return err
	}

	logger.Info().Msgf("Got request for updating content template api: %v", string(payload))

	out, err := app.core.UpdateContentTemplate(id, o.Content, o.Name, o.HtmlData)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, out)

}

func handleDeleteContentTemplate(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.ContentTemplateDetails{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("Error occured while binding content template object %v", err)
		return err
	}

	logger.Info().Msgf("Got request for deleteContentTemplate with id is %v", o.ID)
	id, _ := strconv.Atoi(string(o.ID))

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}
	if err := app.core.DeleteContentTemplate(id); err != nil {
		return err
	}
	logger.Info().Msgf("successfully deleted ContentTemplate with id %d", o.ID)
	return c.JSON(http.StatusOK, true)
}
