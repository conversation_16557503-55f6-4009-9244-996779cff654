FROM ************.dkr.ecr.ap-south-1.amazonaws.com/redhat/ubi8:8.6
LABEL maintainer="Mintoak" releaseversion="4.1.0" applicationname="listmonk" is-production="false"
#ENV TZ="Asia/Kolkata"
ENV TZ="Indian/Mauritius"
RUN yum update -y && yum install -y gcc git
ENV GO_VERSION=1.20.5
ARG FOLDER_NAME
ENV FOLDER_NAME $FOLDER_NAME
RUN curl -OL https://dl.google.com/go/go${GO_VERSION}.linux-amd64.tar.gz && tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz && rm go${GO_VERSION}.linux-amd64.tar.gz
ENV PATH=$PATH:/usr/local/go/bin \
    GOPATH=/go \
    GOBIN=/go/bin
WORKDIR /listmonk
COPY . . 
RUN mkdir -p /home/<USER>/listmonk
RUN rm -rf ~/.aws/credentials
RUN  aws s3 cp s3://absaappconfig/$FOLDER_NAME/Listmonk/config.toml /home/<USER>/listmonk/
RUN  aws s3 cp s3://absaappconfig/$FOLDER_NAME/Listmonk/serviceAccountKey.json /home/<USER>/listmonk/
RUN go build
EXPOSE 9000
CMD ["./listmonk"]
