pipeline {
    agent any
      environment {
          mvnHome = tool name: 'maven', type: 'maven'
          scannerHome = tool name: 'Sonar', type: 'hudson.plugins.sonar.SonarRunnerInstallation'
          Key = jiraIssueSelector(issueSelector: [$class: 'DefaultIssueSelector'])
          awsAccess = credentials('jenkins-aws-access-key')
          awsSecret = credentials('jenkins-aws-secret-key')
          argocdPassDev = credentials('argo-pass-dev')
          argocdPassUAT = credentials('argo-pass')
	        appname = "axis-listmonk"
	        tag = "${BUILD_NUMBER}"
	        Ecr_registry = "${EcrRegistryUrl}/${appname}"
          }

   stages {
   stage('Create Git Tag for Production'){
        when { branch 'master' }
        steps {
          withCredentials([usernamePassword(credentialsId: 'Mintoak_Bitbucket', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
            sh '''
              TAGVERSION=$(git log --merges --grep="pull request #" master | grep -E "release/|hotfix/" | head -n 1 | grep -oE "(release|hotfix)/[0-9.]*" | sed 's/\\(release\\|hotfix\\)\\///')
              echo v$TAGVERSION
              git tag -f -a v$TAGVERSION -m "New release for v$TAGVERSION"
              git push -f --tags https://lokeshbuddappagari:$<EMAIL>/mintoak/listmonk.git
              '''
            }
        }
      }
    stage ('GitVersion Determine'){
      steps {
        sh '''
          export DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
          gitversion /config /var/lib/jenkins/gitversion/GitVersion.yml /output buildserver
        '''
            script {
            def props = readProperties file: 'gitversion.properties'
            env.GitVersion_SemVer = props.GitVersion_SemVer
            env.GitVersion_BranchName = props.GitVersion_BranchName
            env.GitVersion_AssemblySemVer = props.GitVersion_AssemblySemVer
            env.GitVersion_MajorMinorPatch = props.GitVersion_MajorMinorPatch
            env.GitVersion_Sha = props.GitVersion_Sha
            echo "Current Version: ${GitVersion_SemVer}"
            currentBuild.displayName = "${GitVersion_SemVer}-${BUILD_NUMBER}"
          }
        }
  }

  stage('Docker Axis build Dev'){
        when { branch 'develop'}
          steps {
            script {      
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} -f AXISDockerfile ."
         sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:dev"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker push ${Ecr_registry}:dev || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer}"
         sh "docker rmi -f ${Ecr_registry}:dev"
         sh "sleep 60"
        }  
      }
    post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }     
   
  
      stage('Docker Axis build UAT'){
        when { branch '*release/**'}
          steps {
            script { 
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} -f AXISDockerfile ."
         sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:uat"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker push ${Ecr_registry}:uat || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:uat"
         
        }   
      }
    post {
           always {
             jiraSendDeploymentInfo site: 'mintoak.atlassian.net', environmentId: 'uat-env', environmentName: 'ecs_uat', environmentType: 'testing'
             }
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }
       
    stage('Docker Axis build Production'){
        when { branch 'master'}
          steps {
            script {      
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} -f AXISDockerfile ."
         sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:prod"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker push ${Ecr_registry}:prod || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer}"
         sh "docker rmi -f ${Ecr_registry}:prod"
         sh "sleep 60"
        }  
      }
    post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }
       stage('K8s Dev-Deploy'){
     when { branch 'develop' }
     steps{
           sh "yes | argocd login ${ArgoCDDevUrl} --username admin --password ${argocdPassDev}"
           sh "argocd app actions run listmonk-axis restart --kind Rollout --resource-name listmonk-deploy"
         }
       post {
	  always {
             jiraSendDeploymentInfo site: 'mintoak.atlassian.net', environmentId: 'App-cluster', environmentName: 'Develop', environmentType: 'development'
             }
	 success {
             script{
                     emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    Deployment Server: app-cluster
    ---------------------------
    SonarQube Report generated (code analysis + code coverage)
    Sonar Quality Gate is Passed, check URL: ${SONAR_URL}

    Junit Test-Cases:
    Total tests = $TEST_COUNTS
    Passed = ${TEST_COUNTS,var="pass"}
    Failed = ${TEST_COUNTS,var="fail"}
    Test cases Results:
    ${FAILED_TESTS}
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
             }
           }       
         }
       }
       
    stage('Clean Workspace After build') {
            when { not { branch 'master' } }
            steps {
                sh "ls -all"
                cleanWs()
                sh "ls -all"
                sh "pwd"
            }
        }
  }
}