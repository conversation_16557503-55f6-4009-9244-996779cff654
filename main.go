package main

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

var localClient *redis.Client
var ctx = context.Background()

// Initialize Redis connection
func InitializeLocalRedis(hostname string) *redis.Client {
	localClient = redis.NewClient(&redis.Options{
		Addr:     hostname,
		Password: "",
		DB:       0,
	})
	if err := localClient.Ping(ctx).Err(); err != nil {
		panic("Unable to connect to local Redis: " + err.Error())
	}
	fmt.Println("Connected successfully with local Redis.")
	return localClient
}

// Lua script for member insertion and TTL update
var luaScript = `
-- Add the member if it doesn't exist
if redis.call('SISMEMBER', KEYS[1], ARGV[1]) == 0 then
    redis.call('SADD', KEYS[1], ARGV[1])
end

-- Check if TTL needs to be updated
local ttl = redis.call('TTL', KEYS[1])
if ttl == -1 or ttl < tonumber(ARGV[2]) then
    redis.call('EXPIRE', KEYS[1], ARGV[2])
end

return 1  -- Operation complete
`

// Insert member into Redis set, with TTL managed inside Lua script
func insertMember(campaignID, memberTarget string, ttl, refreshThreshold int) {
	// Use hash tag to ensure all members of a set are stored on the same node
	setKey := fmt.Sprintf("{process::%s}", campaignID) // e.g., campaign:set:{C1}

	// Member naming convention: campaignID:target (e.g., C1:<EMAIL>)
	member := fmt.Sprintf("%s:%s", campaignID, memberTarget)

	// Execute Lua script for atomic operation
	vals := localClient.Eval(ctx, luaScript, []string{setKey}, member, ttl, refreshThreshold)
	result, err := vals.Result()
	if err != nil {
		fmt.Println("Error executing Lua script:", err)
		return
	}

	fmt.Printf("Result for member %s: %v\n", member, result)
}

func main() {
	// Initialize Redis connection
	InitializeLocalRedis("localhost:6379")

	campaignID := "C1"
	memberEmail := "<EMAIL>"
	memberPhone := "7453008795"
	ttl := 40 // TTL of 5 minutes (in seconds)
	refreshThreshold := 60

	// Launch multiple go routines to simulate concurrent inserts
	go insertMember(campaignID, memberEmail, ttl, refreshThreshold)
	go insertMember(campaignID, memberPhone, ttl, refreshThreshold)

	// Wait for some time to observe the results
	time.Sleep(5 * time.Second)
}
