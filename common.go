package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/jaytaylor/html2text"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/outbound"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
	"gopkg.in/volatiletech/null.v6"
)

const (
	CONTENT_TYPE     = "Content-Type"
	APPLICATION_JSON = "application/json"
	FORM_DATA        = "application/x-www-form-urlencoded"
)

func CreateListExternal(ListIDs []models.List, c echo.Context) ([]models.List, []int, error) {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)
	redisMap, err := getSegmentList(app, tracer.WrapEchoContextLogger(c))
	var newLists []int
	if err != nil {
		logger.Error().Msgf("error while getting segment_list data: %v", err)
	}
	var list models.List
	var data []models.List
	var tags []string
	for _, item := range ListIDs {
		// segment data but added in listmonk
		if len(item.Tags) > 0 && item.Description == "Segment" || item.Description == "Listmonk" {
			data = append(data, item)
		} else {
			//check for duplicates
			existingSegment, err := app.core.GetListsByName(item.Name, logger)
			if err != nil {
				logger.Error().Msgf("error getting existing segment data: %v", err)
			}
			if len(existingSegment) == 0 {
				var listData = redisMap[item.ID]
				list.Name = listData.SegmentName
				list.Status = null.StringFrom("pending")
				id := int(listData.SegmentId)
				tags = append(tags, strconv.Itoa(id))
				// list.Tags = tags
				list.Tags = []string{strconv.Itoa(id)}
				list.Description = "Segment"
				out, err := app.core.CreateList(list, logger)
				newLists = append(newLists, out.ID)
				if err != nil {
					logger.Error().Msgf("error creating list: %v", err)
				}
				data = append(data, out)
			} else {
				data = append(data, existingSegment...)
			}
		}

	}
	return data, newLists, err
}

func CreateListExternalWithListReturn(ListIDs []models.List, c echo.Context) []int {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)
	redisMap, err := getSegmentList(app, tracer.WrapEchoContextLogger(c))
	if err != nil {
		logger.Error().Msgf("error while getting segment_list data: %v", err)
	}
	var list models.List
	var data []int
	var tags []string
	for _, item := range ListIDs {
		// segment data but added in listmonk
		if len(item.Tags) > 0 && item.Description == "Segment" || item.Description == "Listmonk" {
			data = append(data, item.ID)
		} else {
			//check for duplicates
			existingSegment, err := app.core.GetListsByName(item.Name, logger)
			if err != nil {
				logger.Error().Msgf("error getting existing segment data: %v", err)
			}
			if len(existingSegment) == 0 {
				var listData = redisMap[item.ID]
				list.Name = listData.SegmentName
				id := int(listData.SegmentId)
				tags = append(tags, strconv.Itoa(id))
				list.Tags = tags
				list.Description = "Segment"
				out, err := app.core.CreateList(list, logger)
				if err != nil {
					logger.Error().Msgf("error creating list: %v", err)
				}
				data = append(data, out.ID)
			} else {
				data = append(data, existingSegment[0].ID)
			}
		}
	}
	return data
}

func htmlToPlainText(html string) string {
	text, err := html2text.FromString(html)
	if err != nil {
		fmt.Println(err)
	}
	return text
}

func Login(email string, password string, c echo.Context) models.LoginResponse {
	logger := c.Get("logger").(push.Logger)

	url := config.ServerConfig.OneAppAuthUrl + "OneAppAuth/adminPortalLogin"

	logger.Info().Msgf("calling login URL:> %v", url)

	r := LoginReq{email, password}

	jsonStr, err := json.Marshal(r)

	if err != nil {
		logger.Error().Msgf("error occured marshalling json , detailed error : %v", err)
		return models.LoginResponse{}
	}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		logger.Error().Msgf("error occured while creating http request , detailed error : %v", err)
		return models.LoginResponse{}
	}

	req.Header.Set(CONTENT_TYPE, APPLICATION_JSON)

	client, err := tracer.GetHttpClient(config.ServerConfig.SkipSSLCheck)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return models.LoginResponse{}
	}

	req = tracer.GetRequestWithTraceEcho(req, c)

	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while calling login api %v", err)
		return models.LoginResponse{}
	}

	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %v", resp.Status)
	body, _ := io.ReadAll(resp.Body)

	response := models.LoginResponse{}

	err = json.Unmarshal((body), &response)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
	}

	logger.Info().Msgf("response Body: %s", string(body))
	return response
}

type LoginReq struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type Request struct {
	TerminalId string `json:"terminalId"`
}

// for sending sms
func SendMessageV2(requestParams string, targetUrl string, requestHeaders string, requestBody string, method string, messenger string, ctx context.Context) (string, error) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	var req *http.Request
	method = strings.ToUpper(method)
	if method == "GET" {
		if len(requestParams) != 0 {

			params := make(map[string]interface{})

			err := json.Unmarshal([]byte(requestParams), &params)

			if err != nil {
				logger.Error().Msgf("Error %v", err)
				return "", err
			}
			queryParams := url.Values{}
			for key, value := range params {
				queryParams.Add(key, fmt.Sprintf("%v", value))
			}
			targetUrl += `?` + queryParams.Encode()
		}
		logger.Info().Msgf("%v Messenger: sending request  %v", messenger, targetUrl)
		req, _ = http.NewRequest(method, targetUrl, nil)
	} else {
		logger.Info().Msgf("%v Messenger: sending request %v", messenger, requestBody)
		req, _ = http.NewRequest(method, targetUrl, bytes.NewBufferString(requestBody))
	}

	if len(requestHeaders) != 0 {

		header := make(map[string]string)

		err := json.Unmarshal([]byte(requestHeaders), &header)

		if err != nil {
			return "", err
		}
		for key, value := range header {
			req.Header.Set(key, fmt.Sprintf("%v", value))
		}
	}

	req.Header.Set(CONTENT_TYPE, APPLICATION_JSON)

	client := outbound.GetOutboundClient(config.ServerConfig.SkipSSLCheck)

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error().Msgf("Error reading response body: %v", err)
		return "", err
	}

	logger.Info().Msgf("%v Messenger: response from provider: %v", messenger, string(body))
	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected response status code: %d", resp.StatusCode)
	} else {
		logger.Info().Msgf("%v Messenger: Successfully sent message", messenger)
	}
	return string(body), nil
}
