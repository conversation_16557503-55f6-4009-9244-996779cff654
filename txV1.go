package main

import (
	"net/http"
	"net/textproto"

	"github.com/knadh/listmonk/internal/manager"
	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

// handleSendTxMessage handles the sending of a transactional message.
func handleSendTxMessageV1(c echo.Context) error {
	lo.Printf("Got request for txn api v1")
	var (
		app    = c.Get("app").(*App)
		t      models.TransactionMsg
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&t); err != nil {
		return err
	}
	var m = t.TransactionMsg
	for _, item := range m {
		var txnmsg models.TxMessage
		var memberIds []string
		memberIds = append(memberIds, item.MemberId)
		txnmsg.MemberIds = memberIds
		txnmsg.MemberType = item.MemberType
		txnmsg.Messenger = item.Messenger

		// Validate input.
		if r, err := validateTxMessageV1(txnmsg, app); err != nil {
			logger.Error().Msgf("Error in txn api", err)
			continue
		} else {
			txnmsg = r
		}

		// Get the cached tx template.
		tpl, err := app.manager.GetTpl(item.TemplateID)
		if err != nil {
			continue
			// return echo.NewHTTPError(http.StatusBadRequest,
			// 	app.i18n.Ts("globals.messages.notFound", "name", fmt.Sprintf("template %d", item.TemplateID)))
		}

		var sub models.Subscriber
		sub.Name = item.MemberId
		sub.Type = item.MemberType

		// Render the message.
		if err := txnmsg.Render(sub, tpl); err != nil {
			continue
			// return echo.NewHTTPError(http.StatusBadRequest,
			// 	app.i18n.Ts("globals.messages.errorFetching", "name"))
		}

		// Prepare the final message.
		msg := manager.Message{}
		msg.Subscriber = sub
		msg.To = []string{sub.Email}
		msg.From = item.FromEmail
		msg.Subject = item.Subject
		msg.ContentType = item.ContentType
		msg.Messenger = item.Messenger
		msg.Body = item.Body
		msg.MessageType = tpl.MessageType

		// Optional headers.
		if len(item.Headers) != 0 {
			msg.Headers = make(textproto.MIMEHeader, len(item.Headers))
			for _, set := range item.Headers {
				for hdr, val := range set {
					msg.Headers.Add(hdr, val)
				}
			}
		}

		if err := app.manager.PushMessage(msg); err != nil {
			logger.Error().Msgf("error sending message (%s): %v", msg.Subject, err)
			continue
			//return err
		}
	}
	logger.Info().Msgf("Got OK response from txn api")
	return c.JSON(http.StatusOK, okResp{true})
}

func validateTxMessageV1(m models.TxMessage, app *App) (models.TxMessage, error) {
	if len(m.MemberIds) == 0 {
		return m, echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.invalidFields", "name", "send member ids."))
	}

	if m.Messenger == "" {
		m.Messenger = emailMsgr
	} else if !app.manager.HasMessenger(m.Messenger) {
		return m, echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("campaigns.fieldInvalidMessenger", "name", m.Messenger))
	}

	return m, nil
}
