package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
	"gopkg.in/volatiletech/null.v6"
)

var (
	payloadType = []string{"list", "individual"}
	authType    = []string{"API_KEY", "NONE"}
)

func handleUpsertWebhookConfig(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.WebhookConfig{}
		logger = c.Get("logger").(push.Logger)
	)

	logger.Info().Msgf("got request for webhook config api")

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("error in webhook config api, detailed error %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	if err := validateWebhookConfig(&o); err != nil {
		logger.Error().Msgf("error in webhook validation api, detailed error %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		o.CreatedBy = &userID
		o.UpdatedBy = &userID
	}

	count, err := app.core.ValidateWebhookConfig(o.Provider, o.Messenger, o.Id.String)

	if err != nil {
		logger.Error().Msgf("error in webhook api, while checking for conflict,  detailed error %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError)
	}

	if count > 0 {
		logger.Error().Msgf("error in webhook api, webhook_config for (provider, messenger) (%v, %v) already exists", o.Provider, o.Messenger)
		return echo.NewHTTPError(http.StatusConflict, "Configuration for the same provider and messenger already exists")
	}

	f := app.manager.WebhookFuncs(&o)

	// Compile the template and validate.
	if err := o.Compile(f); err != nil {
		logger.Error().Msgf("error in webhook api, while compiling the config,  detailed error %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	out, err := app.core.UpsertWebhookConfig(o, logger)

	if err != nil {
		logger.Error().Msgf("error in webhook validation api detailed error %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError)
	}

	broadcastPayload := models.BroadcastPayloadCustom{
		BroadcastPayload: models.BroadcastPayload{
			EventId:   uuid.NewString(),
			EventTime: time.Now().String(),
			DataId:    o.Id.String,
			EventName: "webhookUpdated",
		},
	}

	app.manager.GetSingleWebC(out.Id.String)
	err = utils.PublishBroadcastEvent(broadcastPayload, tracer.WrapEchoContextLogger(c))

	if err != nil {
		logger.Error().Msgf("error in webhook validation api detailed error %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError)
	}

	response := make(map[string]string)
	response["id"] = out.Id.String

	res := customResponse{
		okResp: okResp{
			response,
		},
		Status:  "success",
		Message: "Webhook configuration created",
	}

	return c.JSON(http.StatusOK, res)
}

func handleGetWebhookConfig(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.WebhookConfig{}
		logger = c.Get("logger").(push.Logger)
	)

	logger.Info().Msgf("got request for get-webhook-config api")

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("error in webhook config api, detailed error %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	ids := []string{}

	if o.Id.Valid {
		ids = append(ids, o.Id.String)
	}

	out, err := app.core.GetWebhookConfig(ids, logger)

	if err != nil {
		logger.Error().Msgf("error in webhook api database operation, detailed error %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, okResp{out})
}

func handleDeleteWebhookConfig(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.WebhookConfig{}
		logger = c.Get("logger").(push.Logger)
	)
	userID := ""

	if c.Request() != nil && c.Request().Header != nil {
		userID = c.Request().Header.Get("kcUserId")
	}

	logger.Info().Msgf("got request for delete-webhook-config api by user %v", userID)

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("error in delete-webhook-config api, detailed error %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	if !o.Id.Valid {
		logger.Error().Msg("error in delete-webhook-config api, invalid id")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid id")
	}

	out, err := app.core.GetWebhookConfig([]string{o.Id.String}, logger)

	if len(out) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("config with id %v not found", o.Id.String))
	}

	err = app.core.DeleteWebhookConfig(o.Id.String, logger)

	if err != nil {
		logger.Error().Msgf("error in webhook api database operation, detailed error %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}
	res := customResponse{
		okResp: okResp{
			fmt.Sprintf("Succesfully deleted webhook_config with id %v", out[0].Id.String),
		},
		Status:  "success",
		Message: "Webhook config deleted",
	}

	return c.JSON(http.StatusOK, okResp{res})
}

func validateWebhookConfig(wc *models.WebhookConfig) error {
	if wc.Messenger == "" {
		return errors.New("messenger is mandatory")
	}

	if wc.Provider == "" {
		return errors.New("Provider is mandatory")
	}

	if wc.PayloadType == "" || !inArray(wc.PayloadType, payloadType) {
		return errors.New("PayloadType is invalid")
	}

	if wc.AuthType == "" || !inArray(wc.AuthType, authType) {
		return errors.New("AuthType is invalid")
	}

	if wc.PayloadTemplate == "" {
		return errors.New("payload template is mandatory")
	}

	if wc.PayloadTemplate == "" {
		return errors.New("transformer is mandatory")
	}

	uuid := uuid.NewString()

	if !wc.Id.Valid {
		wc.Id = null.StringFrom(uuid)
	}

	return nil
}

func handleProcessWebhook(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id     = c.Param("id")
		logger = c.Get("logger").(push.Logger)
	)

	if !isValidUUID(id) {
		response := customResponse{
			Status:  "failed",
			Message: "invalid id",
		}
		return c.JSON(http.StatusBadRequest, response)
	}

	return GenericHandler(app, id, c, logger)

}

func GenericHandler(app *App, id string, c echo.Context, log push.Logger) error {
	contentType := c.Request().Header.Get(echo.HeaderContentType)

	response := customResponse{
		Status:  "success",
		Message: "succesfully processed the request",
	}

	// Handle multipart requests separately
	if strings.HasPrefix(contentType, "multipart/") {
		return handleMultipart(c, log)
	}

	// Handle other content types
	body, err := io.ReadAll(c.Request().Body)
	if err != nil {
		response.Status = "failed"
		response.Message = "Invalid request format"
		return c.JSON(http.StatusBadRequest, response)
	}
	defer c.Request().Body.Close()

	log.Info().Msgf("Content-Type: %s", contentType)
	log.Info().Msgf("Raw body (%d bytes): %s", len(body), string(body))

	webC, err := app.manager.GetWebhookConfFromMap(id)

	if err != nil {
		response.Status = "failed"
		response.Message = err.Error()
		log.Error().Msgf("error occured in webhook process api detailed error %v", err)
		return c.JSON(http.StatusBadRequest, response)
	}

	if c.Request().Header == nil && !webC.IgnoreAuth {
		return c.JSON(http.StatusUnauthorized, "UnAuthorized")
	}

	// Currently only handling the api-key based Auth

	authKey := c.Request().Header.Get("secret-key")

	if !webC.IgnoreAuth && webC.APIKey != authKey {
		return c.JSON(http.StatusUnauthorized, "UnAuthorized")
	}

	// Try to parse JSON if applicable
	if strings.Contains(contentType, echo.MIMEApplicationJSON) {
		var payload models.NatsWebhookPayload

		payload.EventTime = time.Now().Format("02-01-2006 15:04:05")
		payload.EventUuid = uuid.NewString()
		payload.EventName = "WebhookReceivedEvent"
		payload.SourceSystem = "listmonk"

		webED := models.WebhookEventData{
			Id:      id,
			Payload: string(body),
		}

		payload.EventData = webED

		jsonData, err := json.Marshal(payload)

		if err != nil {
			response.Status = "failed"
			response.Message = "Internal Server Error"
			log.Error().Msgf("error occured in webhook process api detailed error %v", err)
			return c.JSON(http.StatusInternalServerError, response)
		}

		PublishToNats(jsonData, webhookSubject, "", tracer.WrapEchoContextLogger(c))

	}

	log.Info().Msgf("Succesfully proccesed processWebhook api for webhook id %v", id)

	return c.JSON(http.StatusOK, response)
}

func handleMultipart(c echo.Context, log push.Logger) error {
	response := customResponse{
		Status:  "success",
		Message: "succesfully processed the request",
	}
	form, err := c.MultipartForm()
	if err != nil {
		log.Error().Msgf("Error parsing multipart form: %v", err)
		response.Status = "failed"
		response.Message = "Invalid Mutipart form"
		return c.JSON(http.StatusBadRequest, response)
	}

	// Log form values
	log.Info().Msg("Multipart Form Values:")
	for key, values := range form.Value {
		log.Info().Msgf("  %s: %v", key, values)
	}

	// Log files
	log.Info().Msg("Multipart Files:")
	for fieldName, files := range form.File {
		for _, file := range files {
			log.Info().Msgf("  %s: %s (size: %d bytes)", fieldName, file.Filename, file.Size)
			src, err := file.Open()
			if err != nil {
				log.Info().Msgf("Couldn't open file: %v", err)
				continue
			}
			defer src.Close()

			buf := make([]byte, 256)
			n, _ := io.ReadFull(src, buf)
			log.Info().Msgf("    First %d bytes: %q", n, buf[:n])
		}
	}

	response.Data = map[string]string{"respMsg": "Succesfully proccessed the request"}

	return c.JSON(http.StatusOK, response)
}

func isValidUUID(u string) bool {
	_, err := uuid.Parse(u)
	return err == nil
}

func processWebHook(natsPayload models.NatsWebhookPayload, ctx context.Context) {
	log := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	log.Info().Msgf("Proccess webhook nats event with id: %v", natsPayload.EventUuid)
	wED := natsPayload.EventData
	webC, err := app.manager.GetWebhookConfFromMap(wED.Id)
	if err != nil {
		log.Error().Msgf("error while processing webhook from nats consumer, detailed error %v", err)
		return
	}
	wbR := models.WebhookRequest{
		Id:        wED.Id,
		Provider:  webC.Provider,
		Messenger: webC.Messenger,
	}

	var data interface{}
	if err := json.Unmarshal([]byte(wED.Payload), &data); err != nil {
		log.Error().Msgf("error while unmarshalling json in processing webhook, detailed error %v", err)
		return
	}

	err = wbR.Render(data, nil, webC)

	if err != nil {
		log.Error().Msgf("error while rendering webhook payload, detailed error %v", err)
		return
	}

	log.Info().Msgf("Succesfully proccessed webhook nats event with id: %v", natsPayload.EventUuid)

}
