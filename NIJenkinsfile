pipeline {
    agent any
      environment {
          mvnHome = tool name: 'maven', type: 'maven'
          scannerHome = tool name: 'Sonar', type: 'hudson.plugins.sonar.SonarRunnerInstallation'
          Key = jiraIssueSelector(issueSelector: [$class: 'DefaultIssueSelector'])
          awsAccess = credentials('jenkins-aws-access-key')
          awsSecret = credentials('jenkins-aws-secret-key')
          argocdPassDev = credentials('argo-pass-dev')
          azureargocdPassDev = credentials('azure-argo-pass-dev')
          //argocdPassUAT = credentials('argo-pass')
          appname = "ni-listmonk"
          tag = "${BUILD_NUMBER}"
          Ecr_registry = "${EcrRegistryUrl}/${appname}"
          branch = "${GIT_BRANCH}"
          ACR_LOGIN_SERVER = "mintoak.azurecr.io"
          }

   stages {
   stage('Create Git Tag for Production'){
        when { branch 'master' }
        steps {
          withCredentials([usernamePassword(credentialsId: 'Mintoak_Bitbucket', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
            sh '''
              TAGVERSION=$(git log --merges --grep="pull request #" master | grep -E "release/|hotfix/" | head -n 1 | grep -oE "(release|hotfix)/[0-9.]*" | sed 's/\\(release\\|hotfix\\)\\///')
              echo v$TAGVERSION
              git tag -f -a v$TAGVERSION -m "New release for v$TAGVERSION"
              git push -f --tags https://lokeshbuddappagari:$<EMAIL>/mintoak/listmonk.git
              '''
            }
        }
      }
    stage ('GitVersion Determine'){
      steps {
        sh '''
          export DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
          gitversion /config /var/lib/jenkins/gitversion/GitVersion.yml /output buildserver
        '''
            script {
            def props = readProperties file: 'gitversion.properties'
            env.GitVersion_SemVer = props.GitVersion_SemVer
            env.GitVersion_BranchName = props.GitVersion_BranchName
            env.GitVersion_AssemblySemVer = props.GitVersion_AssemblySemVer
            env.GitVersion_MajorMinorPatch = props.GitVersion_MajorMinorPatch
            env.GitVersion_Sha = props.GitVersion_Sha
            echo "Current Version: ${GitVersion_SemVer}"
            currentBuild.displayName = "${GitVersion_SemVer}-${BUILD_NUMBER}"
          }
        }
  }
    
  stage('Docker Develop Azure Build'){
       when { branch 'develop_azure' }
         steps {
            script {
              withCredentials([usernamePassword(credentialsId: 'azure-acr', usernameVariable: 'SERVICE_PRINCIPAL_ID', passwordVariable: 'SERVICE_PRINCIPAL_PASSWORD')]) {
                  sh "docker login ${ACR_LOGIN_SERVER} -u $SERVICE_PRINCIPAL_ID -p $SERVICE_PRINCIPAL_PASSWORD"
                  sh "docker build --no-cache -t ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} --build-arg VERSION=${GitVersion_SemVer} ."
                  sh "docker tag ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} ${ACR_LOGIN_SERVER}/${appname}:dev"
                  sh "docker push ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} || true"
                  sh "docker push ${ACR_LOGIN_SERVER}/${appname}:dev || true"
                  sh "docker rmi -f ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} ${ACR_LOGIN_SERVER}/${appname}:dev"
              }
         }   
       } 
     post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
         }  
      }
      stage('Docker UAT Azure Build'){
       when { branch '*release/**' }
         steps {
            script {
              withCredentials([usernamePassword(credentialsId: 'azure-acr', usernameVariable: 'SERVICE_PRINCIPAL_ID', passwordVariable: 'SERVICE_PRINCIPAL_PASSWORD')]) {
                  sh "docker login ${ACR_LOGIN_SERVER} -u $SERVICE_PRINCIPAL_ID -p $SERVICE_PRINCIPAL_PASSWORD"
                  sh "docker build --no-cache -t ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} --build-arg VERSION=${GitVersion_SemVer} ."
                  sh "docker tag ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} ${ACR_LOGIN_SERVER}/${appname}:uat"
                  sh "docker push ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} || true"
                  sh "docker push ${ACR_LOGIN_SERVER}/${appname}:uat || true"
                  sh "docker rmi -f ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} ${ACR_LOGIN_SERVER}/${appname}:uat"
              }
         }   
       } 
     post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
         }  
      }
      stage('Docker build Production'){
       when { branch 'master' }
         steps {
            script {
              withCredentials([usernamePassword(credentialsId: 'azure-acr', usernameVariable: 'SERVICE_PRINCIPAL_ID', passwordVariable: 'SERVICE_PRINCIPAL_PASSWORD')]) {
                  sh "docker login ${ACR_LOGIN_SERVER} -u $SERVICE_PRINCIPAL_ID -p $SERVICE_PRINCIPAL_PASSWORD"
                  sh "docker build --no-cache -t ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} --build-arg VERSION=${GitVersion_SemVer} ."
                  sh "docker tag ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} ${ACR_LOGIN_SERVER}/${appname}:prod"
                  sh "docker push ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} || true"
                  sh "docker push ${ACR_LOGIN_SERVER}/${appname}:prod || true"
                  sh "docker rmi -f ${ACR_LOGIN_SERVER}/${appname}:${GitVersion_SemVer} ${ACR_LOGIN_SERVER}/${appname}:prod"
              }
         }   
       } 
     post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
         }  
      }
  
  stage('K8s azure dev Deploy'){
      when { branch 'develop_azure' }
     steps{
           sh "yes | argocd login ${NIAzureArgoCDDevUrl} --username admin --password ${azureargocdPassDev}"
           sh "argocd app actions run listmonk restart --kind Rollout --resource-name listmonk-deploy"
         }
       post {
	  always {
             jiraSendDeploymentInfo site: 'mintoak.atlassian.net', environmentId: 'App-cluster', environmentName: 'UAT', environmentType: 'testing'
             }
	 success {
             script{
                     emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    Deployment Server: app-cluster
    ---------------------------
    SonarQube Report generated (code analysis + code coverage)
    Sonar Quality Gate is Passed, check URL: ${SONAR_URL}

    Junit Test-Cases:
    Total tests = $TEST_COUNTS
    Passed = ${TEST_COUNTS,var="pass"}
    Failed = ${TEST_COUNTS,var="fail"}
    Test cases Results:
    ${FAILED_TESTS}
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
             }
           }       
	    }   
     } 
     
     stage('Clean Workspace After build') {
            when { not { branch 'master' } }
            steps {
                sh "ls -all"
                cleanWs()
                sh "ls -all"
                sh "pwd"
            }
        }
  }
}