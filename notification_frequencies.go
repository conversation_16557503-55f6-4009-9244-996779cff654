package main

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

var (
	validNotifications = []string{"email"}
)

// handleGetNotificationFrequencies handles getting all Notification frequencies
func handleGetNotificationFrequencies(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)

	out, err := app.core.GetNotificationFrequencies(logger)
	if err != nil {
		return err
	}
	if len(out) == 0 {
		return c.JSON(http.StatusOK, okResp{models.NotificationFrequencies{}})
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpsertNotificationFrequency handles upsertion of Notification frequency
func handleUpsertNotificationFrequency(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.NotificationFrequencies{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		return err
	}

	if err := loadValidNotifications(logger); err != nil {
		return err
	}

	if err := validateNotificationFrequency(o, app); err != nil {
		return err
	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		o.CreatedBy = userID
		o.UpdatedBy = &userID
	}

	out, err := app.core.UpsertNotificationFrequency(o, logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// validateNotificationFrequency validates Notification frequency fields.
func validateNotificationFrequency(o models.NotificationFrequencies, app *App) error {
	if (o.FrequencyDays < models.FrequencyDaysMin) || (o.FrequencyDays > models.FrequencyDaysMax) {
		return errors.New(app.i18n.T("Notificationfrequencies.fieldInvalidFrequencyDays"))
	}

	if !app.manager.HasMessenger(o.Messenger) {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("Notificationfrequencies.fieldInvalidMessenger", "name", o.Messenger))
	}

	if ok := inArray(o.Notification, validNotifications); !ok {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("Notificationfrequencies.unsupportedNotification", "type", o.Notification))
	}

	if o.EnableFreqCap && (o.FrequencyValue < models.FrequencyValueMin || o.FrequencyValue > models.FrequencyValueMax) {
		return errors.New(app.i18n.T("Notificationfrequencies.fieldInvalidFrequencyValue"))
	}

	return nil
}

func loadValidNotifications(logger push.Logger) error {
	messengers, err := app.core.GetMessengerSettings(logger)
	if err != nil {
		return fmt.Errorf("failed to load messengers: %w", err)
	}

	validNotifications = []string{"email"}
	for _, m := range messengers {
		if m.Enabled {
			if m.Name == "fcm" {
				validNotifications = append(validNotifications, "screen_takeover", "custom_popup", "push_notification")
			} else {
				validNotifications = append(validNotifications, m.Name)
			}
		}
	}

	return nil
}
