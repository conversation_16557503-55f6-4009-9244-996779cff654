package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
)

type MediaFile struct {
	Filename string `db:"filename"`
	Thumb    string `db:"thumb"`
}

type S3Credentials struct {
	AccessKey string
	SecretKey string
	Region    string
	Bucket    string
}

const tempDir = "downloads"
const maxConcurrency = 10

func bucketDataMigration(c echo.Context) error {
	db := GetDB()

	creds, err := fetchS3Credentials(db)

	if err != nil {
		log.Fatalf("Failed to fetch credentials: %v", err)
	}

	if err := os.MkdirAll(tempDir, os.ModePerm); err != nil {
		log.Fatalf("Failed to create temp dir: %v", err)
	}

	mediaFiles, err := fetchDistinctFilenames(db)
	if err != nil {
		log.Fatalf("Failed to fetch filenames: %v", err)
	}

	publicS3Client := newPublicS3Client(creds)
	privateS3Client := newPrivateS3Client()

	var wg sync.WaitGroup
	fileChan := make(chan MediaFile, len(mediaFiles))

	// Spawn worker Goroutines
	for i := 0; i < maxConcurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for media := range fileChan {
				mediaCopy := media
				processMediaFile(creds, publicS3Client, privateS3Client, mediaCopy)
			}
		}()
	}

	// Send files to the channel
	for _, media := range mediaFiles {
		fileChan <- media
	}
	close(fileChan)

	// Wait for all workers to finish
	wg.Wait()

	return c.JSON(http.StatusOK, true)

}

func processMediaFile(creds *S3Credentials, publicS3Client, privateS3Client *s3.S3, media MediaFile) {
	if err := processFile(creds, publicS3Client, privateS3Client, media.Filename); err != nil {
		log.Printf("Failed to process %s: %v", media.Filename, err)
	}

	if media.Thumb != "" {
		if err := processFile(creds, publicS3Client, privateS3Client, media.Thumb); err != nil {
			log.Printf("Failed to process thumbnail %s: %v", media.Thumb, err)
		}
	}
}

func fetchS3Credentials(db *sqlx.DB) (*S3Credentials, error) {

	var creds S3Credentials
	var url string

	err := db.Get(&creds.AccessKey, "SELECT value FROM settings WHERE key = 'upload.s3.aws_access_key_id'")
	if err != nil {
		return nil, fmt.Errorf("error fetching access key: %w", err)
	}

	err = db.Get(&creds.SecretKey, "SELECT value FROM settings WHERE key = 'upload.s3.aws_secret_access_key'")
	if err != nil {
		return nil, fmt.Errorf("error fetching secret key: %w", err)
	}

	err = db.Get(&creds.Region, "SELECT value FROM settings WHERE key = 'upload.s3.aws_default_region'")
	if err != nil {
		return nil, fmt.Errorf("error fetching region: %w", err)
	}

	err = db.Get(&url, "SELECT value FROM settings WHERE key = 'upload.s3.url'")
	if err != nil {
		return nil, fmt.Errorf("error fetching URL: %w", err)
	}
	creds.AccessKey = strings.Trim(creds.AccessKey, "\"")
	creds.SecretKey = strings.Trim(creds.SecretKey, "\"")
	creds.Region = strings.Trim(creds.Region, "\"")
	url = strings.Trim(url, "\"")

	creds.Bucket = extractBucketName(url)

	return &creds, nil
}

func extractBucketName(url string) string {
	parts := strings.Split(url, "/")
	if len(parts) < 3 {
		return ""
	}
	domainParts := strings.Split(parts[2], ".")
	return domainParts[0]
}

func fetchDistinctFilenames(db *sqlx.DB) ([]MediaFile, error) {

	var mediaFiles []MediaFile
	err := db.Select(&mediaFiles, "SELECT DISTINCT filename, thumb FROM media")
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}

	return mediaFiles, nil
}

func newPublicS3Client(creds *S3Credentials) *s3.S3 {
	sess := session.Must(session.NewSession(&aws.Config{
		Region:      aws.String(creds.Region),
		Credentials: credentials.NewStaticCredentials(creds.AccessKey, creds.SecretKey, ""),
	}))
	return s3.New(sess)
}

func newPrivateS3Client() *s3.S3 {
	sess := session.Must(session.NewSession(&aws.Config{
		Region: aws.String(config.ServerConfig.Region),
	}))
	return s3.New(sess)
}

func processFile(creds *S3Credentials, publicS3Client, privateS3Client *s3.S3, filename string) error {
	publicFilePath := fmt.Sprintf("com/%s", filename)
	localFilePath := fmt.Sprintf("%s/%s", tempDir, filename)

	err := downloadFromS3(publicS3Client, creds.Bucket, publicFilePath, localFilePath)

	if err != nil {
		if strings.Contains(err.Error(), "NoSuchKey") {
			log.Printf("Skipping missing file: %s", filename)
			return nil
		}
		return fmt.Errorf("failed to download %s: %w", filename, err)
	}

	cleanFilename := strings.TrimPrefix(publicFilePath, "com/")
	if err := uploadToS3(privateS3Client, config.ServerConfig.BucketName, cleanFilename, localFilePath); err != nil {
		return fmt.Errorf("failed to upload %s: %w", filename, err)
	}

	os.Remove(localFilePath)
	return nil
}

func downloadFromS3(s3Client *s3.S3, bucket, filename, destPath string) error {
	out, err := s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(filename),
	})
	if err != nil {
		return fmt.Errorf("S3 download error: %w", err)
	}
	defer out.Body.Close()

	file, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("error creating local file: %w", err)
	}
	defer file.Close()

	_, err = io.Copy(file, out.Body)
	if err != nil {
		return fmt.Errorf("error writing file: %w", err)
	}

	return nil
}

func uploadToS3(s3Client *s3.S3, bucket, filename, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("error opening file: %w", err)
	}
	defer file.Close()

	contentType := "application/octet-stream"
	if strings.HasSuffix(strings.ToLower(filePath), ".png") {
		contentType = "image/png"
	} else if strings.HasSuffix(strings.ToLower(filePath), ".jpg") || strings.HasSuffix(strings.ToLower(filePath), ".jpeg") {
		contentType = "image/jpeg"
	} else if strings.HasSuffix(strings.ToLower(filePath), ".gif") {
		contentType = "image/gif"
	}

	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return fmt.Errorf("S3 upload error: %w", err)
	}

	return nil
}
