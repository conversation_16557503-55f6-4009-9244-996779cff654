env:
  - GO111MODULE=on
  - CGO_ENABLED=0

before:
  hooks:
    - make build-frontend

builds:
  - binary: listmonk
    main: ./cmd
    goos:
      - windows
      - darwin
      - linux
      - freebsd
      - openbsd
      - netbsd
    goarch:
      - amd64
    ldflags:
      - -s -w -X "main.buildString={{ .Tag }} ({{ .ShortCommit }} {{ .Date }})" -X "main.versionString={{ .Tag }}"

    hooks:
      # stuff executables with static assets.
      post: make pack-bin BIN={{ .Path }}

archives:
  - format: tar.gz
    files:
      - README.md
      - LICENSE

dockers:
  -
    goos: linux
    goarch: amd64
    ids:
    - listmonk
    image_templates:
    - "listmonk/listmonk:latest"
    - "listmonk/listmonk:{{ .Tag }}"
    dockerfile: Dockerfile
    extra_files:
    - config.toml.sample
    - config-demo.toml
