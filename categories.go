package main

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
	null "gopkg.in/volatiletech/null.v6"
)

// handleGetCategories retrieves categories with additional metadata like subscriber counts. This may be slow.
func handleGetCategories(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
		pg     = app.paginator.NewFromURL(c.Request().URL.Query())

		query         = strings.TrimSpace(c.FormValue("query"))
		orderBy       = c.FormValue("order_by")
		order         = c.FormValue("order")
		categoryID, _ = strconv.Atoi(c.Param("id"))

		out models.PageResults
	)

	// Fetch one category.
	single := false
	if categoryID > 0 {
		single = true
	}

	if single {
		out, err := app.core.GetCategory(categoryID, logger)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, okResp{out})
	}

	// Full category query.
	res, total, err := app.core.QueryCategory(query, orderBy, order, pg.Offset, pg.Limit, logger)
	if err != nil {
		return err
	}

	if single && len(res) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.categories}"))
	}

	if single {
		return c.JSON(http.StatusOK, okResp{res[0]})
	}

	out.Query = query
	out.Results = res
	out.Total = total
	out.Page = pg.Page
	out.PerPage = pg.PerPage

	return c.JSON(http.StatusOK, okResp{out})
}

// handleCreateCategories handles category creation.
func handleCreateCategory(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		l      = models.Category{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&l); err != nil {
		return err
	}

	// Serialize FrequencyDetails to JSON before updating.
	if l.FrequencyDetails != nil {
		freqDetailsJSON, err := json.Marshal(l.FrequencyDetails)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "failed to serialize FrequencyDetails")
		}
		l.FrequencyDetailsJSON = json.RawMessage(freqDetailsJSON)
	} else {
		l.FrequencyDetailsJSON = json.RawMessage("{}")
	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		l.CreatedBy = null.StringFrom(userID)
	}

	out, err := app.core.CreateCategory(l, logger)
	if err != nil {
		return err
	}
	err = app.manager.AddCategoryMap(out.ID)

	if err != nil {
		return err
	}
	PublishStateChangeEvents(out.ID, "categoryUpdated", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))

	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpdateCategory handles category modification.
func handleUpdateCategory(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var input map[string]interface{}
	if err := json.NewDecoder(c.Request().Body).Decode(&input); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid JSON")
	}

	existing, err := app.core.GetCategory(id, logger)
	if err != nil {
		return err
	}
	if v, ok := input["name"].(string); ok {
		existing.Name = v
	}
	if v, ok := input["description"].(string); ok {
		existing.Description = null.StringFrom(v)
	}
	if v, ok := input["isDefault"].(bool); ok {
		existing.IsDefault = v
	}

	if opts, err := extractNotificationOptions(input, "isToggleable"); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid isToggleable structure")
	} else if opts != nil {
		existing.IsToggleable = *opts
	}
	if opts, err := extractNotificationOptions(input, "isVisibleToMerchant"); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid isVisibleToMerchant structure")
	} else if opts != nil {
		existing.IsVisibleToMerchant = *opts
	}
	if opts, err := extractNotificationOptions(input, "notificationDefaults"); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid notificationDefaults structure")
	} else if opts != nil {
		existing.NotificationDefaults = *opts
	}

	if freqRaw, ok := input["frequency_details"]; ok {
		freqBytes, _ := json.Marshal(freqRaw)
		var freq models.FrequencyDetails
		if err := json.Unmarshal(freqBytes, &freq); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "invalid frequency_details format")
		}
		existing.FrequencyDetails = freq
		existing.FrequencyDetailsJSON = json.RawMessage(freqBytes)
	}

	if existing.FrequencyDetails != nil {
		freqBytes, err := json.Marshal(existing.FrequencyDetails)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "failed to serialize FrequencyDetails")
		}
		existing.FrequencyDetailsJSON = json.RawMessage(freqBytes)
	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		existing.UpdatedBy = null.StringFrom(userID)
	}
	out, err := app.core.UpdateCategory(id, existing, logger)
	if err != nil {
		return err
	}

	err = app.manager.AddCategoryMap(id)

	if err != nil {
		return err
	}
	PublishStateChangeEvents(id, "categoryUpdated", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))
	return c.JSON(http.StatusOK, okResp{out})
}

func extractNotificationOptions(input map[string]interface{}, key string) (*models.NotificationOptions, error) {
	if v, ok := input[key]; ok {
		raw, err := json.Marshal(v)
		if err != nil {
			return nil, err
		}
		var opts models.NotificationOptions
		if err := json.Unmarshal(raw, &opts); err != nil {
			return nil, err
		}
		return &opts, nil
	}
	return nil, nil
}

// handleDeleteCategories handles category deletion, either a single one (ID in the URI), or a category.
func handleDeleteCategory(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.ParseInt(c.Param("id"), 10, 64)
		ids    []int
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 && len(ids) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	if id > 0 {
		ids = append(ids, int(id))
	}

	var updatedBy string
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		updatedBy = userID
	}

	if err := app.core.DeleteCategories(ids, logger, updatedBy); err != nil {
		return err
	}

	for _, catId := range ids {
		app.manager.DeleteCategoryFromMap(catId)
		PublishStateChangeEvents(catId, "categoryDeleted", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))
	}

	return c.JSON(http.StatusOK, okResp{true})
}
