package main

import (
	"bytes"
	"context"

	"github.com/knadh/listmonk/internal/manager"
	"github.com/knadh/listmonk/tracer"
	push "github.com/phuslu/log"
)

const (
	notifTplImport       = "import-status"
	notifTplCampaign     = "campaign-status"
	notifSubscriberOptin = "subscriber-optin"
	notifSubscriberData  = "subscriber-data"
)

// notifData represents params commonly used across different notification
// templates.
type notifData struct {
	RootURL string
	LogoURL string
}

// sendNotification sends out an e-mail notification to admins.
func (app *App) sendNotification(toEmails []string, subject, tplName string, data interface{}, ctx context.Context) error {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	if len(toEmails) == 0 {
		return nil
	}

	var b bytes.Buffer
	if err := app.notifTpls.tpls.ExecuteTemplate(&b, tplName, data); err != nil {
		logger.Error().Msgf("error compiling notification template '%s': %v", tplName, err)
		return err
	}

	gt, err := app.manager.GetGateway("email_default")

	if err != nil {
		return err
	}
	m := manager.Message{}
	m.ContentType = app.notifTpls.contentType
	m.From = app.constants.FromEmail
	if gt != nil {
		m.Method = gt.Method.String
	}
	m.Subscriber.Name = toEmails[0]
	m.Subscriber.Type = "customer"
	m.To = toEmails
	m.Subject = subject
	m.Body = b.Bytes()
	m.Messenger = emailMsgr
	m.Ctx = ctx
	if err := app.manager.PushMessage(m); err != nil {
		logger.Error().Msgf("error sending admin notification (%s): %v", subject, err)
		return err
	}
	return nil
}
