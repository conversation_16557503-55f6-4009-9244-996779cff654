package main

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"html/template"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"syscall"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/types"
	"github.com/knadh/goyesql/v2"
	goyesqlx "github.com/knadh/goyesql/v2/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/koanf/maps"
	jsonK "github.com/knadh/koanf/parsers/json"
	"github.com/knadh/koanf/parsers/toml"
	"github.com/knadh/koanf/providers/confmap"
	"github.com/knadh/koanf/providers/file"
	"github.com/knadh/koanf/providers/posflag"
	"github.com/knadh/koanf/providers/rawbytes"
	"github.com/knadh/listmonk/authprovider"
	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/internal/bounce"
	"github.com/knadh/listmonk/internal/bounce/mailbox"
	"github.com/knadh/listmonk/internal/i18n"
	"github.com/knadh/listmonk/internal/manager"
	"github.com/knadh/listmonk/internal/media"
	"github.com/knadh/listmonk/internal/media/providers/filesystem"
	"github.com/knadh/listmonk/internal/media/providers/s3"
	"github.com/knadh/listmonk/internal/messenger"
	"github.com/knadh/listmonk/internal/messenger/email"
	"github.com/knadh/listmonk/internal/messenger/postback"
	"github.com/knadh/listmonk/internal/subimporter"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/knadh/stuffbin"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	"github.com/redis/go-redis/v9"
	flag "github.com/spf13/pflag"
)

const (
	queryFilePath = "queries.sql"

	// Root URI of the admin frontend.
	adminRoot = "/admin"
)

// constants contains static, constant config values required by the app.
type constants struct {
	SiteName              string   `koanf:"site_name"`
	RootURL               string   `koanf:"root_url"`
	LogoURL               string   `koanf:"logo_url"`
	FaviconURL            string   `koanf:"favicon_url"`
	FromEmail             string   `koanf:"from_email"`
	NotifyEmails          []string `koanf:"notify_emails"`
	EnablePublicSubPage   bool     `koanf:"enable_public_subscription_page"`
	EnablePublicArchive   bool     `koanf:"enable_public_archive"`
	SendOptinConfirmation bool     `koanf:"send_optin_confirmation"`
	Lang                  string   `koanf:"lang"`
	DBBatchSize           int      `koanf:"batch_size"`
	Privacy               struct {
		IndividualTracking bool            `koanf:"individual_tracking"`
		AllowPreferences   bool            `koanf:"allow_preferences"`
		AllowBlocklist     bool            `koanf:"allow_blocklist"`
		AllowExport        bool            `koanf:"allow_export"`
		AllowWipe          bool            `koanf:"allow_wipe"`
		Exportable         map[string]bool `koanf:"-"`
		DomainBlocklist    map[string]bool `koanf:"-"`
	} `koanf:"privacy"`
	AdminUsername []byte `koanf:"admin_username"`
	AdminPassword []byte `koanf:"admin_password"`

	Appearance struct {
		AdminCSS  []byte `koanf:"admin.custom_css"`
		AdminJS   []byte `koanf:"admin.custom_js"`
		PublicCSS []byte `koanf:"public.custom_css"`
		PublicJS  []byte `koanf:"public.custom_js"`
	}

	UnsubURL      string
	LinkTrackURL  string
	ViewTrackURL  string
	OptinURL      string
	MessageURL    string
	ArchiveURL    string
	MediaProvider string

	BounceWebhooksEnabled bool
	BounceSESEnabled      bool
	BounceSendgridEnabled bool
}

type notifTpls struct {
	tpls        *template.Template
	contentType string
}

func initFlags() {
	f := flag.NewFlagSet("config", flag.ContinueOnError)
	f.Usage = func() {
		// Register --help handler.
		lo.Info().Msg(f.FlagUsages())
		os.Exit(0)
	}

	// Register the commandline flags.
	f.StringSlice("config", []string{"/home/<USER>/work/go/listmonk/dev/config.toml"},
		"path to one or more config files (will be merged in order)")
	f.Bool("install", false, "setup database (first time)")
	f.Bool("idempotent", false, "make --install run only if the database isn't already setup")
	f.Bool("upgrade", false, "upgrade database to the current version")
	f.Bool("version", false, "show current version of the build")
	f.Bool("new-config", false, "generate sample config file")
	f.String("static-dir", "", "(optional) path to directory with static files")
	f.String("i18n-dir", "", "(optional) path to directory with i18n language files")
	f.Bool("yes", false, "assume 'yes' to prompts during --install/upgrade")
	f.Bool("passive", false, "run in passive mode where campaigns are not processed")
	if err := f.Parse(os.Args[1:]); err != nil {
		lo.Fatal().Msgf("error loading flags: %v", err)
	}

	if err := ko.Load(posflag.Provider(f, ".", ko), nil); err != nil {
		lo.Fatal().Msgf("error loading config: %v", err)
	}
}

// initConfigFiles loads the given config files into the koanf instance.
func initConfigFiles(files []string, ko *koanf.Koanf) {
	ko.Load(confmap.Provider(map[string]interface{}{
		"serverConfig.natsMaxAge":                "365d",
		"serverConfig.natsMaxBytes":              10737418240,
		"serverConfig.natsMaxMessages":           -1,
		"serverConfig.natsMaxMessageSize":        -1,
		"serverConfig.natsDuplicateWindow":       "2m",
		"serverConfig.natsMaxMessagesPerSubject": -1,
		"serverConfig.natsStreamUpdateRequired":  true,
	}, "."), nil)
	for _, f := range files {
		lo.Info().Msgf("reading config: %s", f)
		if err := ko.Load(file.Provider(f), toml.Parser()); err != nil {
			if os.IsNotExist(err) {
				lo.Fatal().Msg("config file not found. If there isn't one yet, run --new-config to generate one.")
			}
			lo.Fatal().Msgf("error loading config from file: %v.", err)
		}
	}
}

// initFileSystem initializes the stuffbin FileSystem to provide
// access to bundled static assets to the app.
func initFS(appDir, frontendDir, staticDir, i18nDir string) stuffbin.FileSystem {
	var (
		// stuffbin real_path:virtual_alias paths to map local assets on disk
		// when there an embedded filestystem is not found.

		// These paths are joined with appDir.
		appFiles = []string{
			"./config.toml.sample:config.toml.sample",
			"./queries.sql:queries.sql",
			"./schema.sql:schema.sql",
			"./normalized_changes.sql:normalized_changes.sql",
		}

		frontendFiles = []string{
			// Admin frontend's static assets accessible at /admin/* during runtime.
			// These paths are sourced from frontendDir.
			"./:/admin",
		}

		staticFiles = []string{
			// These paths are joined with staticDir.
			"./email-templates:static/email-templates",
			"./public:/public",
		}

		i18nFiles = []string{
			// These paths are joined with i18nDir.
			"./:/i18n",
		}
	)

	// Get the executable's path.
	path, err := os.Executable()
	if err != nil {
		lo.Fatal().Msgf("error getting executable path: %v", err)
	}

	// Load embedded files in the executable.
	hasEmbed := true
	fs, err := stuffbin.UnStuff(path)
	if err != nil {
		hasEmbed = false

		// Running in local mode. Load local assets into
		// the in-memory stuffbin.FileSystem.
		lo.Error().Msgf("unable to initialize embedded filesystem (%v). Using local filesystem", err)

		fs, err = stuffbin.NewLocalFS("/")
		if err != nil {
			lo.Fatal().Msgf("failed to initialize local file for assets: %v", err)
		}
	}

	// If the embed failed, load app and frontend files from the compile-time paths.
	files := []string{}
	if !hasEmbed {
		files = append(files, joinFSPaths(appDir, appFiles)...)
		files = append(files, joinFSPaths(frontendDir, frontendFiles)...)
	}

	// Irrespective of the embeds, if there are user specified static or i18n paths,
	// load files from there and override default files (embedded or picked up from CWD).
	if !hasEmbed || i18nDir != "" {
		if i18nDir == "" {
			// Default dir in cwd.
			i18nDir = "i18n"
		}
		lo.Info().Msgf("loading i18n files from: %v", i18nDir)
		files = append(files, joinFSPaths(i18nDir, i18nFiles)...)
	}

	if !hasEmbed || staticDir != "" {
		if staticDir == "" {
			// Default dir in cwd.
			staticDir = "static"
		}
		lo.Info().Msgf("loading static files from: %v", staticDir)
		files = append(files, joinFSPaths(staticDir, staticFiles)...)
	}

	// No additional files to load.
	if len(files) == 0 {
		return fs
	}

	// Load files from disk and overlay into the FS.
	fStatic, err := stuffbin.NewLocalFS("/", files...)
	if err != nil {
		lo.Fatal().Msgf("failed reading static files from disk: '%s': %v", staticDir, err)
	}

	if err := fs.Merge(fStatic); err != nil {
		lo.Fatal().Msgf("error merging static files: '%s': %v", staticDir, err)
	}

	return fs
}

func initConfigProperties() models.Config {
	var c models.Config
	var f struct {
		NatsUrl                   string `koanf:"natsUrl"`
		ServerUrl                 string `koanf:"serverUrl"`
		NatsReplica               int    `koanf:"natsReplica"`
		IsLocal                   bool   `koanf:"isLocal"`
		RedisUrl                  string `koanf:"redisHost"`
		RedisPoolSize             int    `koanf:"redisPoolSize"`
		FirebaseJsonPath          string `koanf:"firebaseFilePath"`
		KeyVaultUrl               string `koanf:"keyVaultUrl"`
		TestEnv                   bool   `koanf:"testEnv"`
		ChangeLogEnabled          bool   `koanf:"changeLogEnabled"`
		AesAccessKey              string `koanf:"aesAccessKey"`
		AesSecretKey              string `koanf:"aesSecretKey"`
		AesKeyId                  string `koanf:"aesKeyId"`
		UserTerminalInfoApiUrl    string `koanf:"userTerminalInfoApiUrl"`
		SoundboxServiceApiUrl     string `koanf:"soundboxServiceApiUrl"`
		OneAppAuthUrl             string `koanf:"oneAppAuthUrl"`
		SegmentServiceUrl         string `koanf:"segmentServiceUrl"`
		AwsAccessKey              string `koanf:"awsAccessKey"`
		AwsSecretKey              string `koanf:"awsSecretKey"`
		BucketName                string `koanf:"bucketName"`
		BucketSubFolder           string `koanf:"bucketSubFolder"`
		Region                    string `koanf:"region"`
		NatsMaxAge                string `koanf:"natsMaxAge"`
		NatsMaxBytes              int64  `koanf:"natsMaxBytes"`
		NatsMaxMessages           int64  `koanf:"natsMaxMessages"`
		NatsMaxMessageSize        int32  `koanf:"natsMaxMessageSize"`
		NatsDuplicateWindow       string `koanf:"natsDuplicateWindow"`
		NatsMaxMessagesPerSubject int64  `koanf:"natsMaxMessagesPerSubject"`
		NatsStreamUpdateRequired  bool   `koanf:"natsStreamUpdateRequired"`
		SkipSSLCheck              bool   `koanf:"skipSSLCheck"`
		MaxEmailAttachmentSize    int64  `koanf:"maxEmailAttachmentSize"`
		SegmentMembershipPageSize int    `koanf:"segmentMembershipPageSize"`
		BatchSize                 *int   `koanf:"batchSize"`
		MqttEnabled               bool   `koanf:"mqttEnabled"`
		MaxConcurrentCampaigns    int    `koanf:"maxConcurrentCampaigns"`
		HealthCheckBasePath       string `koanf:"healthCheckBasePath"`
		RedisCampaignTTLHours     int    `koanf:"redisCampaignTTLHours"`
		SecondaryRedisUrl         string `koanf:"secondaryRedisHost"`
		SecondaryRedisPoolSize    int    `koanf:"secondaryRedisPoolSize"`
		CDNUrl                    string `koanf:"cdnUrl"`
		BucketType                string `koanf:"bucketType"`
		MaxVideoSizeInMb          int64  `koanf:"maxVideoSizeInMb"`
		MaxAudioSizeInMb          int64  `koanf:"maxAudioSizeInMb"`
	}
	if err := ko.Unmarshal("serverConfig", &f); err != nil {
		lo.Fatal().Msgf("error loading firebase config: %v", err)
	}
	c.ServerConfig.NatsUrl = f.NatsUrl
	c.ServerConfig.NatsReplica = f.NatsReplica
	c.ServerConfig.IsLocal = f.IsLocal
	c.ServerConfig.RedisUrl = f.RedisUrl
	c.ServerConfig.FirebaseJsonPath = f.FirebaseJsonPath
	c.ServerConfig.TestEnv = f.TestEnv
	c.ServerConfig.RedisPoolSize = f.RedisPoolSize
	c.ServerConfig.ChangeLogEnabled = f.ChangeLogEnabled
	c.ServerConfig.AesAccessKey = f.AesAccessKey
	c.ServerConfig.AesSecretKey = f.AesSecretKey
	c.ServerConfig.AesKeyId = f.AesKeyId
	c.ServerConfig.UserTerminalInfoApiUrl = f.UserTerminalInfoApiUrl
	c.ServerConfig.SegmentServiceUrl = f.SegmentServiceUrl
	c.ServerConfig.OneAppAuthUrl = f.OneAppAuthUrl
	c.ServerConfig.AwsAccessKey = f.AwsAccessKey
	c.ServerConfig.AwsSecretKey = f.AwsSecretKey
	c.ServerConfig.SoundboxServiceApiUrl = f.SoundboxServiceApiUrl
	c.ServerConfig.KeyVaultUrl = f.KeyVaultUrl
	c.ServerConfig.SecondaryRedisUrl = f.SecondaryRedisUrl
	c.ServerConfig.SecondaryRedisPoolSize = f.SecondaryRedisPoolSize
	c.ServerConfig.HealthCheckBasePath = f.HealthCheckBasePath
	if f.Region == "" {
		c.ServerConfig.Region = "ap-south-1"
	} else {
		c.ServerConfig.Region = f.Region
	}

	if f.MaxConcurrentCampaigns != 0 {
		c.ServerConfig.MaxConcurrentCampaigns = f.MaxConcurrentCampaigns
	} else {
		c.ServerConfig.MaxConcurrentCampaigns = maxConcurrentCampaigns
	}
	if f.RedisCampaignTTLHours != 0 {
		c.ServerConfig.RedisCampaignTTLHours = f.RedisCampaignTTLHours
	} else {
		c.ServerConfig.RedisCampaignTTLHours = redisCampaignTTLHours
	}
	c.ServerConfig.BucketName = f.BucketName
	c.ServerConfig.BucketSubFolder = f.BucketSubFolder
	c.ServerConfig.NatsMaxAge = f.NatsMaxAge
	c.ServerConfig.NatsMaxBytes = f.NatsMaxBytes
	c.ServerConfig.NatsMaxMessages = f.NatsMaxMessages
	c.ServerConfig.NatsMaxMessageSize = f.NatsMaxMessageSize
	c.ServerConfig.NatsDuplicateWindow = f.NatsDuplicateWindow
	c.ServerConfig.NatsMaxMessagesPerSubject = f.NatsMaxMessagesPerSubject
	c.ServerConfig.NatsStreamUpdateRequired = f.NatsStreamUpdateRequired
	c.ServerConfig.MqttEnabled = f.MqttEnabled
	if f.BatchSize == nil {
		c.ServerConfig.BatchSize = 400
	} else {
		c.ServerConfig.BatchSize = *f.BatchSize
	}
	lo.Info().Msgf("skip ssl is marked as %v", f.SkipSSLCheck)
	c.ServerConfig.SkipSSLCheck = f.SkipSSLCheck

	if f.MaxEmailAttachmentSize != 0 {
		lo.Info().Msgf("maxEmailAttachmentSize is marked as %v", f.MaxEmailAttachmentSize)
		c.ServerConfig.MaxEmailAttachmentSize = f.MaxEmailAttachmentSize
	} else {
		c.ServerConfig.MaxEmailAttachmentSize = int64(26214400)
	}

	if f.SegmentMembershipPageSize != 0 {
		c.ServerConfig.SegmentMembershipPageSize = f.SegmentMembershipPageSize
	} else {
		c.ServerConfig.SegmentMembershipPageSize = 500
	}
	c.ServerConfig.CDNUrl = f.CDNUrl
	if f.BucketType == "" {
		c.ServerConfig.BucketType = "public"
	} else {
		c.ServerConfig.BucketType = f.BucketType
	}
	if c.ServerConfig.BucketType == "public" {
		c.ServerConfig.PublicUrl = fmt.Sprintf("https://%s.s3.%s.amazonaws.com", c.ServerConfig.BucketName, c.ServerConfig.Region)
	}

	if f.MaxVideoSizeInMb != 0 {
		lo.Info().Msgf("maxVideoSizeInMb is %v", f.MaxVideoSizeInMb)
		c.ServerConfig.MaxVideoSizeInMb = f.MaxVideoSizeInMb
	} else {
		c.ServerConfig.MaxVideoSizeInMb = maxVideoSizeInMb
	}

	if f.MaxAudioSizeInMb != 0 {
		lo.Info().Msgf("maxAudioSizeInMb is %v", f.MaxAudioSizeInMb)
		c.ServerConfig.MaxAudioSizeInMb = f.MaxAudioSizeInMb
	} else {
		c.ServerConfig.MaxAudioSizeInMb = maxAudioSizeInMb
	}

	return c
}

// Added by Deepali for redis initialization
func initRedis() {
	lo.Info().Msgf("Initializing redis!")
	var secondaryClient *redis.ClusterClient

	if !config.ServerConfig.IsLocal {
		primaryClient := utils.InitializeClusterRedis(config.ServerConfig.RedisUrl, config.ServerConfig.RedisPoolSize)
		if config.ServerConfig.SecondaryRedisUrl != "" {
			secondaryClient = utils.InitializeSecondaryClusterRedis(config.ServerConfig.SecondaryRedisUrl, config.ServerConfig.SecondaryRedisPoolSize)
		} else {
			secondaryClient = utils.SetSecondaryClient(primaryClient)
			lo.Info().Msgf("Secondary Redis client set to primary Redis connection.")
		}
		fmt.Println(secondaryClient)
	} else {
		localredisClient := utils.InitalizeLocalRedis(config.ServerConfig.RedisUrl)
		lo.Info().Msgf("Local redis connected sucessfully!")
		loadRedisLuaScriptForFreqCapLocal(localredisClient)
	}
	//loadRedisLuaScriptForFreqCap(secondaryClient)
}

func initFirebase() {
	lo.Info().Msg("Setting up firebase!")
	external.SetupFirebase(config.ServerConfig.FirebaseJsonPath, config.ServerConfig.SkipSSLCheck)
}

//Ended by Deepali for redis initialization

// initDB initializes the main DB connection pool and parse and loads the app's
// SQL queries into a prepared query map.
func initDB() *sqlx.DB {
	var c struct {
		Host         string        `koanf:"host"`
		Port         int           `koanf:"port"`
		User         string        `koanf:"user"`
		Password     string        `koanf:"password"`
		DBName       string        `koanf:"database"`
		SSLMode      string        `koanf:"ssl_mode"`
		Params       string        `koanf:"params"`
		MaxOpen      int           `koanf:"max_open"`
		MaxIdle      int           `koanf:"max_idle"`
		MaxLifetime  time.Duration `koanf:"max_lifetime"`
		Schema       string        `koanf:"schema"`
		QueryTimeout int           `koanf:"query_timeout"`
	}
	if err := ko.Unmarshal("db", &c); err != nil {
		lo.Fatal().Msgf("error loading db config: %v", err)
	}

	if c.QueryTimeout == 0 {
		c.QueryTimeout = queryTimeout
	}

	lo.Info().Msgf("connecting to db: %s:%d/%s", c.Host, c.Port, c.DBName)
	db, err := sqlx.Connect("postgres",
		fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s %s statement_timeout=%d", c.Host, c.Port, fetchSecrets(c.User), fetchSecrets(c.Password), c.DBName, c.SSLMode, c.Params, c.QueryTimeout))

	if err != nil {
		lo.Fatal().Msgf("error connecting to DB: %v", err)
	}

	db.SetMaxOpenConns(c.MaxOpen)
	db.SetMaxIdleConns(c.MaxIdle)
	db.SetConnMaxLifetime(c.MaxLifetime)

	return db
}

//function to fetch secrets if used

func fetchSecrets(value string) string {

	re := regexp.MustCompile(`\{(.+?)\}`)
	result := re.FindStringSubmatch(value)
	if len(result) > 1 {
		return os.Getenv(result[1])
	} else {
		return value
	}
}

// readQueries reads named SQL queries from the SQL queries file into a query map.
func readQueries(sqlFile string, db *sqlx.DB, fs stuffbin.FileSystem) goyesql.Queries {
	// Load SQL queries.
	qB, err := fs.Read(sqlFile)
	if err != nil {
		lo.Fatal().Msgf("error reading SQL file %s: %v", sqlFile, err)
	}
	qMap, err := goyesql.ParseBytes(qB)
	if err != nil {
		lo.Fatal().Msgf("error parsing SQL queries: %v", err)
	}

	return qMap
}

// prepareQueries queries prepares a query map and returns a *Queries
func prepareQueries(qMap goyesql.Queries, db *sqlx.DB, ko *koanf.Koanf) *models.Queries {
	var (
		countQuery = "get-campaign-analytics-counts"
		linkSel    = "*"
	)
	if ko.Bool("privacy.individual_tracking") {
		countQuery = "get-campaign-analytics-unique-counts"
		linkSel = "DISTINCT subscriber_id"
	}

	// These don't exist in the SQL file but are in the queries struct to be prepared.
	qMap["get-campaign-view-counts"] = &goyesql.Query{
		Query: fmt.Sprintf(qMap[countQuery].Query, "campaign_views"),
		Tags:  map[string]string{"name": "get-campaign-view-counts"},
	}
	qMap["get-campaign-click-counts"] = &goyesql.Query{
		Query: fmt.Sprintf(qMap[countQuery].Query, "link_clicks"),
		Tags:  map[string]string{"name": "get-campaign-click-counts"},
	}
	qMap["get-campaign-link-counts"].Query = fmt.Sprintf(qMap["get-campaign-link-counts"].Query, linkSel)

	// Scan and prepare all queries.
	var q models.Queries
	if err := goyesqlx.ScanToStruct(&q, qMap, db.Unsafe()); err != nil {
		lo.Fatal().Msgf("error preparing SQL queries: %v", err)
	}

	return &q
}

// initSettings loads settings from the DB into the given Koanf map.
func initSettings(query string, db *sqlx.DB, ko *koanf.Koanf) {
	var s types.JSONText
	if err := db.Get(&s, query); err != nil {
		msg := err.Error()
		if err, ok := err.(*pq.Error); ok {
			if err.Detail != "" {
				msg = fmt.Sprintf("%s. %s", err, err.Detail)
			}
		}

		lo.Fatal().Msgf("error reading settings from DB: %s", msg)
	}

	// Setting keys are dot separated, eg: app.favicon_url. Unflatten them into
	// nested maps {app: {favicon_url}}.
	var out map[string]interface{}
	if err := json.Unmarshal(s, &out); err != nil {
		lo.Fatal().Msgf("error unmarshalling settings from DB: %v", err)
	}
	if err := ko.Load(confmap.Provider(out, "."), nil); err != nil {
		lo.Fatal().Msgf("error parsing settings from DB: %v", err)
	}
}

func initConstants() *constants {
	// Read constants.
	var c constants
	if err := ko.Unmarshal("app", &c); err != nil {
		lo.Fatal().Msgf("error loading app config: %v", err)
	}
	if err := ko.Unmarshal("privacy", &c.Privacy); err != nil {
		lo.Fatal().Msgf("error loading app.privacy config: %v", err)
	}
	if err := ko.UnmarshalWithConf("appearance", &c.Appearance, koanf.UnmarshalConf{FlatPaths: true}); err != nil {
		lo.Fatal().Msgf("error loading app.appearance config: %v", err)
	}

	c.RootURL = strings.TrimRight(c.RootURL, "/")
	c.Lang = ko.String("app.lang")
	c.Privacy.Exportable = maps.StringSliceToLookupMap(ko.Strings("privacy.exportable"))
	c.MediaProvider = ko.String("upload.provider")
	c.Privacy.DomainBlocklist = maps.StringSliceToLookupMap(ko.Strings("privacy.domain_blocklist"))

	// Static URLS.
	// url.com/subscription/{campaign_uuid}/{subscriber_uuid}
	c.UnsubURL = fmt.Sprintf("%s/subscription/%%s/%%s", c.RootURL)

	// url.com/subscription/optin/{subscriber_uuid}
	c.OptinURL = fmt.Sprintf("%s/subscription/optin/%%s?%%s", c.RootURL)

	// url.com/link/{campaign_uuid}/{subscriber_uuid}/{link_uuid}
	c.LinkTrackURL = fmt.Sprintf("%s/link/%%s/%%s/%%s", c.RootURL)

	// url.com/link/{campaign_uuid}/{subscriber_uuid}
	c.MessageURL = fmt.Sprintf("%s/campaign/%%s/%%s", c.RootURL)

	// url.com/archive
	c.ArchiveURL = c.RootURL + "/archive"

	// url.com/campaign/{campaign_uuid}/{subscriber_uuid}/px.png
	c.ViewTrackURL = fmt.Sprintf("%s/campaign/%%s/%%s/px.png", c.RootURL)

	c.BounceWebhooksEnabled = ko.Bool("bounce.webhooks_enabled")
	c.BounceSESEnabled = ko.Bool("bounce.ses_enabled")
	c.BounceSendgridEnabled = ko.Bool("bounce.sendgrid_enabled")
	return &c
}

// initI18n initializes a new i18n instance with the selected language map
// loaded from the filesystem. English is a loaded first as the default map
// and then the selected language is loaded on top of it so that if there are
// missing translations in it, the default English translations show up.
func initI18n(lang string, fs stuffbin.FileSystem) *i18n.I18n {
	i, ok, err := getI18nLang(lang, fs)
	if err != nil {
		if ok {
			lo.Error().Msgf("error occured %v", err)
		} else {
			lo.Fatal().Msgf("error occured %v", err)
		}
	}
	return i
}

// initCampaignManager initializes the campaign manager.
func initCampaignManager(q *models.Queries, cs *constants, app *App) *manager.Manager {
	campNotifCB := func(subject string, data interface{}, ctx context.Context) error {
		return app.sendNotification(cs.NotifyEmails, subject, notifTplCampaign, data, ctx)
	}

	if ko.Int("app.concurrency") < 1 {
		lo.Fatal().Msg("app.concurrency should be at least 1")
	}
	if ko.Int("app.message_rate") < 1 {
		lo.Fatal().Msg("app.message_rate should be at least 1")
	}

	if ko.Bool("passive") {
		lo.Info().Msg("running in passive mode. won't process campaigns.")
	}

	return manager.New(manager.Config{
		BatchSize:             ko.Int("app.batch_size"),
		Concurrency:           ko.Int("app.concurrency"),
		MessageRate:           ko.Int("app.message_rate"),
		MaxSendErrors:         ko.Int("app.max_send_errors"),
		FromEmail:             cs.FromEmail,
		IndividualTracking:    ko.Bool("privacy.individual_tracking"),
		UnsubURL:              cs.UnsubURL,
		OptinURL:              cs.OptinURL,
		LinkTrackURL:          cs.LinkTrackURL,
		ViewTrackURL:          cs.ViewTrackURL,
		MessageURL:            cs.MessageURL,
		ArchiveURL:            cs.ArchiveURL,
		UnsubHeader:           ko.Bool("privacy.unsubscribe_header"),
		SlidingWindow:         ko.Bool("app.message_sliding_window"),
		SlidingWindowDuration: ko.Duration("app.message_sliding_window_duration"),
		SlidingWindowRate:     ko.Int("app.message_sliding_window_rate"),
		ScanInterval:          time.Second * 5,
		ScanCampaigns:         !ko.Bool("passive"),
	}, newManagerStore(q), campNotifCB, app.i18n, lo1, &lo, ko.String("serverConfig.segmentServiceUrl"), ko.String("serverConfig.oneAppAuthUrl"))
}

func initTxTemplates(m *manager.Manager, app *App) {
	tpls, err := app.core.GetTemplates(models.TemplateTypeTx, false)
	if err != nil {
		lo.Fatal().Msgf("error loading transactional templates: %v", err)
	}

	for _, t := range tpls {
		tpl := t
		if err := tpl.Compile(app.manager.GenericTemplateFuncs()); err != nil {
			lo.Info().Msgf("error compiling transactional template %d: %v", tpl.ID, err)
			continue
		}
		m.CacheTpl(tpl.ID, &tpl)
	}
}

/*
 * Added for caching the gateway info (or provider of messenger)
 */

func initGatewayDetails(m *manager.Manager, app *App) map[string]models.SMTP {
	gat, err := app.core.GetGatewayDetails(m.GetGatewayName())

	if err != nil {
		lo.Fatal().Msgf("error loading gateway details: %v", err)
	}
	smtpDetails := make(map[string]models.SMTP)

	providerCount := make(map[string]int)

	for _, t := range gat {
		messenger := t.Messenger.String
		val, h := providerCount[messenger]
		if h {
			providerCount[messenger] = val + 1
		} else {
			providerCount[messenger] = 1
		}
	}

	gatewayFlags := make(map[string]bool)

	for _, t := range gat {
		gtd := t

		count := providerCount[gtd.Messenger.String]

		if count == 1 {
			gtd.IsDefault = true
		}

		if gtd.Method.Valid && gtd.Method.String == "smtp" {
			out := models.SMTP{}
			if err := json.Unmarshal([]byte(gtd.Configuration), &out); err != nil {
				lo.Fatal().Msgf("error unmarshalling smtp settings: %v", err)
			}

			if !out.Enabled {
				continue
			}
			_, ok := smtpDetails[out.UUID]
			if ok {
				continue
			}
			out.ID = int64(gtd.ID)
			out.Default = gtd.IsDefault
			smtpDetails[out.UUID] = out
			m.CacheGatewayContract(gtd.Messenger.String, &gtd)
			gatewayFlags[gtd.Messenger.String] = true
			continue
		}
		if err := gtd.CompileV2(app.manager.GenericTemplateFuncs()); err != nil {
			lo.Error().Msgf("error compiling gateway details with %d: %v", gtd.ID, err)
			continue
		}
		if gtd.AuthConfig != nil && gtd.AuthConfig.Url != "" {
			gtd.AuthConfigInterface = authprovider.NewGatewayAuthProvider(*gtd.AuthConfig)
		}

		m.CacheGatewayContract(gtd.Messenger.String, &gtd)
		gatewayFlags[gtd.Messenger.String] = true
	}

	m.AddGatewayFlags(gatewayFlags)

	return smtpDetails
}

func initGatewayProperties() models.GatewayConfig {
	var s models.GatewayConfig

	var propMap map[string]interface{}

	if err := ko.Unmarshal("gatewayConfig", &propMap); err != nil {
		lo.Fatal().Msgf("error loading firebase gatewayConfig: %v", err)
	}
	value, _ := json.Marshal(propMap)
	lo.Info().Msgf("values == %v", string(value))

	prop := propMap["property"]
	delete(propMap, "property")
	s.Gateway = propMap

	resultMap, ok := prop.(map[string]interface{})
	if !ok {
		lo.Info().Msg("Not a map[string]interface{}")
		return s
	}
	s.Properties = resultMap
	return s
}

// initImporter initializes the bulk subscriber importer.
func initImporter(q *models.Queries, db *sqlx.DB, app *App) *subimporter.Importer {
	return subimporter.New(
		subimporter.Options{
			DomainBlocklist:    app.constants.Privacy.DomainBlocklist,
			UpsertStmt:         q.UpsertSubscriber.Stmt,
			UpsertAttribsStmt:  q.UpsertAttribsStmt.Stmt,
			BlocklistStmt:      q.UpsertBlocklistSubscriber.Stmt,
			UpdateListDateStmt: q.UpdateListsDate.Stmt,
			NotifCB: func(subject string, data interface{}, ctx context.Context) error {
				app.sendNotification(app.constants.NotifyEmails, subject, notifTplImport, data, ctx)
				return nil
			},
		}, db.DB, app.i18n)
}

// initSMTPMessenger initializes the SMTP messenger.
func initSMTPMessenger(gateway map[string]models.SMTP, skipSSL bool, maxFileSize int64) messenger.Messenger {
	var (
		servers  = make([]email.Server, 0, len(gateway))
		confMap  = make(map[string]interface{})
		smtpList = make([]models.SMTP, 0, len(gateway))
	)

	for _, val := range gateway {
		smtpList = append(smtpList, val)
	}

	confMap["smtp"] = smtpList

	confBytes, err := json.Marshal(confMap)

	if err != nil {
		panic(err)
	}

	ko := koanf.New(".")
	if err := ko.Load(rawbytes.Provider(confBytes), jsonK.Parser()); err != nil {
		lo.Error().Msgf("error loading SMTP settings: %v", err)
		panic(err)
	}

	items := ko.Slices("smtp")
	if len(items) == 0 {
		lo.Info().Msgf("no SMTP servers found in settings")
		return &email.Emailer{}
	}

	for _, item := range items {

		var s email.Server
		if err := item.UnmarshalWithConf("", &s, koanf.UnmarshalConf{Tag: "json"}); err != nil {
			lo.Error().Msgf("error unmarshalling SMTP settings: %v", err)
			panic(err)
		}

		servers = append(servers, s)
		lo.Info().Msgf("loaded email (SMTP) messenger: %s@%s",
			item.String("username"), item.String("host"))
	}
	email.SetSSLFlag(skipSSL)
	email.SetMaxEmailAttachmentSize(maxFileSize)
	if len(servers) == 0 {
		lo.Info().Msgf("no SMTP servers enabled in settings")
	}

	// Initialize the e-mail messenger with multiple SMTP servers.
	msgr, err := email.New(servers...)
	if err != nil {
		lo.Fatal().Msgf("error loading e-mail messenger: %v", err)
	}

	return msgr
}

// initPostbackMessengers initializes and returns all the enabled
// HTTP postback messenger backends.
func initPostbackMessengers(m *manager.Manager, skipSSL bool) []messenger.Messenger {
	items := ko.Slices("messengers")
	if len(items) == 0 {
		return nil
	}

	var out []messenger.Messenger
	for _, item := range items {
		if !item.Bool("enabled") {
			continue
		}

		// Read the Postback server config.
		var (
			name = item.String("name")
			o    postback.Options
		)
		if err := item.UnmarshalWithConf("", &o, koanf.UnmarshalConf{Tag: "json"}); err != nil {
			lo.Fatal().Msgf("error reading Postback config: %v", err)
		}

		// Initialize the Messenger.
		p, err := postback.New(o, skipSSL)
		if err != nil {
			lo.Fatal().Msgf("error initializing Postback messenger %s: %v", name, err)
		}
		out = append(out, p)

		lo.Info().Msgf("loaded Postback messenger: %s", name)
	}

	return out
}

// initMediaStore initializes Upload manager with a custom backend.
func initMediaStore() media.Store {
	switch provider := ko.String("upload.provider"); provider {
	case "s3":
		var o s3.Opt
		o.AccessKey = config.ServerConfig.AwsAccessKey
		o.SecretKey = config.ServerConfig.AwsSecretKey
		o.Region = config.ServerConfig.Region
		o.Bucket = config.ServerConfig.BucketName
		o.BucketSubFolder = config.ServerConfig.BucketSubFolder
		o.BucketType = config.ServerConfig.BucketType
		o.CdnUrl = config.ServerConfig.CDNUrl
		o.PublicURL = config.ServerConfig.PublicUrl
		// ko.Unmarshal("upload.s3", &o)
		up, err := s3.NewS3Store(o)
		if err != nil {
			lo.Fatal().Msgf("error initializing s3 upload provider %s", err)
		}
		lo.Info().Msg("media upload provider: s3")
		return up

	case "filesystem":
		var o filesystem.Opts

		ko.Unmarshal("upload.filesystem", &o)
		o.RootURL = ko.String("app.root_url")
		o.UploadPath = filepath.Clean(o.UploadPath)
		o.UploadURI = filepath.Clean(o.UploadURI)
		up, err := filesystem.New(o)
		if err != nil {
			lo.Fatal().Msgf("error initializing filesystem upload provider %s", err)
		}
		lo.Info().Msg("media upload provider: filesystem")
		return up

	default:
		lo.Fatal().Msgf("unknown provider. select filesystem or s3")
	}
	return nil
}

// initNotifTemplates compiles and returns e-mail notification templates that are
// used for sending ad-hoc notifications to admins and subscribers.
func initNotifTemplates(path string, fs stuffbin.FileSystem, i *i18n.I18n, cs *constants) *notifTpls {
	// Register utility functions that the e-mail templates can use.
	funcs := template.FuncMap{
		"RootURL": func() string {
			return cs.RootURL
		},
		"LogoURL": func() string {
			return cs.LogoURL
		},
		"L": func() *i18n.I18n {
			return i
		},
		"Safe": func(safeHTML string) template.HTML {
			return template.HTML(safeHTML)
		},
	}

	tpls, err := stuffbin.ParseTemplatesGlob(funcs, fs, "/static/email-templates/*.html")
	if err != nil {
		lo.Fatal().Msgf("error parsing e-mail notif templates: %v", err)
	}

	html, err := fs.Read("/static/email-templates/base.html")
	if err != nil {
		lo.Fatal().Msgf("error reading static/email-templates/base.html: %v", err)
	}

	out := &notifTpls{
		tpls:        tpls,
		contentType: models.CampaignContentTypeHTML,
	}

	// Determine whether the notification templates are HTML or plaintext.
	// Copy the first few (arbitrary) bytes of the template and check if has the <!doctype html> tag.
	ln := 256
	if len(html) < ln {
		ln = len(html)
	}
	h := make([]byte, ln)
	copy(h, html[0:ln])

	if !bytes.Contains(bytes.ToLower(h), []byte("<!doctype html")) {
		out.contentType = models.CampaignContentTypePlain
		lo.Info().Msg("system e-mail templates are plaintext")
	}

	return out
}

// initBounceManager initializes the bounce manager that scans mailboxes and listens to webhooks
// for incoming bounce events.
func initBounceManager(app *App) *bounce.Manager {
	opt := bounce.Opt{
		WebhooksEnabled: ko.Bool("bounce.webhooks_enabled"),
		SESEnabled:      ko.Bool("bounce.ses_enabled"),
		SendgridEnabled: ko.Bool("bounce.sendgrid_enabled"),
		SendgridKey:     ko.String("bounce.sendgrid_key"),

		RecordBounceCB: app.core.RecordBounce,
	}

	// For now, only one mailbox is supported.
	for _, b := range ko.Slices("bounce.mailboxes") {
		if !b.Bool("enabled") {
			continue
		}

		var boxOpt mailbox.Opt
		if err := b.UnmarshalWithConf("", &boxOpt, koanf.UnmarshalConf{Tag: "json"}); err != nil {
			lo.Fatal().Msgf("error reading bounce mailbox config: %v", err)
		}

		opt.MailboxType = b.String("type")
		opt.MailboxEnabled = true
		opt.Mailbox = boxOpt
		break
	}

	b, err := bounce.New(opt, &bounce.Queries{
		RecordQuery: app.queries.RecordBounce,
	}, app.log)
	if err != nil {
		lo.Fatal().Msgf("error initializing bounce manager: %v", err)
	}

	return b
}

// initHTTPServer sets up and runs the app's main HTTP server and blocks forever.
func initHTTPServer(app *App) *echo.Echo {
	// Initialize the HTTP server.
	var srv = echo.New()
	srv.HideBanner = true

	// Register app (*App) to be injected into all HTTP handlers.
	srv.Use(tracer.SetUpMiddleware(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("app", app)
			return next(c)
		}
	}))

	// Parse and load user facing templates.
	tpl, err := stuffbin.ParseTemplatesGlob(template.FuncMap{
		"L": func() *i18n.I18n {
			return app.i18n
		}}, app.fs, "/public/templates/*.html")
	if err != nil {
		lo.Fatal().Msgf("error parsing public templates: %v", err)
	}
	srv.Renderer = &tplRenderer{
		templates:           tpl,
		SiteName:            app.constants.SiteName,
		RootURL:             app.constants.RootURL,
		LogoURL:             app.constants.LogoURL,
		FaviconURL:          app.constants.FaviconURL,
		EnablePublicSubPage: app.constants.EnablePublicSubPage,
		EnablePublicArchive: app.constants.EnablePublicArchive,
	}

	// Initialize the static file server.
	fSrv := app.fs.FileServer()

	// Public (subscriber) facing static files.
	srv.GET("/public/static/*", echo.WrapHandler(fSrv))

	// Admin (frontend) facing static files.
	srv.GET("/admin/static/*", echo.WrapHandler(fSrv))

	// Public (subscriber) facing media upload files.
	if ko.String("upload.provider") == "filesystem" && ko.String("upload.filesystem.upload_uri") != "" {
		srv.Static(ko.String("upload.filesystem.upload_uri"), ko.String("upload.filesystem.upload_path"))
	}

	// Register all HTTP handlers.
	initHTTPHandlers(srv, app)

	// Start the server.
	go func() {
		if err := srv.Start(ko.String("app.address")); err != nil {
			if strings.Contains(err.Error(), "Server closed") {
				lo.Info().Msgf("HTTP server shut down")
			} else {
				lo.Fatal().Msgf("error starting HTTP server: %v", err)
			}
		}
	}()

	return srv
}

func awaitReload(sigChan chan os.Signal, closerWait chan bool, closer func()) chan bool {
	// The blocking signal handler that main() waits on.
	out := make(chan bool)

	// Respawn a new process and exit the running one.
	respawn := func() {
		if err := syscall.Exec(os.Args[0], os.Args, os.Environ()); err != nil {
			lo.Fatal().Msgf("error spawning process: %v", err)
		}
		os.Exit(0)
	}

	// Listen for reload signal.
	go func() {
		for range sigChan {
			lo.Info().Msg("reloading on signal ...")

			go closer()
			select {
			case <-closerWait:
				// Wait for the closer to finish.
				respawn()
			case <-time.After(time.Second * 3):
				// Or timeout and force close.
				respawn()
			}
		}
	}()

	return out
}

func joinFSPaths(root string, paths []string) []string {
	out := make([]string, 0, len(paths))
	for _, p := range paths {
		// real_path:stuffbin_alias
		f := strings.Split(p, ":")

		out = append(out, path.Join(root, f[0])+":"+f[1])
	}

	return out
}

func generateRandomNumber() (string, error) {
	const dictionary = "0123456789"
	var bytes = make([]byte, 6)

	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	for k, v := range bytes {
		bytes[k] = dictionary[v%byte(len(dictionary))]
	}

	return string(bytes), nil
}

func loadRedisLuaScriptForFreqCapLocal(secondaryClient *redis.Client) {
	lo.Info().Msg("Loading Redis Lua script for frequency cap")
	rscript := `
	local category = tostring(ARGV[1]) -- represents category id
	local timeOffset = tonumber(ARGV[10]) or 0 -- time in seconds for non UTC
	local catLimit = tonumber(ARGV[2]) -- represents category level limit
	local catUpdatedAt = tonumber(ARGV[3]) + timeOffset -- represents category updated time in seconds
	local catDays = tonumber(ARGV[4]) or 1 -- how many days set at category level
	local chanLimit = tonumber(ARGV[5]) -- channel level limit
	local chanUpdatedAt = tonumber(ARGV[6]) + timeOffset -- channel updated time in seconds
	local chanDays = tonumber(ARGV[7]) -- how many days set at channel level
	local ignoreFreqCap = (ARGV[8] == "true") -- flag if ignore rate limiter logic
	local ignoreCatCap = (ARGV[9] == "true") -- flag if category rate limit to be ignored
	local now = redis.call('TIME')[1] + timeOffset
	local catResult = false
	local chanResult = false
	local secondsRemainingToday = 86400 - (now % 86400)
	local nextChanMidnight = secondsRemainingToday + (chanDays - 1) * 86400
	local nextCatMidnight = secondsRemainingToday + (catDays - 1) * 86400
	local currentCount = tonumber(0)
	local createdAt = tonumber(0)

	local key = KEYS[1] -- Assuming KEYS[1] is the deviceId:channel

	if redis.call('EXISTS', key) == 0 then
	    redis.call('HMSET', key, 
	        'count', 0,
	        'created_at', now
	    )
	    redis.call('EXPIRE', key, nextChanMidnight)
	    chanResult = true
	else
		currentCount = tonumber(redis.call('HGET', key, 'count'))
		createdAt = tonumber(redis.call('HGET', key, 'created_at'))
	    if createdAt < chanUpdatedAt then
	        redis.call('EXPIRE', key, nextChanMidnight)
	    end
	    if currentCount < chanLimit then
	        chanResult = true
	    else
	        chanResult = false
	    end
	end
	
	
	local catKey = KEYS[2] -- Assuming KEYS[2] is the category key
	
	if redis.call('EXISTS', catKey) == 0 then
	    redis.call('HMSET', catKey, 
	        'count', 1,
	        'created_at', now
	    )
	    redis.call('EXPIRE', catKey, nextCatMidnight)
	    catResult = true
	else
		currentCount = tonumber(redis.call('HGET', catKey, 'count'))
		createdAt = tonumber(redis.call('HGET', catKey, 'created_at'))
	    if createdAt < catUpdatedAt then
	        redis.call('EXPIRE', catKey, nextCatMidnight)
	    end
	    if (chanResult and (currentCount < catLimit)) or ignoreCatCap then
			redis.call('HINCRBY', catKey, 'count', 1)
	        catResult = true
	    else
	        catResult = false
	    end
	end
	
	if (catResult and chanResult) or ignoreFreqCap then
		redis.call('HINCRBY', key, 'count', 1)
	    return true
	end
	
	return 0`
	//err := secondaryClient.ForEachMaster(context.Background(), func(ctx context.Context, client *redis.Client) error {
	_, err := secondaryClient.ScriptLoad(context.Background(), rscript).Result()
	//return err
	//})
	if err != nil {
		lo.Fatal().Msgf("Failed to load script cluster-wide: %v", err)
	} else {
		lo.Info().Msg("Redis Lua script loaded successfully")
	}

	// Get SHA (same across all nodes for same script)
	freqCapRedisScript = redis.NewScript(rscript).Hash()
}
