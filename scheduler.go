package main

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	push "github.com/phuslu/log"
	"github.com/robfig/cron/v3"
)

const (
	campaignCronCheck     string = "campaignCreationCron"
	campaignCronCheckLock string = "listmonk.cron.update.task"
	runningCampaign       string = "runningCampaignCron"
	runningCampaignLock   string = "listmonk.cron.running.campaign"
)

var (
	listmonkSubject string
)

func initCron() {
	lo.Info().Msg("initializing cron")
	c := cron.New()
	c.AddFunc("*/5 * * * *", lockAndProcessTask)
	c.Start()
	lo.Info().Msg("successfully initialized cron ")

	lo.Info().Msg("Initializing cron for publishing running campaign")
	runnerCron := cron.New()
	runnerCron.AddFunc("*/1 * * * *", publishRunningCampaign)
	runnerCron.Start()
	lo.Info().Msg("Successfully initialized cron for publishing running campaign")
	listMonkSubject = GetListmonkCronSubject()
}

func lockAndProcessTask() {

	ctx := tracer.GenerateNewTracingContext(context.Background(), "campaignCronCheck", "")

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	logger.Info().Msg("trying to lock the process")

	if utils.AcquireLock(campaignCronCheckLock, campaignCronCheck, 200, logger) {
		logger.Info().Msgf("Successfully acquired the redis lock for task %s", campaignCronCheck)
		processTask(ctx)
	} else {
		logger.Info().Msgf("Unable to acquire redis lock for task %s", campaignCronCheck)
	}

}

func processTask(ctx context.Context) {

	currentTime := time.Now()
	truncatedTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
		currentTime.Hour(), currentTime.Minute(), 0, 0, currentTime.Location())
	duration, _ := time.ParseDuration("5m")
	subtractedTime := truncatedTime.Add(-duration)
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	logger.Info().Msgf("Scheduler: checking if any campaign cron matches the current time: %v", truncatedTime)

	db = GetDB()
	stmt, err := db.Preparex(`select id, cron, report_name from campaigns where cron is not null and status = 'finished' and (end_at is null or end_at >=now()) and paused=false`)
	if err != nil {
		logger.Error().Msgf("Failed to fetch campaign due to error: %v", err)
		return
	}
	defer stmt.Close()

	rows, err := stmt.Queryx()
	if err != nil {
		logger.Error().Msgf("error while preparing statement for campaign cron: detailed error %v", err)
		return
	}

	defer rows.Close()

	for rows.Next() {
		var campaign models.Campaign
		err := rows.StructScan(&campaign)

		var campaignPayload models.NatsCampaignPayload

		if err != nil {
			// panic(err)
			logger.Error().Msgf("Error occurred : %v", err)
			continue
		}

		exp := campaign.Cron.String

		parserV3 := cron.NewParser(cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
		// schedule, _ := cron.Parse(exp)
		schedule, err := parserV3.Parse(exp)

		if err != nil {
			logger.Error().Msgf("Error occurred while parsing expression: %d", err)
			continue
		}

		scheduledTime := schedule.Next(subtractedTime)

		isMatch := scheduledTime == truncatedTime

		if isMatch {
			logger.Info().Msgf("time matches cron event for campaign id: %d", campaign.ID)
			campaignPayload.CampaignId = strconv.Itoa(campaign.ID)
			campaignPayload.EventId = uuid.NewString()
			campaignPayload.EventTime = truncatedTime.String()
			campaignPayload.EventType = models.CampaignCreationCron
			campaignPayload.ReportName = campaign.ReportName
			jsonData, err := json.Marshal(campaignPayload)

			if err != nil {
				logger.Error().Msgf("Error marshaling JSON: %v", err)
				continue
			}

			PublishToNats([]byte(jsonData), listMonkSubject, "", ctx)

		} else {
			logger.Debug().Msgf("%s does not match", exp)
		}

	}
}

func publishRunningCampaign() {

	ctx := tracer.GenerateNewTracingContext(context.Background(), "runningCampaignCron", "")

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	logger.Info().Msgf("Running publishRunningCampaign")

	if utils.AcquireLock(runningCampaignLock, runningCampaign, 500, logger) {
		logger.Info().Msgf("Successfully acquired the redis lock for task: %s with key %s", runningCampaign, runningCampaignLock)

		db = GetDB()

		rows, err := db.Queryx(app.queries.GetRunningCampaigns, maxConcurrentCampaigns)
		logger.Info().Msgf("maxConcurrentCampaigns is %v", maxConcurrentCampaigns)

		if err != nil {
			logger.Error().Msgf("Error fetching running campaign %v", err)
			utils.ReleaseLock(runningCampaignLock, runningCampaign, logger)
			return
		}
		defer rows.Close()

		for rows.Next() {

			var campId int
			rows.Scan(&campId)

			var campaignPayload models.NatsCampaignPayload

			campaignPayload.CampaignId = strconv.Itoa(campId)
			campaignPayload.EventId = uuid.NewString()
			campaignPayload.EventTime = time.Now().String()
			campaignPayload.EventType = models.RunningCampaignCron
			jsonData, err := json.Marshal(campaignPayload)

			if err != nil {
				logger.Error().Msgf("Error marshaling JSON: %v", err)
				utils.ReleaseLock(runningCampaignLock, runningCampaign, logger)
				return
			}
			logger.Info().Msgf("publishing to nats for campaign id %v", campId)
			PublishToNats([]byte(jsonData), listMonkSubject, "", ctx)
		}

		utils.ReleaseLock(runningCampaignLock, runningCampaign, logger)

	} else {
		logger.Info().Msgf("Unable to acquire redis lock for task: %s", runningCampaign)
	}
}
