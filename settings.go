package main

import (
	"bytes"
	"io"
	"net/http"
	"regexp"
	"strings"
	"syscall"
	"time"

	"github.com/gofrs/uuid"
	"github.com/knadh/koanf"
	"github.com/knadh/koanf/parsers/json"
	"github.com/knadh/koanf/providers/rawbytes"
	"github.com/knadh/listmonk/internal/messenger"
	"github.com/knadh/listmonk/internal/messenger/email"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

var (
	reAlphaNum = regexp.MustCompile(`[^a-z0-9\-]`)
)

// handleGetSettings returns settings from the DB.
func handleGetSettings(c echo.Context) error {
	app := c.Get("app").(*App)
	logger := c.Get("logger").(push.Logger)

	logger.Info().Msgf("Got request for get settings api")

	s, err := app.core.GetSettings()
	if err != nil {
		logger.Error().Msgf("got error while fetching the settings, details error %v", err)
		return err
	}
	// Empty out passwords.
	for i := 0; i < len(s.SMTP); i++ {
		s.SMTP[i].Password = ""
	}
	for i := 0; i < len(s.BounceBoxes); i++ {
		s.BounceBoxes[i].Password = ""
	}
	for i := 0; i < len(s.Messengers); i++ {
		s.Messengers[i].Password = ""
	}
	s.UploadS3AwsSecretAccessKey = ""
	s.SendgridKey = ""

	logger.Info().Msgf("successfullt executed the getSettings api")

	return c.JSON(http.StatusOK, okResp{s})
}

// handleUpdateSettings returns settings from the DB.
func handleUpdateSettings(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		set    models.Settings
		logger = c.Get("logger").(push.Logger)
	)

	// Unmarshal and marshal the fields once to sanitize the settings blob.
	if err := c.Bind(&set); err != nil {
		return err
	}

	logger.Info().Msgf("Got request for update settings api %v", set)

	// Get the existing settings.
	cur, err := app.core.GetSettings()
	if err != nil {
		logger.Error().Msgf("error occurred while fetching settings %v", err)
		return err
	}

	currentSmtp := make(map[string]models.SMTP)

	for _, val := range cur.SMTP {
		currentSmtp[val.UUID] = val
	}

	smtpNames := make(map[string]bool)
	modifiedSmtp := make(map[string]models.SMTP)

	for i, s := range set.SMTP {

		if _, ok := smtpNames[s.Name]; ok {
			logger.Error().Msg("smtp name is not unique")
			return echo.NewHTTPError(http.StatusBadRequest, "smtp name should be unique")
		}
		if s.Name != "" {
			smtpNames[s.Name] = true
		}
		// Assign a UUID. The frontend only sends a password when the user explicitly
		// changes the password. In other cases, the existing password in the DB
		// is copied while updating the settings and the UUID is used to match
		// the incoming array of SMTP blocks with the array in the DB.
		if s.UUID == "" {
			set.SMTP[i].UUID = uuid.Must(uuid.NewV4()).String()
		}

		c, has := currentSmtp[s.UUID]

		if has {
			if c.Name != "" && c.Name != s.Name {
				return echo.NewHTTPError(http.StatusBadRequest, "smtp name cannot be changed once assigned")
			}
			// If there's no password coming in from the frontend, copy the existing
			// password by matching the UUID.
			if s.Password == "" && s.UUID == c.UUID {
				set.SMTP[i].Password = c.Password
			}
		} else if s.Password == "" {
			return echo.NewHTTPError(http.StatusBadRequest, "password is mandatory")
		}

		modifiedSmtp[s.UUID] = set.SMTP[i]

	}

	if len(modifiedSmtp) > 0 {
		err := UpdateSmtpGateway(modifiedSmtp, app, logger, c)
		if err != nil {
			return err
		}
	}

	set.SMTP = nil

	// Bounce boxes.
	for i, s := range set.BounceBoxes {
		// Assign a UUID. The frontend only sends a password when the user explicitly
		// changes the password. In other cases, the existing password in the DB
		// is copied while updating the settings and the UUID is used to match
		// the incoming array of blocks with the array in the DB.
		if s.UUID == "" {
			set.BounceBoxes[i].UUID = uuid.Must(uuid.NewV4()).String()
		}

		if d, _ := time.ParseDuration(s.ScanInterval); d.Minutes() < 1 {
			return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("settings.bounces.invalidScanInterval"))
		}

		// If there's no password coming in from the frontend, copy the existing
		// password by matching the UUID.
		if s.Password == "" {
			for _, c := range cur.BounceBoxes {
				if s.UUID == c.UUID {
					set.BounceBoxes[i].Password = c.Password
				}
			}
		}
	}

	// Validate and sanitize postback Messenger names. Duplicates are disallowed
	// and "email" is a reserved name.
	names := map[string]bool{emailMsgr: true}

	for i, m := range set.Messengers {
		// UUID to keep track of password changes similar to the SMTP logic above.
		if m.UUID == "" {
			set.Messengers[i].UUID = uuid.Must(uuid.NewV4()).String()
		}

		if m.Password == "" {
			for _, c := range cur.Messengers {
				if m.UUID == c.UUID {
					set.Messengers[i].Password = c.Password
				}
			}
		}

		name := reAlphaNum.ReplaceAllString(strings.ToLower(m.Name), "")
		if _, ok := names[name]; ok {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("settings.duplicateMessengerName", "name", name))
		}
		if len(name) == 0 {
			return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("settings.invalidMessengerName"))
		}

		set.Messengers[i].Name = name
		names[name] = true
	}

	// S3 password?
	if set.UploadS3AwsSecretAccessKey == "" {
		set.UploadS3AwsSecretAccessKey = cur.UploadS3AwsSecretAccessKey
	}
	if set.SendgridKey == "" {
		set.SendgridKey = cur.SendgridKey
	}

	// Domain blocklist.
	doms := make([]string, 0)
	for _, d := range set.DomainBlocklist {
		d = strings.TrimSpace(strings.ToLower(d))
		if d != "" {
			doms = append(doms, d)
		}
	}
	set.DomainBlocklist = doms

	// Update the settings in the DB.
	if err := app.core.UpdateSettings(set); err != nil {
		return err
	}

	// If there are any active campaigns, don't do an auto reload and
	// warn the user on the frontend.
	if app.manager.HasRunningCampaigns() {
		app.Lock()
		app.needsRestart = true
		app.Unlock()

		return c.JSON(http.StatusOK, okResp{struct {
			NeedsRestart bool `json:"needs_restart"`
		}{true}})
	}

	// No running campaigns. Reload the app.
	go func() {
		<-time.After(time.Millisecond * 500)
		app.sigChan <- syscall.SIGHUP
	}()

	return c.JSON(http.StatusOK, okResp{true})
}

// handleGetLogs returns the log entries stored in the log buffer.
// func handleGetLogs(c echo.Context) error {
// 	app := c.Get("app").(*App)
// 	return c.JSON(http.StatusOK, okResp{app.bufLog.Lines()})
// }

// handleTestSMTPSettings returns the log entries stored in the log buffer.
func handleTestSMTPSettings(c echo.Context) error {
	app := c.Get("app").(*App)
	logger := c.Get("logger").(push.Logger)

	// Copy the raw JSON post body.
	reqBody, err := io.ReadAll(c.Request().Body)
	if err != nil {
		logger.Error().Msgf("error reading SMTP test: %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("globals.messages.internalError"))
	}

	// Load the JSON into koanf to parse SMTP settings properly including timestrings.
	ko := koanf.New(".")
	if err := ko.Load(rawbytes.Provider(reqBody), json.Parser()); err != nil {
		logger.Error().Msgf("error unmarshalling SMTP test request: %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("globals.messages.internalError"))
	}

	req := email.Server{}
	if err := ko.UnmarshalWithConf("", &req, koanf.UnmarshalConf{Tag: "json"}); err != nil {
		logger.Error().Msgf("error scanning SMTP test request: %v", err)
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("globals.messages.internalError"))
	}

	to := ko.String("email")
	if to == "" {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("globals.messages.missingFields", "name", "email"))
	}

	// Initialize a new SMTP pool.
	req.MaxConns = 1
	req.IdleTimeout = time.Second * 2
	req.PoolWaitTimeout = time.Second * 2
	req.IsDefault = true
	msgr, err := email.New(req)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.errorCreating", "name", "SMTP", "error", err.Error()))
	}

	var b bytes.Buffer
	if err := app.notifTpls.tpls.ExecuteTemplate(&b, "smtp-test", nil); err != nil {
		logger.Error().Msgf("error compiling notification template '%s': %v", "smtp-test", err)
		return err
	}

	m := messenger.Message{}
	m.ContentType = app.notifTpls.contentType
	m.From = req.Opt.HelloHostname
	m.To = []string{to}
	m.Subject = app.i18n.T("settings.smtp.testConnection")
	m.Body = b.Bytes()
	m.Method = "smtp"
	m.Ctx = tracer.WrapEchoContextLogger(c)
	if _, err := msgr.Push(m); err != nil {
		logger.Error().Msgf("error sending SMTP test (%s): %v", m.Subject, err)
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, okResp{"test message sent"})
}
