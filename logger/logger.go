package logger

import (
	"fmt"
	"io"

	"github.com/phuslu/log"
)

var Log log.Logger

func InitLogger() log.Logger {
	Log = log.Logger{
		Level:     log.InfoLevel,
		Caller:    1,
		TimeField: "time",
		// TimeFormat: time.RFC33<PERSON><PERSON><PERSON>,
		Writer: &log.ConsoleWriter{Formatter: func(w io.Writer, a *log.FormatterArgs) (int, error) {
			return fmt.Fprintf(w, "\n%s %s[traceID=%s, spanID=%s][campaignMsgId=%s][%s] %s", a.Time, a.Level, a.Get("traceID"), a.<PERSON>("spanID"), a.Get("campaignMsgId"), a.Caller, a.Message)
		}},
		//Context: log.NewContext(nil).Str("traceID", "").Str("spanID", "").Value(),
	}

	log.DefaultLogger = Log

	return Log
}

func NewContextWithTracer(traceID, spanID string) log.Context {
	return log.NewContext(nil).Str("traceID", traceID).Str("spanID", spanID).Value()
}

func NewContextWithTracerMsgId(traceID, spanID string, msgId string) log.Context {
	return log.NewContext(nil).Str("traceID", traceID).Str("spanID", spanID).Str("campaignMsgId", msgId).Value()
}
