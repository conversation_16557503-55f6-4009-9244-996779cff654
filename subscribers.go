package main

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

const (
	dummyUUID = "00000000-0000-0000-0000-000000000000"
)

// subQueryReq is a "catch all" struct for reading various
// subscriber related requests.
type subQueryReq struct {
	Query         string  `json:"query"`
	ListIDs       []int   `json:"list_ids"`
	TargetListIDs []int   `json:"target_list_ids"`
	SubscriberIDs []int64 `json:"ids"`
	Action        string  `json:"action"`
	Status        string  `json:"status"`
}

// subProfileData represents a subscriber's collated data in JSON
// for export.
type subProfileData struct {
	Email         string          `db:"email" json:"-"`
	Profile       json.RawMessage `db:"profile" json:"profile,omitempty"`
	Subscriptions json.RawMessage `db:"subscriptions" json:"subscriptions,omitempty"`
	CampaignViews json.RawMessage `db:"campaign_views" json:"campaign_views,omitempty"`
	LinkClicks    json.RawMessage `db:"link_clicks" json:"link_clicks,omitempty"`
}

// subOptin contains the data that's passed to the double opt-in e-mail template.
type subOptin struct {
	models.Subscriber

	OptinURL string
	UnsubURL string
	Lists    []models.List
}

var (
	dummySubscriber = models.Subscriber{
		Email:   "<EMAIL>",
		Name:    "Demo Subscriber",
		UUID:    dummyUUID,
		Attribs: models.JSON{"city": "Bengaluru"},
	}

	subQuerySortFields = []string{"email", "name", "created_at", "updated_at"}

	errSubscriberExists = errors.New("subscriber already exists")
)

// handleGetSubscriber handles the retrieval of a single subscriber by ID.
func handleGetSubscriber(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.ParseInt(c.Param("id"), 10, 64)
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	out, err := app.core.GetSubscriber(id, "", "", logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleQuerySubscribers handles querying subscribers based on an arbitrary SQL expression.
func handleQuerySubscribers(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		pg     = app.paginator.NewFromURL(c.Request().URL.Query())
		logger = c.Get("logger").(push.Logger)

		// The "WHERE ?" bit.
		query   = sanitizeSQLExp(c.FormValue("query"))
		orderBy = c.FormValue("order_by")
		order   = c.FormValue("order")
		out     models.PageResults
	)

	logger.Info().Msgf("got request for querySubscribers")

	// Limit the subscribers to specific lists?
	listIDs, err := getQueryInts("list_id", c.QueryParams())
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	res, total, err := app.core.QuerySubscribers(query, listIDs, order, orderBy, pg.Offset, pg.Limit, logger)
	if err != nil {
		return err
	}

	out.Query = query
	out.Results = res
	out.Total = total
	out.Page = pg.Page
	out.PerPage = pg.PerPage
	logger.Info().Msgf("successfully returned results for querySubscribers")

	return c.JSON(http.StatusOK, okResp{out})
}

// handleExportSubscribers handles querying subscribers based on an arbitrary SQL expression.
func handleExportSubscribers(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)

		// The "WHERE ?" bit.
		query = sanitizeSQLExp(c.FormValue("query"))
	)

	// Limit the subscribers to specific lists?
	listIDs, err := getQueryInts("list_id", c.QueryParams())
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	// Export only specific subscriber IDs?
	subIDs, err := getQueryInts64("id", c.QueryParams())
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	// Get the batched export iterator.
	exp, err := app.core.ExportSubscribers(query, subIDs, listIDs, app.constants.DBBatchSize, logger)
	if err != nil {
		return err
	}

	var (
		h  = c.Response().Header()
		wr = csv.NewWriter(c.Response())
	)

	h.Set(echo.HeaderContentType, echo.MIMEOctetStream)
	h.Set("Content-type", "text/csv")
	h.Set(echo.HeaderContentDisposition, "attachment; filename="+"subscribers.csv")
	h.Set("Content-Transfer-Encoding", "binary")
	h.Set("Cache-Control", "no-cache")
	wr.Write([]string{"uuid", "email", "name", "attributes", "status", "created_at", "updated_at"})

loop:
	// Iterate in batches until there are no more subscribers to export.
	for {
		out, err := exp()
		if err != nil {
			return err
		}
		if out == nil || len(out) == 0 {
			break
		}

		for _, r := range out {
			if err = wr.Write([]string{r.UUID, r.Email, r.Name, r.Attribs, r.Status,
				r.CreatedAt.Time.String(), r.UpdatedAt.Time.String()}); err != nil {
				logger.Error().Msgf("error streaming CSV export: %v", err)
				break loop
			}
		}

		// Flush CSV to stream after each batch.
		wr.Flush()
	}

	return nil
}

// handleCreateSubscriber handles the creation of a new subscriber.
func handleCreateSubscriber(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
		req struct {
			models.Subscriber
			Lists          []int    `json:"lists"`
			ListUUIDs      []string `json:"list_uuids"`
			PreconfirmSubs bool     `json:"preconfirm_subscriptions"`
		}
		logger = c.Get("logger").(push.Logger)
	)

	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		return err
	}

	// Validate fields.
	//Commented by Deepali
	// if len(req.Email) > 1000 {
	// 	return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidEmail"))
	// }

	if req.Email != "" {
		em, err := app.importer.SanitizeEmail(req.Email)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}
		req.Email = utils.EncryptData(em, logger)
	}

	req.Name = strings.TrimSpace(req.Name)
	if len(req.Name) == 0 || len(req.Name) > stdInputMaxLen {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidName"))
	}

	// Insert the subscriber into the DB.
	sub, _, err := app.core.InsertSubscriber(req.Subscriber, req.Lists, req.ListUUIDs, req.PreconfirmSubs, tracer.WrapEchoContextLogger(c))
	if err != nil {
		return err
	}
	//name, type, uuid, email, status, attribs
	return c.JSON(http.StatusOK, okResp{sub})
}

// handleUpdateSubscriber handles modification of a subscriber.
func handleUpdateSubscriber(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.ParseInt(c.Param("id"), 10, 64)
		logger = c.Get("logger").(push.Logger)
		req    struct {
			models.Subscriber
			Lists          []int `json:"lists"`
			PreconfirmSubs bool  `json:"preconfirm_subscriptions"`
		}
	)

	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		return err
	}

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	if em, err := app.importer.SanitizeEmail(req.Email); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	} else {
		req.Email = em
	}

	if req.Name != "" && !strHasLen(req.Name, 1, stdInputMaxLen) {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidName"))
	}

	out, err := app.core.UpdateSubscriberWithLists(id, req.Subscriber, req.Lists, nil, req.PreconfirmSubs, true, logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleGetSubscriberSendOptin sends an optin confirmation e-mail to a subscriber.
func handleSubscriberSendOptin(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.ParseInt(c.Param("id"), 10, 64)
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	// Fetch the subscriber.
	out, err := app.core.GetSubscriber(id, "", "", logger)
	if err != nil {
		return err
	}

	if _, err := sendOptinConfirmationHook(app)(out, nil, tracer.WrapEchoContextLogger(c)); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, app.i18n.T("subscribers.errorSendingOptin"))
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleBlocklistSubscribers handles the blocklisting of one or more subscribers.
// It takes either an ID in the URI, or a list of IDs in the request body.
func handleBlocklistSubscribers(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		pID    = c.Param("id")
		subIDs []int64
	)

	// Is it a /:id call?
	if pID != "" {
		id, _ := strconv.ParseInt(pID, 10, 64)
		if id < 1 {
			return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
		}

		subIDs = append(subIDs, id)
	} else {
		// Multiple IDs.
		var req subQueryReq
		if err := c.Bind(&req); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("globals.messages.errorInvalidIDs", "error", err.Error()))
		}
		if len(req.SubscriberIDs) == 0 {
			return echo.NewHTTPError(http.StatusBadRequest,
				"No IDs given.")
		}

		subIDs = req.SubscriberIDs
	}

	if err := app.core.BlocklistSubscribers(subIDs); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleManageSubscriberLists handles bulk addition or removal of subscribers
// from or to one or more target lists.
// It takes either an ID in the URI, or a list of IDs in the request body.
func handleManageSubscriberLists(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		pID    = c.Param("id")
		subIDs []int64
		logger = c.Get("logger").(push.Logger)
	)

	// Is it an /:id call?
	if pID != "" {
		id, _ := strconv.ParseInt(pID, 10, 64)
		if id < 1 {
			return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
		}
		subIDs = append(subIDs, id)
	}

	var req subQueryReq
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.errorInvalidIDs", "error", err.Error()))
	}
	if len(req.SubscriberIDs) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.errorNoIDs"))
	}
	if len(subIDs) == 0 {
		subIDs = req.SubscriberIDs
	}
	if len(req.TargetListIDs) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.errorNoListsGiven"))
	}

	// Action.
	var err error
	switch req.Action {
	case "add":
		err = app.core.AddSubscriptions(subIDs, req.TargetListIDs, req.Status, logger)
	case "remove":
		err = app.core.DeleteSubscriptions(subIDs, req.TargetListIDs, logger)
	case "unsubscribe":
		err = app.core.UnsubscribeLists(subIDs, req.TargetListIDs, nil, logger)
	default:
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidAction"))
	}

	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleDeleteSubscribers handles subscriber deletion.
// It takes either an ID in the URI, or a list of IDs in the request body.
func handleDeleteSubscribers(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		pID    = c.Param("id")
		subIDs []int
		logger = c.Get("logger").(push.Logger)
	)

	// Is it an /:id call?
	if pID != "" {
		id, _ := strconv.Atoi(pID)
		if id < 1 {
			return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
		}
		subIDs = append(subIDs, id)
	} else {
		// Multiple IDs.
		i, err := parseStringIDs(c.Request().URL.Query()["id"])
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("globals.messages.errorInvalidIDs", "error", err.Error()))
		}
		if len(i) == 0 {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("subscribers.errorNoIDs", "error", err.Error()))
		}
		subIDs = i
	}

	if err := app.core.DeleteSubscribers(subIDs, nil, logger); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleDeleteSubscribersByQuery bulk deletes based on an
// arbitrary SQL expression.
func handleDeleteSubscribersByQuery(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		req    subQueryReq
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&req); err != nil {
		return err
	}

	if err := app.core.DeleteSubscribersByQuery(req.Query, req.ListIDs, logger); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleBlocklistSubscribersByQuery bulk blocklists subscribers
// based on an arbitrary SQL expression.
func handleBlocklistSubscribersByQuery(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
		req subQueryReq
	)

	if err := c.Bind(&req); err != nil {
		return err
	}

	if err := app.core.BlocklistSubscribersByQuery(req.Query, req.ListIDs); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleManageSubscriberListsByQuery bulk adds/removes/unsubscribes subscribers
// from one or more lists based on an arbitrary SQL expression.
func handleManageSubscriberListsByQuery(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		req    subQueryReq
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&req); err != nil {
		return err
	}
	if len(req.TargetListIDs) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.T("subscribers.errorNoListsGiven"))
	}

	// Action.
	var err error
	switch req.Action {
	case "add":
		err = app.core.AddSubscriptionsByQuery(req.Query, req.ListIDs, req.TargetListIDs, logger)
	case "remove":
		err = app.core.DeleteSubscriptionsByQuery(req.Query, req.ListIDs, req.TargetListIDs, logger)
	case "unsubscribe":
		err = app.core.UnsubscribeListsByQuery(req.Query, req.ListIDs, req.TargetListIDs, logger)
	default:
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidAction"))
	}

	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleDeleteSubscriberBounces deletes all the bounces on a subscriber.
func handleDeleteSubscriberBounces(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
		pID = c.Param("id")
	)

	id, _ := strconv.Atoi(pID)
	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	if err := app.core.DeleteSubscriberBounces(id, ""); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleExportSubscriberData pulls the subscriber's profile,
// list subscriptions, campaign views and clicks and produces
// a JSON report. This is a privacy feature and depends on the
// configuration in app.Constants.Privacy.
func handleExportSubscriberData(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		pID    = c.Param("id")
		logger = c.Get("logger").(push.Logger)
	)

	id, _ := strconv.Atoi(pID)
	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	// Get the subscriber's data. A single query that gets the profile,
	// list subscriptions, campaign views, and link clicks. Names of
	// private lists are replaced with "Private list".
	_, b, err := exportSubscriberData(id, "", app.constants.Privacy.Exportable, app, logger)
	if err != nil {
		logger.Error().Msgf("error exporting subscriber data: %s", err)
		return echo.NewHTTPError(http.StatusInternalServerError,
			app.i18n.Ts("globals.messages.errorFetching",
				"name", "{globals.terms.subscribers}", "error", err.Error()))
	}

	c.Response().Header().Set("Cache-Control", "no-cache")
	c.Response().Header().Set("Content-Disposition", `attachment; filename="data.json"`)
	return c.Blob(http.StatusOK, "application/json", b)
}

// exportSubscriberData collates the data of a subscriber including profile,
// subscriptions, campaign_views, link_clicks (if they're enabled in the config)
// and returns a formatted, indented JSON payload. Either takes a numeric id
// and an empty subUUID or takes 0 and a string subUUID.
func exportSubscriberData(id int, subUUID string, exportables map[string]bool, app *App, logger push.Logger) (models.SubscriberExportProfile, []byte, error) {
	data, err := app.core.GetSubscriberProfileForExport(id, subUUID, logger)
	if err != nil {
		return data, nil, err
	}

	// Filter out the non-exportable items.
	if _, ok := exportables["profile"]; !ok {
		data.Profile = nil
	}
	if _, ok := exportables["subscriptions"]; !ok {
		data.Subscriptions = nil
	}
	if _, ok := exportables["campaign_views"]; !ok {
		data.CampaignViews = nil
	}
	if _, ok := exportables["link_clicks"]; !ok {
		data.LinkClicks = nil
	}

	// Marshal the data into an indented payload.
	b, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		logger.Error().Msgf("error marshalling subscriber export data: %v", err)
		return data, nil, err
	}

	return data, b, nil
}

// sanitizeSQLExp does basic sanitisation on arbitrary
// SQL query expressions coming from the frontend.
func sanitizeSQLExp(q string) string {
	if len(q) == 0 {
		return ""
	}
	q = strings.TrimSpace(q)

	// Remove semicolon suffix.
	if q[len(q)-1] == ';' {
		q = q[:len(q)-1]
	}
	return q
}

func getQueryInts(param string, qp url.Values) ([]int, error) {
	var out []int
	if vals, ok := qp[param]; ok {
		for _, v := range vals {
			if v == "" {
				continue
			}

			listID, err := strconv.Atoi(v)
			if err != nil {
				return nil, err
			}
			out = append(out, listID)
		}
	}

	return out, nil
}

func getQueryInts64(param string, qp url.Values) ([]int64, error) {
	var out []int64
	if vals, ok := qp[param]; ok {
		for _, v := range vals {
			if v == "" {
				continue
			}

			listID, err := strconv.ParseInt(v, 10, 64)
			if err != nil {
				return nil, err
			}
			out = append(out, listID)
		}
	}

	return out, nil
}

// sendOptinConfirmationHook returns an enclosed callback that sends optin confirmation e-mails.
// This is plugged into the 'core' package to send optin confirmations when a new subscriber is
// created via `core.CreateSubscriber()`.
func sendOptinConfirmationHook(app *App) func(sub models.Subscriber, listIDs []int, ctx context.Context) (int, error) {
	return func(sub models.Subscriber, listIDs []int, ctx context.Context) (int, error) {
		logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
		lists, err := app.core.GetSubscriberLists(sub.ID, "", listIDs, nil, models.SubscriptionStatusUnconfirmed, models.ListOptinDouble, logger)
		if err != nil {
			return 0, err
		}

		// None.
		if len(lists) == 0 {
			return 0, nil
		}

		var (
			out      = subOptin{Subscriber: sub, Lists: lists}
			qListIDs = url.Values{}
		)

		// Construct the opt-in URL with list IDs.
		for _, l := range out.Lists {
			qListIDs.Add("l", l.UUID)
		}
		out.OptinURL = fmt.Sprintf(app.constants.OptinURL, sub.UUID, qListIDs.Encode())
		out.UnsubURL = fmt.Sprintf(app.constants.UnsubURL, dummyUUID, sub.UUID)

		// Send the e-mail.
		if err := app.sendNotification([]string{sub.Email}, app.i18n.T("subscribers.optinSubject"), notifSubscriberOptin, out, ctx); err != nil {
			logger.Error().Msgf("error sending opt-in e-mail: %s", err)
			return 0, err
		}

		return len(lists), nil
	}
}

func AddSubscriberList(subscriberId int, listId int) {
	stmt, err := db.Prepare("insert into subscriber_lists(subscriber_id , list_id, status) values($1,$2,$3)")
	if err != nil {
		panic(err)
	}
	defer stmt.Close()
	// Execute the insert statement
	_, err = stmt.Exec(subscriberId, listId, "confirmed")
	if err != nil {
		panic(err)
	}
	fmt.Println("Data inserted successfully.")
}

func AddSubscriber(name string) (id int) {
	var subscriberId int
	uu, err := uuid.NewV4()
	if err != nil {
		fmt.Printf("error generating UUID: %v", err)
	}
	stmt, err := db.Prepare("INSERT INTO subscribers (uuid, name, status, type,email) VALUES ($1, $2, $3, $4,$5) RETURNING id;")
	err = stmt.QueryRow(uu, name, "enabled", "Terminal", "").Scan(&subscriberId)
	if err != nil {
		return 0
	}
	defer stmt.Close()
	return subscriberId
}

func UpdateListMembership(name string, segmentId string) {
	lo.Printf("Started updating the segment on nats event source: %s with segment id: %s", name, segmentId)
	//check if segment is present in listmonk
	var id int

	stmt, err := db.Prepare("update lists set status = 'pending' where name = $1 returning id")
	if err != nil {
		return
	}
	defer stmt.Close()
	rows, err := stmt.Query(name)
	if err != nil {
		return
	}
	defer rows.Close()

	if rows.Next() {

		err = rows.Scan(&id)

		if err != nil {
			return
		}
		//if segment is present then update subscribers
		log.Printf("Segment found in listmonk with id %v", id)

		func(value int) {
			stmt, err := db.Prepare("DELETE FROM subscriber_lists WHERE list_id = $1")
			if err != nil {
				fmt.Println(err)
			}
			defer stmt.Close()
			result, err := stmt.Exec(id)
			if err != nil {
				fmt.Println(err)
			}

			rowsAffected, err := result.RowsAffected()
			if err != nil {
				fmt.Println(err)
			}

			fmt.Println(rowsAffected, "rows deleted.")
			key := "segment_" + segmentId + "_members"

			err, sub := utils.GetRedisData(key)

			if sub != "" {

				segMembers := models.SegmentMembers{}
				err = json.Unmarshal([]byte(sub), &segMembers)
				if err != nil {
					fmt.Println("error marshalling the segment membership cache :", err)
				}
				terminalList := segMembers.Members
				for _, item := range terminalList {

					err := func(item string, memberType string, id int) error {
						var subscriberId int

						log.Printf("member = %v", item)
						subscriber := models.Subscriber{}
						subscriber.Name = item
						subscriber.Type = memberType

						stmt1, _ := db.Prepare("SELECT id from subscribers where name =$1 and type = $2 ")
						rows, err := stmt1.Query(item, memberType)
						if err != nil {
							fmt.Println(err)
							return err
						}
						defer stmt1.Close()
						defer rows.Close()
						if rows.Next() {
							if err := rows.Scan(&subscriberId); err != nil {
								fmt.Println(err)
								return err
							}
							// if subscriber is present then update membership
							AddSubscriberList(subscriberId, id)
						} else {
							// if not then add subscriber and membership
							subId := AddSubscriber(item)
							if subId != 0 {
								AddSubscriberList(subId, id)
							}
						}

						return nil
					}(item, segMembers.SegmentMemberType, value)

					if err != nil {
						log.Printf("Error occured while populating member %v for segment %v", item, segmentId)
					}

				}
			}

		}(id)

		stmt2, err := db.Prepare("update lists set status = 'finished' where name = $1 returning id")
		if err != nil {
			return
		}
		defer stmt2.Close()
		stmt2.Exec(name)

	}

	lo.Printf("Finished updating the segment on nats event source: %s with segment id: %s", name, segmentId)
}

func UpdateListMembershipV2(event models.EventData, ctx context.Context) {

	natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	natsLogger.Info().Msgf("Started updating the segment on nats event source: %s with segment id: %v", event.SegmentName, event.SegmentId)

	listData, err := updateListStatus(event.SegmentName, "pending")

	if err != nil {
		natsLogger.Info().Msgf("eror occured while scanning the segmentName %v detailed error: %v", event.SegmentName, err)
		return
	}

	if listData == nil {
		natsLogger.Info().Msgf("list not found in listmonk with name %v", event.SegmentName)
		return
	}

	//if segment is present then update subscribers
	natsLogger.Info().Msgf("Segment found in listmonk with id %v", listData.ID)

	addCount := len(event.MembersAdded)
	removeCount := len(event.MembersRemoved)

	if event.MemberAddedCount == 0 && event.MemberRemovedCount == 0 {
		natsLogger.Info().Msgf("No members to add or remove for segmentId %v and lisId %v", event.SegmentId, listData.ID)

		_, err = updateListStatus(event.SegmentName, "finished")

		if err != nil {
			natsLogger.Info().Msgf("eror occured while scanning the segmentName %v detailed error: %v", event.SegmentName, err)
			return
		}
		return
	}

	addCountLarge := addCount == 0 && event.MemberAddedCount > 0
	removeCountLarge := removeCount == 0 && event.MemberRemovedCount > 0

	if addCountLarge || removeCountLarge {
		natsLogger.Error().Msgf("Update operation selected for segmentId %v and lisId %v", event.SegmentId, listData.ID)

		rowsAffected, err := deletePreviousMembers(listData.ID, app)

		if err != nil {
			natsLogger.Error().Msgf("Error occured while deleting previous members for segmentId %v and lisId %v", event.SegmentId, listData.ID)
			return
		}
		natsLogger.Info().Msgf("Deleted %v members for segmentId %v and lisId %v", rowsAffected, event.SegmentId, listData.ID)
		addSegmentV2([]models.List{*listData}, []int{listData.ID}, ctx)
	} else if event.MemberRemovedCount == 0 && !addCountLarge && event.MemberAddedCount != 0 {
		natsLogger.Info().Msgf("Add operation for segmentId %v and lisId %v", event.SegmentId, listData.ID)

		addSegmentOperation(models.SegmentMembershipResponse{
			Id:         int64(event.SegmentId),
			MemberType: "Terminal",
			MemberIds:  event.MembersAdded,
		}, int64(listData.ID), ctx)
	} else if event.MemberAddedCount == 0 && !removeCountLarge && event.MemberRemovedCount != 0 {
		natsLogger.Info().Msgf("Remove operation for segmentId %v and lisId %v", event.SegmentId, listData.ID)
		removeSegmentOperation(models.SegmentMembershipResponse{
			Id:         int64(event.SegmentId),
			MemberType: "Terminal",
			MemberIds:  event.MembersRemoved,
		}, int64(listData.ID), app, ctx)
	} else if !addCountLarge && !removeCountLarge {
		natsLogger.Info().Msgf("Add and Remove operation for segmentId %v and lisId %v", event.SegmentId, listData.ID)
		addSegmentOperation(models.SegmentMembershipResponse{
			Id:         int64(event.SegmentId),
			MemberType: "Terminal",
			MemberIds:  event.MembersAdded,
		}, int64(listData.ID), ctx)
		removeSegmentOperation(models.SegmentMembershipResponse{
			Id:         int64(event.SegmentId),
			MemberType: "Terminal",
			MemberIds:  event.MembersRemoved,
		}, int64(listData.ID), app, ctx)
	}

	updateListStatus(event.SegmentName, "finished")
	natsLogger.Info().Msgf("Finished updating the segment on nats event source: %s with segment id: %v", event.SegmentName, event.SegmentId)
}

func updateListStatus(name string, status string) (*models.List, error) {
	listData := models.List{}

	stmt, err := db.Prepare("UPDATE lists SET status = $1, updated_at= now() WHERE name = $2 RETURNING id, name, tags, description")
	if err != nil {
		return &listData, err
	}
	defer stmt.Close()

	err = stmt.QueryRow(status, name).Scan(&listData.ID, &listData.Name, &listData.Tags, &listData.Description)
	if err != nil {
		return &listData, err
	}
	return &listData, nil
}

func deletePreviousMembers(listId int, app *App) (int64, error) {
	stmt, err := db.Prepare("DELETE FROM subscriber_lists WHERE list_id = $1")
	if err != nil {
		return 0, err
	}
	defer stmt.Close()
	result, err := stmt.Exec(listId)
	if err != nil {
		return 0, err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, err

	}
	return rowsAffected, nil
}

func handleGetSubscribersAttributes(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
		req struct {
			Member     string `json:"member"`
			MemberType string `json:"memberType"`
		}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, okResp{"Invalid request"})
	}

	if req.Member == "" || req.MemberType == "" {
		return c.JSON(http.StatusBadRequest, okResp{"member and type are required"})
	}

	sub, err := app.core.GetSubscribersAttributes(req.Member, req.MemberType, logger)

	if err != nil {
		return c.JSON(http.StatusInternalServerError, okResp{err.Error()})
	}

	return c.JSON(http.StatusOK, okResp{sub})
}

func handleAddSubscribersAttributes(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
		req    models.Subscriber
	)

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, okResp{"Invalid request"})
	}

	data, _ := json.Marshal(req)

	logger.Info().Msgf("got request for addSubscriber attributes: " + string(data))
	data = nil

	if req.Name == "" || req.Type == "" {
		return c.JSON(http.StatusBadRequest, okResp{"member and type are required"})
	}
	if len(req.Email) > 0 {
		email, err := app.importer.SanitizeEmail(req.Email)
		if err != nil {
			return c.JSON(http.StatusBadRequest, okResp{"Invalid email"})
		} else {
			req.Email = email
		}
	}

	var attribs models.JSON
	if len(req.Attribs) > 0 {
		attribs = req.Attribs
	}

	req.Name = strings.TrimSpace(req.Name)
	req.Type = strings.TrimSpace(req.Type)

	subString := "(values('" + req.Name + "'," + "'" + req.Type + "')) as k(name, type)"

	subList, err := fetchSubscribers(subString)

	if err != nil {
		logger.Error().Msgf("error occured while fetching subscribers(%s,%s) detailed error: %v", req.Name, req.Type, err)
		return c.JSON(http.StatusInternalServerError, okResp{"Internal Server Error"})
	}

	var subscriber models.Subscriber

	if len(subList) == 0 {
		subscriber, err = app.core.InsertNewSubscriber(req.Name, req.Type, req.Email, removeEmptyKeys(attribs), tracer.WrapEchoContextLogger(c))
	} else {
		subscriber, err = app.core.UpdateSubscriberAttribs(subList[0].ID, removeEmptyKeys(req.Attribs), logger)
	}

	if err != nil {
		return c.JSON(http.StatusInternalServerError, okResp{"Internal Server Error"})
	}

	return c.JSON(http.StatusOK, okResp{subscriber})
}

func fetchSubscribers(subsString string) ([]Subscriber, error) {

	subscriberList := []Subscriber{}
	query := `with subs as (select * from ` + subsString + `)` + `select s.id as id, s.name as name, s.type as type from subscribers s join subs on subs.name = s.name and subs.type =s.type`

	err := db.Select(&subscriberList, query)

	if err != nil {
		return subscriberList, err
	}

	return subscriberList, nil
}

func removeEmptyKeys(data models.JSON) models.JSON {
	filtered := make(models.JSON)

	for key, value := range data {
		if value != nil {
			switch v := value.(type) {
			case string:
				if v != "" {
					filtered[key] = value
				}
			default:
				filtered[key] = value
			}
		}
	}

	return filtered
}
