package authprovider

import (
	"context"
	"time"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
)

type ProviderAuthDetails struct {
	models.AuthDetails
	ticker *time.Ticker
	value  map[string]string
	done   chan struct{}
}

func NewGatewayAuthProvider(a models.AuthDetails) *ProviderAuthDetails {
	p := &ProviderAuthDetails{
		AuthDetails: a,
		done:        make(chan struct{}),
	}
	p.TokenGenerator()
	return p
}

func (a *ProviderAuthDetails) TokenGenerator() {

	expiry := int64(1800)

	if a.Expiry > 0 {
		expiry = int64(a.Expiry)
	}

	a.ticker = time.NewTicker(time.Duration(expiry) * time.Second)
	a.value = getLatestToken(a)
	go func() {
		for {
			select {
			case <-a.done:
				a.ticker.Stop()
				return
			case <-a.ticker.C:
				a.value = getLatestToken(a)
			}
		}
	}()

}

func (a *ProviderAuthDetails) GetValue() map[string]string {
	return a.value
}

func (a *ProviderAuthDetails) Close() {
	defer func() {
		if r := recover(); r != nil {
			return
		}
	}()
	close(a.done)
}

func getLatestToken(a *ProviderAuthDetails) map[string]string {
	response, err := utils.HandleAuthApi(models.AuthDetails{
		RequestBody:   a.RequestBody,
		RequestParams: a.RequestParams,
		Response:      a.Response,
		Method:        a.Method,
		Type:          a.Type,
		Url:           a.Url,
		Headers:       a.Headers,
		Messenger:     a.Messenger,
	}, tracer.GenerateNewTracingContext(context.Background(), "providerAuth", ""))

	if err != nil {
		return a.value
	}
	return response
}
