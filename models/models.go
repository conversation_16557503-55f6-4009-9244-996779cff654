package models

import (
	"bytes"
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	txttpl "text/template"
	"time"

	unescape "html"

	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/types"
	"github.com/lib/pq"
	"github.com/yuin/goldmark"
	"github.com/yuin/goldmark/extension"
	"github.com/yuin/goldmark/parser"
	"github.com/yuin/goldmark/renderer/html"
	null "gopkg.in/volatiletech/null.v6"
)

// Enum values for various statuses.
const (
	// Subscriber.
	SubscriberStatusEnabled     = "enabled"
	SubscriberStatusDisabled    = "disabled"
	SubscriberStatusBlockListed = "blocklisted"

	// Subscription.
	SubscriptionStatusUnconfirmed  = "unconfirmed"
	SubscriptionStatusConfirmed    = "confirmed"
	SubscriptionStatusUnsubscribed = "unsubscribed"

	// Campaign.
	CampaignStatusDraft         = "draft"
	CampaignStatusScheduled     = "scheduled"
	CampaignStatusRunning       = "running"
	CampaignStatusPaused        = "paused"
	CampaignStatusFinished      = "finished"
	CampaignStatusCancelled     = "cancelled"
	CampaignTypeRegular         = "regular"
	CampaignTypeOptin           = "optin"
	CampaignContentTypeRichtext = "richtext"
	CampaignContentTypeHTML     = "html"
	CampaignContentTypeMarkdown = "markdown"
	CampaignContentTypePlain    = "plain"

	// List.
	ListTypePrivate = "private"
	ListTypePublic  = "public"
	ListOptinSingle = "single"
	ListOptinDouble = "double"

	// User.
	UserTypeSuperadmin = "superadmin"
	UserTypeUser       = "user"
	UserStatusEnabled  = "enabled"
	UserStatusDisabled = "disabled"

	// BaseTpl is the name of the base template.
	BaseTpl = "base"

	// ContentTpl is the name of the compiled message.
	ContentTpl = "content"

	// Headers attached to e-mails for bounce tracking.
	EmailHeaderSubscriberUUID = "X-Listmonk-Subscriber"
	EmailHeaderCampaignUUID   = "X-Listmonk-Campaign"

	// Standard e-mail headers.
	EmailHeaderDate        = "Date"
	EmailHeaderFrom        = "From"
	EmailHeaderSubject     = "Subject"
	EmailHeaderMessageId   = "Message-Id"
	EmailHeaderDeliveredTo = "Delivered-To"
	EmailHeaderReceived    = "Received"

	BounceTypeHard = "hard"
	BounceTypeSoft = "soft"

	// Templates.
	TemplateTypeCampaign = "campaign"
	TemplateTypeTx       = "tx"

	//event types for nats consumer
	RunningCampaignCron  = "runningCampaign"
	CampaignCreationCron = "campaignCreationCron"

	//status for process_status column in campaigns table
	//it represents whether a campaign be already picked by scheduler
	ProcessCompleted = "completed"
	SchedulerPicked  = "picked"

	CampaignStatusFailed = "failed"

	//Channel Frequency Days Min and Max
	FrequencyDaysMin  = 1
	FrequencyDaysMax  = 30
	FrequencyValueMin = 1
	FrequencyValueMax = 100

	RejectedDueToDuplication      = "rejected_deliveries_due_to_duplication"
	RejectedDueToFrequencyCapping = "rejected_deliveries_due_to_frequency_capping"
	RejectedDueToOptOut           = "rejected_deliveries_due_to_opt_out"
	SuccessDelivery               = "success_deliveries"
	FailedDelivery                = "failure_deliveries"
)

// Headers represents an array of string maps used to represent SMTP, HTTP headers etc.
// similar to url.Values{}
type Headers []map[string]string

// regTplFunc represents contains a regular expression for wrapping and
// substituting a Go template function from the user's shorthand to a full
// function call.
type regTplFunc struct {
	regExp  *regexp.Regexp
	replace string
}

var regTplFuncs = []regTplFunc{
	// Regular expression for matching {{ TrackLink "http://link.com" }} in the template
	// and substituting it with {{ Track "http://link.com" . }} (the dot context)
	// before compilation. This is to make linking easier for users.
	{
		regExp:  regexp.MustCompile("{{(\\s+)?TrackLink(\\s+)?(.+?)(\\s+)?}}"),
		replace: `{{ TrackLink $3 . }}`,
	},

	// Convert the shorthand https://google.com@TrackLink to {{ TrackLink ... }}.
	// This is for WYSIWYG editors that encode and break quotes {{ "" }} when inserted
	// inside <a href="{{ TrackLink "https://these-quotes-break" }}>.
	{
		regExp:  regexp.MustCompile(`(https?://.+?)@TrackLink`),
		replace: `{{ TrackLink "$1" . }}`,
	},

	{
		regExp:  regexp.MustCompile(`{{(\s+)?(TrackView|UnsubscribeURL|ManageURL|OptinURL|MessageURL)(\s+)?}}`),
		replace: `{{ $2 . }}`,
	},
}

// AdminNotifCallback is a callback function that's called
// when a campaign's status changes.
type AdminNotifCallback func(subject string, data interface{}, ctx context.Context) error

// PageResults is a generic HTTP response container for paginated results of list of items.
type PageResults struct {
	Results interface{} `json:"results"`

	Query   string `json:"query"`
	Total   int    `json:"total"`
	PerPage int    `json:"per_page"`
	Page    int    `json:"page"`
}

// Base holds common fields shared across models.
type Base struct {
	ID        int       `db:"id" json:"id"`
	CreatedAt null.Time `db:"created_at" json:"created_at"`
	UpdatedAt null.Time `db:"updated_at" json:"updated_at"`
}

// BaseV2 holds common fields shared across models with int64.
type BaseV2 struct {
	ID        int64     `db:"id" json:"id"`
	CreatedAt null.Time `db:"created_at" json:"created_at"`
	UpdatedAt null.Time `db:"updated_at" json:"updated_at"`
}

type SubscriberLists struct {
	SubscriberID int64 `db:"subscriber_id"`
	ListId       int64 `db:"list_id"`
}

// User represents an admin user.
type User struct {
	Base

	Email    string `json:"email"`
	Name     string `json:"name"`
	Password string `json:"-"`
	Type     string `json:"type"`
	Status   string `json:"status"`
}

// Real time segment struct.
type RealTimeSegResp struct {
	Id        string   `json:"id"`
	Variables []string `json:"variables"`
	Name      string   `json:"name"`
}

type RealTimeSegDataResult struct {
	Result []string `json:"result"`
}

type RealTimeSegDataReq struct {
	PropertyName string  `json:"propertyName"`
	Request      Request `json:"request"`
}

// Real time segment data struct.
type RealTimeSegDataResp struct {
	Status      string                `json:"status"`
	RespMessage string                `json:"respMessage"`
	StatusCode  string                `json:"statusCode"`
	Results     RealTimeSegDataResult `json:"results"`
}

type Request struct {
	TerminalId string `json:"terminalId"`
}

// Subscriber represents an e-mail subscriber.
type Subscriber struct {
	BaseV2

	UUID        string            `db:"uuid" json:"uuid,omitempty"`
	Email       string            `db:"email" json:"email,omitempty" form:"email"`
	Name        string            `db:"name" json:"name" form:"name"`
	Attribs     JSON              `db:"attribs" json:"attribs,omitempty"`
	Status      string            `db:"status" json:"status,omitempty"`
	Lists       types.JSONText    `db:"lists" json:"lists,omitempty"`
	Type        string            `db:"type" json:"type"`
	Preference  bool              `db:"pref" json:"-"`
	FileAttribs map[string]string `json:"fileAttribs"`
}
type subLists struct {
	SubscriberID int64          `db:"subscriber_id"`
	Lists        types.JSONText `db:"lists"`
}

// Subscription represents a list attached to a subscriber.
type Subscription struct {
	List
	SubscriptionStatus    null.String `db:"subscription_status" json:"subscription_status"`
	SubscriptionCreatedAt null.String `db:"subscription_created_at" json:"subscription_created_at"`
}

// SubscriberExportProfile represents a subscriber's collated data in JSON for export.
type SubscriberExportProfile struct {
	Email         string          `db:"email" json:"-"`
	Profile       json.RawMessage `db:"profile" json:"profile,omitempty"`
	Subscriptions json.RawMessage `db:"subscriptions" json:"subscriptions,omitempty"`
	CampaignViews json.RawMessage `db:"campaign_views" json:"campaign_views,omitempty"`
	LinkClicks    json.RawMessage `db:"link_clicks" json:"link_clicks,omitempty"`
}

type SubscriberAttribes struct {
	Member     string          `db:"member" json:"member"`
	Attributes json.RawMessage `db:"attributes" json:"attributes"`
}

// JSON is is the wrapper for reading and writing arbitrary JSONB fields from the DB.
type JSON map[string]interface{}

// StringIntMap is used to define DB Scan()s.
type StringIntMap map[string]int

// Subscribers represents a slice of Subscriber.
type Subscribers []Subscriber

// SubscriberExport represents a subscriber record that is exported to raw data.
type SubscriberExport struct {
	BaseV2

	UUID    string `db:"uuid" json:"uuid"`
	Email   string `db:"email" json:"email"`
	Name    string `db:"name" json:"name"`
	Attribs string `db:"attribs" json:"attribs"`
	Status  string `db:"status" json:"status"`
}

// For De-duplication
type CampaignTargets struct {
	Id         int64       `db:"id" json:"id"`
	CampaignId int         `db:"campaign_id" json:"campaignId"`
	Target     null.String `db:"target" json:"target"`
	Status     null.String `db:"status" json:"status"`
	CreatedAt  null.Time   `db:"created_at" json:"created_at"`
}

type MessengerLog struct {
	UUID              string    `db:"uuid" json:"uuid,omitempty"`
	CreatedAt         null.Time `db:"created_at" json:"created_at"`
	UpdatedAt         null.Time `db:"updated_at" json:"updated_at"`
	RequestType       string    `db:"request_type" json:"request_type"`
	MessengerType     string    `db:"messenger_type" json:"messenger_type"`
	MemberName        string    `db:"member_name" json:"member_name"`
	MemberType        string    `db:"member_type" json:"member_type"`
	Target            string    `db:"target" json:"target"`
	Status            string    `db:"status" json:"status"`
	Remarks           string    `db:"remarks" json:"remarks"`
	Response          JSON      `db:"response" json:"response"`
	TemplateId        int       `db:"template_id" json:"template_id"`
	ReferenceID       string    `db:"reference_id" json:"ref_id"`
	AdditionalDetails JSON      `db:"additional_details" json:"additional_details"`
	CreatedBy         string    `db:"created_by" json:"created_by"`
	UpdatedBy         string    `db:"updated_by" json:"updated_by"`
}

// Added by Deepali
type Atribs struct {
	Text  string
	Value string
}

type CampaignAtribsMaster struct {
	Base
	CampaignId int    `db:"campaign_id" json:"campaign_id"`
	Attribs    string `db:"attribs" json:"attribs"`
}

type Segment struct {
	SegmentName string
	SegmentId   float32
	MemberCount int
}

type SegmentMembers struct {
	Members           []string
	SegmentMemberType string
}

type Terminal struct {
	Email string `json:"email"`
}

type FcmCache struct {
	RoleId    string   `json:"roleId"`
	FcmTokens []string `json:"fcmTokens"`
}

type FcmTokenOject struct {
	Status           string     `json:"status"`
	RespMessage      string     `json:"respMessage"`
	StatusCode       string     `json:"statusCode"`
	RoleAndFCMTokens []FcmCache `json:"roleAndFCMTokens"`
}

type Config struct {
	ServerConfig ServerConfig
}

type SmsConfig struct {
	Gateway    []string
	Properties map[string]interface{}
}
type MqttConfig struct {
	serverUrl string
	port      int
	qos       int
	username  string
	password  string
}

type GatewayConfig struct {
	Gateway    map[string]interface{}
	Properties map[string]interface{}
}

type ServerConfig struct {
	NatsUrl                   string
	NatsReplica               int
	IsLocal                   bool
	RedisUrl                  string
	FirebaseJsonPath          string
	AesAccessKey              string
	AesSecretKey              string
	AesKeyId                  string
	KeyVaultUrl               string
	TestEnv                   bool
	RedisPoolSize             int
	ChangeLogEnabled          bool
	UserTerminalInfoApiUrl    string
	SegmentServiceUrl         string
	OneAppAuthUrl             string
	AwsAccessKey              string
	AwsSecretKey              string
	BucketName                string
	BucketSubFolder           string
	Region                    string
	NatsMaxAge                string
	NatsMaxBytes              int64
	NatsMaxMessages           int64
	NatsMaxMessageSize        int32
	NatsDuplicateWindow       string
	NatsMaxMessagesPerSubject int64
	NatsStreamUpdateRequired  bool
	SkipSSLCheck              bool
	SegmentMembershipPageSize int
	MaxEmailAttachmentSize    int64
	BatchSize                 int
	MqttEnabled               bool
	SoundboxServiceApiUrl     string
	MaxConcurrentCampaigns    int
	RedisCampaignTTLHours     int
	SecondaryRedisUrl         string
	SecondaryRedisPoolSize    int
	CDNUrl                    string
	BucketType                string
	PublicUrl                 string
	HealthCheckBasePath       string
	MaxVideoSizeInMb          int64
	MaxAudioSizeInMb          int64
}

type Cache struct {
	AccountNumber      string `json:"accountNumber"`
	CorporateId        string `json:"corporateId"`
	LegalName          string `json:"legalName"`
	MerchantId         string `json:"merchantId"`
	TerminalId         string `json:"terminalId"`
	DbaName            string `json:"dbaName"`
	Email              string `json:"email"`
	MobileNumber       string `json:"mobileNumber"`
	City               string `json:"city"`
	State              string `json:"state"`
	PinCode            string `json:"pinCode"`
	MccCode            string `json:"mccCode"`
	Area               string `json:"area"`
	Status             string `json:"status"`
	PrimaryContactName string `json:"primaryContactName"`
}

type TerminalResponse struct {
	Status           string                `json:"status"`
	TerminalsDetails []UserInfoApiResponse `json:"terminalsDetails"`
}

type UserInfoApiResponse map[string]Cache

//Ended by Deepali

// List represents a mailing list.
type List struct {
	Base

	UUID             string         `db:"uuid" json:"uuid"`
	Name             string         `db:"name" json:"name"`
	Type             string         `db:"type" json:"type"`
	Optin            string         `db:"optin" json:"optin"`
	Tags             pq.StringArray `db:"tags" json:"tags"`
	Description      string         `db:"description" json:"description"`
	SubscriberCount  int            `db:"-" json:"subscriber_count"`
	SubscriberCounts StringIntMap   `db:"subscriber_statuses" json:"subscriber_statuses"`
	SubscriberID     int64          `db:"subscriber_id" json:"-"`
	Status           null.String    `db:"status" json:"status"`

	// This is only relevant when querying the lists of a subscriber.
	SubscriptionStatus    string    `db:"subscription_status" json:"subscription_status,omitempty"`
	SubscriptionCreatedAt null.Time `db:"subscription_created_at" json:"subscription_created_at,omitempty"`
	SubscriptionUpdatedAt null.Time `db:"subscription_updated_at" json:"subscription_updated_at,omitempty"`

	// Pseudofield for getting the total number of subscribers
	// in searches and queries.
	Total int `db:"total" json:"-"`
}

type FrequencyConfig struct {
	Freq          int    `json:"freq"`            // Accepts both string or number
	FreqDays      int    `json:"freq_days"`       // Always numeric
	IgnoreFreqCap bool   `json:"ignore_freq_cap"` // Boolean
	DisplayName   string `json:"display_name"`    // Display name for the frequency ***
}

type FrequencyDetails map[string]FrequencyConfig

type Category struct {
	Base
	Name                 string              `db:"name" json:"name"`
	CreatedBy            null.String         `db:"created_by" json:"created_by"`
	UpdatedBy            null.String         `db:"updated_by" json:"updated_by"`
	IsToggleable         NotificationOptions `db:"is_toggleable" json:"isToggleable"`
	IsVisibleToMerchant  NotificationOptions `db:"is_visible_to_merchant" json:"isVisibleToMerchant"`
	NotificationDefaults NotificationOptions `db:"notification_defaults" json:"notificationDefaults"`
	Description          null.String         `db:"description" json:"description"`
	IsDefault            bool                `db:"is_default" json:"isDefault"`
	FrequencyDetails     FrequencyDetails    `db:"frequency_details" json:"frequency_details"`
	FrequencyDetailsJSON json.RawMessage     `json:"-"`
	// Pseudofield for getting the total number of subscribers
	// in searches and queries.
	Total int `db:"total" json:"-"`
}

type DeepLinks struct {
	Base
	Category     string `db:"category" json:"category"`
	Deeplink     string `db:"deeplink" json:"deeplink"`
	DeeplinkName string `db:"deeplinkname" json:"deeplinkName"`
	IsActive     bool   `db:"isactive" json:"isActive"`
	CreatedBy    string `db:"created_by" json:"created_by"`
	UpdatedBy    string `db:"updated_by" json:"updated_by"`
}

type NotificationOptions struct {
	FCM   bool `json:"fcm"`
	SMS   bool `json:"sms"`
	Email bool `json:"email"`
	PAX   bool `json:"pax"`
	MQTT  bool `json:"mqtt"`
}

type TemplateSegmentConfig struct {
	IncludedSegments []SegmentIdName `json:"includedSegments"`
	ExcludedSegments []SegmentIdName `json:"excludedSegments"`
}

type SegmentIdName struct {
	SegmentName string      `json:"segmentName"`
	SegmentId   json.Number `json:"segmentId"`
}

type NotificationMetaData struct {
	ActiveIcon  string `json:"active_icon"`
	PassiveIcon string `json:"passive_icon"`
}

func (no TemplateSegmentConfig) Value() (driver.Value, error) {
	return json.Marshal(no)
}

// Scan implements the sql.Scanner interface
func (no *TemplateSegmentConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	// Check the type of the value
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid notificationOptions type")
	}

	// Unmarshal the JSON data into the struct
	return json.Unmarshal(bytes, no)
}

func (no NotificationOptions) Value() (driver.Value, error) {
	return json.Marshal(no)
}

// Scan implements the sql.Scanner interface
func (no *NotificationOptions) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	// Check the type of the value
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid notificationOptions type")
	}

	// Unmarshal the JSON data into the struct
	return json.Unmarshal(bytes, no)
}

func (fd *FrequencyDetails) Value() (driver.Value, error) {
	return json.Marshal(fd)
}

// Scan implements the sql.Scanner interface
func (fd *FrequencyDetails) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	// Check the type of the value
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid frequencyDetails type")
	}

	// Unmarshal the JSON data into the struct
	return json.Unmarshal(bytes, fd)
}

func (nm *NotificationMetaData) Value() (driver.Value, error) {
	return json.Marshal(nm)
}

// Scan implements the sql.Scanner interface
func (nm *NotificationMetaData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	// Check the type of the value
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid additionalDetails type")
	}

	// Unmarshal the JSON data into the struct
	return json.Unmarshal(bytes, nm)
}

func (dsf *DeliveryStatsFields) Value() (driver.Value, error) {
	return json.Marshal(dsf)
}

// Scan implements the sql.Scanner interface
func (dsf *DeliveryStatsFields) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	// Check the type of the value
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid deliverystatsfields type")
	}

	// Unmarshal the JSON data into the struct
	return json.Unmarshal(bytes, dsf)
}

type SubscriberPreferences struct {
	SubscriberId int64       `db:"subscriber_id" json:"subscriberId"`
	CategoryId   int         `db:"category_id" json:"categoryId"`
	Channel      string      `db:"channel" json:"channel"`
	Value        bool        `db:"value" json:"value"`
	CreatedAt    null.Time   `db:"created_at" json:"createdAt"`
	UpdatedAt    null.Time   `db:"updated_at" json:"updatedAt"`
	CreatedBy    null.String `db:"created_by" json:"createdBy"`
	UpdatedBy    null.String `db:"updated_by" json:"updatedBy"`
}

type SubscriberPreferenceLogs struct {
	SubscriberId int64     `db:"subscriber_id" json:"subscriberId"`
	CategoryId   int       `db:"category_id" json:"categoryId"`
	Channel      string    `db:"channel" json:"channel"`
	Value        bool      `db:"value" json:"value"`
	CategoryName string    `db:"category_name" json:"-"`
	FormatedDate null.Time `db:"formatted_datetime" json:"createdAt"`
}

type SubscriberAttributesKeys struct {
	ID        int32       `db:"id" json:"id"`
	Name      string      `db:"name" json:"name"`
	CreatedAt null.Time   `db:"created_at" json:"createdAt"`
	UpdatedAt null.Time   `db:"updated_at" json:"updatedAt"`
	CreatedBy null.String `db:"created_by" json:"createdBy"`
	UpdatedBy null.String `db:"updated_by" json:"updatedBy"`
}

type PreferenceDTO struct {
	Name     string          `json:"name"`
	ID       int             `json:"id"`
	Channels RequestChannels `json:"channels"`
}

type Preference struct {
	Name     string   `json:"name"`
	ID       int      `json:"id"`
	Channels Channels `json:"channels"`
}
type Channels struct {
	FCM   ChannelOptions `json:"fcm,omitempty"`
	SMS   ChannelOptions `json:"sms,omitempty"`
	Email ChannelOptions `json:"email,omitempty"`
	MQTT  ChannelOptions `json:"mqtt,omitempty"`
}

type RequestChannels struct {
	FCM   *ChannelOptions `json:"fcm,omitempty"`
	SMS   *ChannelOptions `json:"sms,omitempty"`
	Email *ChannelOptions `json:"email,omitempty"`
}

type ChannelOptions struct {
	IsToggleable bool `json:"isToggleable"`
	IsVisible    bool `json:"isVisible"`
	Value        bool `json:"value"`
}
type PreferenceRequest struct {
	Name        string          `json:"name"`
	Type        string          `json:"type"`
	Preferences []PreferenceDTO `json:"preferences"`
}

type PreferenceGetRequest struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

type PreferenceLogsRequest struct {
	Request    []PreferenceGetRequest `json:"request"`
	PageNumber int                    `json:"pageNo"`
	PageSize   int                    `json:"pageSize"`
}

type RealTimeSegReq struct {
	CategoryName string `json:"categoryName"`
}

type SegmentMembershipBase struct {
	SegmentName string `json:"segmentName"`
	LastId      string `json:"lastId"`
	PageSize    int    `json:"pageSize"`
}

type ReportApiRequest struct {
	FileType         string    `json:"fileType,omitempty"`
	ReportName       string    `json:"reportName,omitempty"`
	Name             string    `json:"name,omitempty"`
	IsCampaignReport null.Bool `json:"isCampaignReport,omitempty"`
	Url              string    `json:"url,omitempty"`
}

type ReportResults struct {
	Status        string       `json:"status"`
	StatusMessage string       `json:"respMessage"`
	StatusCode    string       `json:"statusCode"`
	Results       ReportResult `json:"results"`
}

type ReportHeaderResponse struct {
	Headers []string `json:"headers,omitempty"`
}

type ReportResult struct {
	RequestId string `json:"requestId"`
}

type SegmentMembershipResponse struct {
	Id         int64    `json:"id"`
	LastId     string   `json:"lastId"`
	PageSize   int      `json:"pageSize"`
	MemberType string   `json:"membertype"`
	MemberIds  []string `json:"memberIds"`
}

type SoundboxRequest struct {
	MemberType string   `json:"memberType"`
	Members    []string `json:"members"`
}

type SoundboxResponse struct {
	Status        string          `json:"status"`
	Message       string          `json:"message"`
	StatusCode    string          `json:"statusCode"`
	LinkedDevices []LinkedDevices `json:"linkedDevices"`
}

type LinkedDevices struct {
	MemberType string    `json:"memberType"`
	Member     string    `json:"member"`
	Devices    []Devices `json:"devices"`
}

type Devices struct {
	Imei     string `json:"imei"`
	Language string `json:"language"`
}

type TerminalInfoReq struct {
	Tids []string `json:"tids"`
}

type SegmentEligibilityRequest struct {
	Members           []string `json:"members"`
	MemberType        string   `json:"memberType"`
	InclusionSegments []string `json:"inclusionSegments"`
	ExclusionSegments []string `json:"exclusionSegments"`
}

type SegmentEligibilityResponse struct {
	Message string          `json:"message"`
	Result  map[string]bool `json:"results"`
}

// Campaign represents an e-mail campaign.
type Campaign struct {
	Base
	CampaignMeta

	UUID              string             `db:"uuid" json:"uuid"`
	Type              string             `db:"type" json:"type"`
	Name              string             `db:"name" json:"name"`
	Subject           string             `db:"subject" json:"subject"`
	FromEmail         string             `db:"from_email" json:"from_email"`
	Body              string             `db:"body" json:"body"`
	AltBody           null.String        `db:"altbody" json:"altbody"`
	SendAt            null.Time          `db:"send_at" json:"send_at"`
	Status            string             `db:"status" json:"status"`
	ContentType       string             `db:"content_type" json:"content_type"`
	Tags              pq.StringArray     `db:"tags" json:"tags"`
	Headers           Headers            `db:"headers" json:"headers"`
	TemplateID        int                `db:"template_id" json:"template_id"`
	Messenger         string             `db:"messenger" json:"messenger"`
	Archive           bool               `db:"archive" json:"archive"`
	ArchiveTemplateID int                `db:"archive_template_id" json:"archive_template_id"`
	ArchiveMeta       json.RawMessage    `db:"archive_meta" json:"archive_meta"`
	Cron              null.String        `db:"cron" json:"cron"`
	FCMRoles          null.String        `db:"fcm_roles" json:"fcmRoles"`
	FcmImage          null.String        `db:"fcm_image" json:"fcmImage"`
	FcmKeyval         json.RawMessage    `db:"fcm_keyval" json:"fcmKeyval"`
	KeyVal            []AdditionalValues `json:"keyval"`
	Category          null.Int           `db:"category" json:"category"`
	FCMCta            null.String        `db:"fcm_cta" json:"fcmCta"`
	DeDuplication     bool               `db:"de_duplication" json:"deDuplication"`
	DuplicationLevel  null.String        `db:"duplication_level" json:"duplicationLevel"`
	EndAt             null.Time          `db:"end_at" json:"end_at"`

	// TemplateBody is joined in from templates by the next-campaigns query.
	TemplateBody        string             `db:"template_body" json:"-"`
	ArchiveTemplateBody string             `db:"archive_template_body" json:"-"`
	Tpl                 *template.Template `json:"-"`
	SubjectTpl          *txttpl.Template   `json:"-"`
	KeyValueTpl         *txttpl.Template   `json:"-"`
	AltBodyTpl          *template.Template `json:"-"`
	MessageType         string             `db:"message_type" json:"-"`

	NonEmailTpl     *txttpl.Template `json:"-"`
	FilePath        null.String      `db:"file_path" json:"file_path"`
	Paused          bool             `db:"paused" json:"paused"`
	ReportRequestId null.String      `db:"report_request_id" json:"report_request_id"`
	ReportName      null.String      `db:"report_name" json:"report_name"`

	// Pseudofield for getting the total number of subscribers
	// in searches and queries.
	Total       int               `db:"total" json:"-"`
	CampAttribs string            `json:"attribs"`
	SubAttribs  string            `json:"subAttribs"`
	Data        map[string]string `json:"data"`

	Label              string       `json:"label"`
	KeyValRenReq       bool         `json:"-"`
	IsCachePresent     bool         `json:"-"`
	IsRealTimePresent  bool         `json:"-"`
	RealTimeAttribsMut sync.RWMutex `json:"-"`
	RealTimeAttribs    []string     `json:"-"`
	BroadcastName      string       `json:"-"`

	PrefString                    string                 `db:"default_pref" json:"-"`
	DefaultPref                   bool                   `json:"-"`
	CampaignParentUUID            null.String            `db:"parent_campaign_uuid"`
	TemplateName                  null.String            `db:"template_name" json:"templateName"`
	ConversionEventName           null.String            `db:"conversion_event_name" json:"conversionEventName"`
	ConversionEventTime           null.Int               `db:"conversion_event_time" json:"conversionEventTime"`
	TemplateParams                types.JSONText         `db:"template_params" json:"template_params"`
	Constant                      map[string]interface{} `json:"contant"`
	Gateway                       *GateWayDetails        `json:"-"`
	GatewayId                     string                 `json:"-"`
	ParentCampaignId              null.String            `db:"parent_campaign_id" json:"parentCampaignId"`
	ParentCampaignName            null.String            `db:"parent_campaign_name" json:"parentCampaignName"`
	Delivered                     null.Int               `db:"delivered" json:"delivered"`
	CampaignType                  null.String            `db:"campaign_type" json:"campaignType"`
	IgnoreFrequencyCap            null.Bool              `db:"ignore_freq_cap" json:"ignoreFrequencyCap"`
	CategoryFrequencyValue        string                 `json:"-"`
	CategoryFrequencyDuration     string                 `json:"-"`
	CategoryUpdatedAt             string                 `json:"-"`
	IgnoreCategoryFreqCap         string                 `json:"-"`
	NotificationFrequencyValue    string                 `json:"-"`
	NotificationFrequencyDuration string                 `json:"-"`
	NotificationUpdatedAt         string                 `json:"-"`
	EnableNotificationFreqCap     string                 `json:"-"`
	CreatedBy                     null.String            `db:"created_by" json:"createdBy"`
	UpdatedBy                     null.String            `db:"updated_by" json:"updatedBy"`
	DeliveryStats                 DeliveryStatsFields    `db:"delivery_stats" json:"deliveryStats"`
}

type DeliveryStatsFields struct {
	RejectedDueToDuplication      int64 `db:"rejected_due_to_duplication" json:"rejected_due_to_duplication"`
	RejectedDueToFrequencyCapping int64 `db:"rejected_delieveries_due_to_frequency_capping" json:"rejected_delieveries_due_to_frequency_capping"`
	RejectedDueToOptOut           int64 `db:"rejected_delieveries_due_to_opt_out" json:"rejected_delieveries_due_to_opt_out"`
	SuccessDelivery               int64 `db:"success" json:"success"`
	FailedDelivery                int64 `db:"failure" json:"failure"`
}

// CampaignMeta contains fields tracking a campaign's progress.
type CampaignMeta struct {
	CampaignID int `db:"campaign_id" json:"-"`
	Views      int `db:"views" json:"views"`
	Clicks     int `db:"clicks" json:"clicks"`
	Bounces    int `db:"bounces" json:"bounces"`

	// This is a list of {list_id, name} pairs unlike Subscriber.Lists[]
	// because lists can be deleted after a campaign is finished, resulting
	// in null lists data to be returned. For that reason, campaign_lists maintains
	// campaign-list associations with a historical record of id + name that persist
	// even after a list is deleted.
	Lists types.JSONText `db:"lists" json:"lists"`

	StartedAt null.Time `db:"started_at" json:"started_at"`
	ToSend    int       `db:"to_send" json:"to_send"`
	Sent      int       `db:"sent" json:"sent"`
}

type CampaignStats struct {
	ID        int       `db:"id" json:"id"`
	Status    string    `db:"status" json:"status"`
	ToSend    int       `db:"to_send" json:"to_send"`
	Sent      int       `db:"sent" json:"sent"`
	Started   null.Time `db:"started_at" json:"started_at"`
	UpdatedAt null.Time `db:"updated_at" json:"updated_at"`
	Rate      int       `json:"rate"`
	NetRate   int       `json:"net_rate"`
}

type CampaignAnalyticsCount struct {
	CampaignID int       `db:"campaign_id" json:"campaign_id"`
	Count      int       `db:"count" json:"count"`
	Timestamp  time.Time `db:"timestamp" json:"timestamp"`
}

type CampaignAnalyticsLink struct {
	URL   string `db:"url" json:"url"`
	Count int    `db:"count" json:"count"`
}

// Campaigns represents a slice of Campaigns.
type Campaigns []Campaign

// Template represents a reusable e-mail template.
type Template struct {
	Base

	Name string `db:"name" json:"name"`
	// Subject is only for type=tx.
	Subject          string             `db:"subject" json:"subject"`
	Type             string             `db:"type" json:"type"`
	Body             string             `db:"body" json:"body,omitempty"`
	Category         int                `db:"category" json:"category"`
	IsDefault        bool               `db:"is_default" json:"is_default"`
	KeyVal           []AdditionalValues `json:"keyval"`
	AdditionalValues null.String        `db:"additional_values" json:"additional_values"`
	MessageType      string             `db:"message_type" json:"message_type"`

	//Only for sms templates, contains gateway details
	RequestParams      null.String         `db:"request_params" json:"request_params,omitempty"`
	Url                null.String         `db:"url" json:"url,omitempty"`
	Headers            null.String         `db:"headers" json:"headers,omitempty"`
	RequestBody        null.String         `db:"request_body" json:"request_body,omitempty"`
	Method             null.String         `db:"method" json:"method,omitempty"`
	RegisteredTemplate null.String         `db:"registered_template_id" json:"registeredTemplate,omitempty"`
	DefaultPrefs       NotificationOptions `db:"notification_defaults"`

	TemplateParams types.JSONText         `db:"template_params" json:"template_params"`
	SegmentConfig  TemplateSegmentConfig  `db:"segment_config" json:"segmentConfig,omitempty"`
	Constant       map[string]interface{} `json:"-"`
	// Only relevant to tx (transactional) templates.
	SubjectTpl          *txttpl.Template   `json:"-"`
	Tpl                 *template.Template `json:"-"`
	AdditionalValuesTpl *txttpl.Template   `json:"-"`
	HeaderTpl           *txttpl.Template   `json:"-"`
	RequestBodyTpl      *txttpl.Template   `json:"-"`
	RequestParamTpl     *txttpl.Template   `json:"-"`
	IsCachePresent      bool               `json:"-"`
	IncludedSegments    []string           `json:"-"`
	ExcludedSegments    []string           `json:"-"`
	DeDuplication       null.Bool          `db:"de_duplication" json:"de_duplication"`
	DuplicationLevel    null.String        `db:"duplication_level" json:"duplication_level"`
	ProcessDuration     int                `db:"process_duration" json:"process_duration"`
	CreatedBy           null.String        `db:"created_by" json:"createdBy"`
	UpdatedBy           null.String        `db:"updated_by" json:"updatedBy"`
}

type GateWayDetails struct {
	Base
	Name                null.String          `db:"name" json:"name"`
	Messenger           null.String          `db:"messenger" json:"messenger"`
	IsDefault           bool                 `db:"is_default" json:"is_default,omitempty"`
	RequestParams       null.String          `db:"request_params" json:"request_params,omitempty"`
	Url                 null.String          `db:"url" json:"url,omitempty"`
	Headers             null.String          `db:"headers" json:"headers,omitempty"`
	RequestBody         null.String          `db:"request_body" json:"request_body,omitempty"`
	Method              null.String          `db:"method" json:"method,omitempty"`
	Configuration       types.JSONText       `db:"configuration" json:"configuration"`
	HeaderTpl           *txttpl.Template     `json:"-"`
	RequestBodyTpl      *txttpl.Template     `json:"-"`
	RequestParamTpl     *txttpl.Template     `json:"-"`
	AuthConfig          *AuthDetails         `json:"-"`
	AuthConfigInterface AuthDetailsInterface `json:"-"`
	CreatedBy           null.String          `db:"created_by" json:"createdBy"`
	UpdatedBy           null.String          `db:"updated_by" json:"updatedBy"`
}

type AuthDetails struct {
	RequestBody   map[string]interface{}
	RequestParams map[string]interface{}
	Method        string
	Url           string
	Headers       map[string]string
	Response      map[string]interface{}
	Expiry        int
	Type          string
	Messenger     string
}

type AuthDetailStringfied struct {
	RequestBody   string `json:"request_body,omitempty"`
	RequestParams string `json:"request_params,omitempty"`
	Method        string `json:"method,omitempty"`
	Url           string `json:"url,omitempty"`
	Headers       string `json:"headers,omitempty"`
	Response      string `json:"response,omitempty"`
	Expiry        string `json:"expiry,omitempty"`
	Type          string `json:"type,omitempty"`
	Messenger     string `json:"messenger,omitempty"`
}

type AuthDetailsInterface interface {
	TokenGenerator()
	GetValue() map[string]string
	Close()
}

type AdditionalValues struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// Bounce represents a single bounce event.
type Bounce struct {
	ID        int             `db:"id" json:"id"`
	Type      string          `db:"type" json:"type"`
	Source    string          `db:"source" json:"source"`
	Meta      json.RawMessage `db:"meta" json:"meta"`
	CreatedAt time.Time       `db:"created_at" json:"created_at"`

	// One of these should be provided.
	Email          string `db:"email" json:"email,omitempty"`
	SubscriberUUID string `db:"subscriber_uuid" json:"subscriber_uuid,omitempty"`
	SubscriberID   int64  `db:"subscriber_id" json:"subscriber_id,omitempty"`

	CampaignUUID string           `db:"campaign_uuid" json:"campaign_uuid,omitempty"`
	Campaign     *json.RawMessage `db:"campaign" json:"campaign"`

	// Pseudofield for getting the total number of bounces
	// in searches and queries.
	Total int `db:"total" json:"-"`
}

type LoginResponse struct {
	Status        string      `json:"status"`
	StatusMessage string      `json:"statusMessage"`
	StatusCode    string      `json:"statusCode"`
	UserDetails   UserDetails `json:"userDetails"`
}

type UserDetails struct {
	EmailId  string      `json:"emailId"`
	UserName string      `json:"userName"`
	Role     string      `json:"role"`
	UserId   json.Number `json:"userId"`
}

// TxMessage represents an e-mail campaign.
type TxMessage1 struct {
	//Added by Deepali
	MemberId   string `json:"memberId"`
	MemberType string `json:"memberType"`
	//End by Deepali
	SubscriberEmails []string `json:"subscriber_emails"`
	SubscriberIDs    []int64  `json:"subscriber_ids"`

	// Deprecated.
	SubscriberEmail string `json:"subscriber_email"`
	SubscriberID    int64  `json:"subscriber_id"`

	TemplateID  int                    `json:"template_id"`
	Data        map[string]interface{} `json:"data"`
	FromEmail   string                 `json:"from_email"`
	Headers     Headers                `json:"headers"`
	ContentType string                 `json:"content_type"`
	Messenger   string                 `json:"messenger"`

	Subject    string             `json:"-"`
	Body       []byte             `json:"-"`
	Tpl        *template.Template `json:"-"`
	SubjectTpl *txttpl.Template   `json:"-"`
}

type TxMessage struct {
	//Added by Deepali
	MemberIds  []string `json:"memberIds"`
	MemberType string   `json:"memberType"`
	//End by Deepali
	SubscriberEmails []string `json:"subscriber_emails"`
	SubscriberIDs    []int64  `json:"subscriber_ids"`

	// Deprecated.
	SubscriberEmail string `json:"subscriber_email"`
	SubscriberID    int64  `json:"subscriber_id"`

	TemplateID       json.Number            `json:"templateId"`
	Data             map[string]interface{} `json:"data"`
	FromEmail        string                 `json:"from_email"`
	Headers          Headers                `json:"headers"`
	ContentType      string                 `json:"contentType"`
	Messenger        string                 `json:"messenger"`
	AdditionalValues string                 `json:"additional_values"`

	Subject      string             `json:"subject"`
	Body         []byte             `json:"body"`
	CampaignBody string             `json:"campaign_body"`
	Tpl          *template.Template `json:"-"`
	SubjectTpl   *txttpl.Template   `json:"-"`
	Files        []Files            `json:"files"`
	Encrypted    bool               `json:"encrypted"`
	DirectFcm    interface{}        `json:"direct_fcm"`

	//For sending sms
	RequestParams  string `json:"request_params,omitempty"`
	Url            string `json:"url,omitempty"`
	RequestHeaders string `json:"request_headers,omitempty"`
	RequestBody    string `json:"request_body,omitempty"`
	Method         string `json:"method,omitempty"`

	Content          string                 `json:"-"`
	EmailDetails     EmailDetails           `json:"emailDetails,omitempty"`
	Roles            string                 `json:"roles,omitempty"`
	Subscriber       map[string]interface{} `json:"-"`
	TemplateName     string                 `json:"templateName,omitempty"`
	GatewayId        string                 `json:"gatewayId,omitempty"`
	ProcessId        string                 `json:"process_id,omitempty"`
	DeDuplication    bool                   `json:"de_duplication,omitempty"`
	DuplicationLevel string                 `json:"duplication_level,omitempty"`
	ProcessDuration  int                    `json:"process_duration,omitempty"`
}

type TestTxnMessage struct {
	TxMessage
	AltBody           null.String        `db:"altbody" json:"altbody"`
	TemplateBody      string             `db:"template_body" json:"-"`
	FcmKeyval         json.RawMessage    `db:"fcm_keyval" json:"fcmKeyval"`
	ContentType       string             `db:"content_type" json:"content_type"`
	KeyValueTpl       *txttpl.Template   `json:"-"`
	AltBodyTpl        *template.Template `json:"-"`
	NonEmailTpl       *txttpl.Template   `json:"-"`
	KeyValRenReq      bool               `json:"-"`
	IsCachePresent    bool               `json:"-"`
	IsRealTimePresent bool               `json:"-"`
}

type EmailDetails struct {
	Cc  []string `json:"cc,omitempty"`
	Bcc []string `json:"bcc,omitempty"`
	To  []string `json:"to,omitempty"`
}

type Files struct {
	FileName      string `json:"fileName"`
	FileUrl       string `json:"fileUrl"`
	ContentType   string `json:"contentType"`
	Encoding      string `json:"encoding"`
	ContentLength int64  `json:"contentLength"`
}

type TransactionMsg struct {
	TransactionMsg []TxMessageV1 `json:"txnMessage"`
}

type TxMessageV1 struct {
	//Added by Deepali
	MemberId   string `json:"memberId"`
	MemberType string `json:"memberType"`
	//End by Deepali
	SubscriberEmails []string `json:"subscriber_emails"`
	SubscriberIDs    []int64  `json:"subscriber_ids"`

	// Deprecated.
	SubscriberEmail string `json:"subscriber_email"`
	SubscriberID    int64  `json:"subscriber_id"`

	TemplateID  int                    `json:"template_id"`
	Data        map[string]interface{} `json:"data"`
	FromEmail   string                 `json:"from_email"`
	Headers     Headers                `json:"headers"`
	ContentType string                 `json:"content_type"`
	Messenger   string                 `json:"messenger"`

	Subject    string             `json:"-"`
	Body       []byte             `json:"-"`
	Tpl        *template.Template `json:"-"`
	SubjectTpl *txttpl.Template   `json:"-"`
}

type NatsCampaignPayload struct {
	EventId    string      `json:"eventId"`
	CampaignId string      `json:"campaignId"`
	EventTime  string      `json:"eventTime"`
	EventType  string      `json:"eventType"`
	ReportName null.String `json:"reportName"`
}

type NatsPayload struct {
	EventName           string `json:"eventName"`
	SegmentUpdatedEvent string `json:"segmentUpdatedEvent,omitempty"`
	EventTime           string `json:"eventTime"`
	EventUuid           string `json:"eventUuid"`
	SourceSystem        string `json:"sourceSystem"`
	EventData           string `json:"eventData"`
}

type NatsWebhookPayload struct {
	EventName    string           `json:"eventName"`
	EventTime    string           `json:"eventTime"`
	EventUuid    string           `json:"eventUuid"`
	SourceSystem string           `json:"sourceSystem"`
	EventData    WebhookEventData `json:"eventData"`
}

type WebhookEventData struct {
	Id      string `json:"id"`
	Payload string `json:"payload"`
}

type WebhookLogDto struct {
	MessageId      string `json:"messageId"`
	Provider       string `json:"provider"`
	Messenger      string `json:"messenger"`
	MemberId       string `json:"memberId"`
	RequestId      string `json:"requestId"`
	EventTime      string `json:"eventTime"`
	FailureReason  string `json:"failureReason"`
	DeliveryStatus string `json:"deliveryStatus"`
	ProviderStatus string `json:"providerStatus"`
}

type WebhookLog struct {
	UUID           string      `db:"id" json:"id,omitempty"`
	Provider       string      `db:"provider" json:"provider"`
	Messenger      string      `db:"messenger" json:"messenger"`
	MessageId      null.String `db:"message_id" json:"message_id"`
	ProviderStatus string      `db:"provider_status" json:"provider_status"`
	DeliveryStatus string      `db:"delivery_status" json:"delivery_status"`
	EventTime      null.Time   `db:"event_time" json:"event_time"`
	RequestId      null.String `db:"request_id" json:"request_id"`
	PFailureReason null.String `db:"provider_failure_reason" json:"provider_failure_reason"`
}

type NatsTxnPayload struct {
	EventName    string    `json:"eventName"`
	EventTime    string    `json:"eventTime"`
	EventUuid    string    `json:"eventUuid"`
	SourceSystem string    `json:"sourceSystem"`
	EventData    TxMessage `json:"eventData"`
}
type EventData struct {
	SegmentId          int      `json:"segmentId"`
	SegmentName        string   `json:"segmentName"`
	UpdatedAt          string   `json:"updatedAt"`
	SyncFlag           bool     `json:"communicationServiceFlag"`
	MemberAddedCount   int      `json:"noOfMembersAdded"`
	MemberRemovedCount int      `json:"noOfMembersRemoved"`
	MembersAdded       []string `json:"membersAdded"`
	MembersRemoved     []string `json:"membersRemoved"`
}

type NatsReportEventData struct {
	RequestId        null.String `json:"requestId"`
	JobId            string      `json:"jobId"`
	FileUrl          string      `json:"fileUrl"`
	FileType         string      `json:"fileType"`
	FileName         string      `json:"fileName"`
	IsAdminReport    bool        `json:"adminReport"`
	IsCampaignReport null.Bool   `json:"isCampaignReport"`
}

type NatsMemberPayload struct {
	EventId    string     `json:"eventId"`
	CampaignId string     `json:"campaignId"`
	EventTime  string     `json:"eventTime"`
	EventType  string     `json:"eventType"`
	Subscriber Subscriber `json:"subscriber"`
}

// Nats Broadcast message payload
type BroadcastPayload struct {
	EventId        string `json:"eventId"`
	CampaignId     string `json:"campaignId"`
	EventTime      string `json:"eventTime"`
	EventName      string `json:"eventName"`
	DataId         string `json:"dataId"`
	CampaignStatus string `json:"campaignStatus"`
}

type BroadcastPayloadCustom struct {
	BroadcastPayload
	TraceId string `json:"traceId"`
}

type CampaignsSearchFilter struct {
	Status        string     `json:"status"`
	Channel       string     `json:"channel"`
	SearchQuery   string     `json:"searchQuery"`
	StartedFrom   string     `json:"startedFrom"`
	StartedTo     string     `json:"startedTo"`
	CreatedFrom   string     `json:"createdFrom"`
	CreatedTo     string     `json:"createdTo"`
	EndFrom       string     `json:"endFrom"`
	EndTo         string     `json:"endTo"`
	OrderBy       string     `json:"orderBy"`
	Order         string     `json:"order"`
	RecurringFlag bool       `json:"typeRecurringFlag"`
	PageNo        int        `json:"pageNo"`
	PageSize      int        `json:"pageSize"`
	TemplateName  string     `json:"templateName"`
	FcmKeyVal     *FcmKeyVal `json:"fcmKeyVal"`
	// added for backward campatibility
	Started            string `json:"startedAt"`
	CreatedAt          string `json:"createdAt"`
	EndAt              string `json:"endAt"`
	CampaignType       string `json:"campaignType"`
	IgnoreFrequencyCap bool   `json:"ignoreFrequencyCap"`
}

type SegmentListResponse struct {
	Status      string          `json:"status"`
	RespMessage string          `json:"respMessage"`
	StatusCode  string          `json:"statusCode"`
	Segments    map[int]Segment `json:"segments"`
}

type ContentTemplateDetails struct {
	ID       json.Number `json:"id"`
	Content  string      `json:"content"`
	Name     string      `json:"name"`
	HtmlData string      `json:"htmlData"`
}

type ContentTemplateModel struct {
	ID       int         `db:"id" json:"id"`
	Content  null.String ` db:"content" json:"content"`
	Name     null.String `db:"name" json:"name"`
	HtmlData null.String `db:"html_data" json:"htmlData"`
}

type FcmKeyVal struct {
	Key   string   `json:"key"`
	Value []string `json:"value"`
}

type ListV2 struct {
	ID              int            `db:"id" json:"id"`
	Name            string         `db:"name" json:"name"`
	Tags            pq.StringArray `db:"tags" json:"tags,omitempty"`
	SubscriberCount int            `db:"-" json:"subscriber_count"`
	Status          string         `db:"status" json:"status,omitempty"`
	Total           int            `db:"total" json:"-"`
}

type NotificationFrequencies struct {
	Notification          string                `db:"notification" json:"notification"`
	DisplayName           string                `db:"display_name" json:"display_name"`
	Messenger             string                `db:"messenger" json:"messenger"`
	EnableFreqCap         bool                  `db:"enable_freq_cap" json:"enable_freq_cap"`
	FrequencyDays         int                   `db:"frequency_days" json:"frequency_days"`
	FrequencyValue        int                   `db:"frequency_value" json:"frequency_value"`
	IsNotificationEnabled bool                  `db:"is_notification_enabled" json:"is_notification_enabled"`
	CreatedAt             null.Time             `db:"created_at" json:"created_at"`
	UpdatedAt             null.Time             `db:"updated_at" json:"updated_at"`
	CreatedBy             string                `db:"created_by" json:"created_by"`
	UpdatedBy             *string               `db:"updated_by" json:"updated_by"`
	AdditionalDetails     *NotificationMetaData `db:"additional_details" json:"additional_details,omitempty"`
}

type WebhookConfig struct {
	Id                  null.String       `db:"id" json:"id"`
	Provider            string            `db:"provider" json:"provider"`
	Messenger           string            `db:"messenger" json:"messenger"`
	PayloadType         string            `db:"payload_type" json:"payload_type"`
	AuthType            string            `db:"auth_type" json:"auth_type"`
	AuthSecretPath      string            `db:"auth_secret_path" json:"auth_secret_path"`
	PayloadTemplate     string            `db:"payload_template" json:"payload_template"`
	TransformerTemplate string            `db:"transformer" json:"transformer"`
	CreatedAt           null.Time         `db:"created_at" json:"created_at"`
	UpdatedAt           null.Time         `db:"updated_at" json:"updated_at"`
	CreatedBy           *string           `db:"created_by" json:"created_by"`
	UpdatedBy           *string           `db:"updated_by" json:"updated_by"`
	StatusMapper        types.JSONText    `db:"status_mapper" json:"status_mapper,omitempty"`
	PayloadTpl          *txttpl.Template  `json:"-"`
	TransformTpl        *txttpl.Template  `json:"-"`
	StatusM             map[string]string `json:"-"`
	APIKey              string            `json:"-"`
	IgnoreAuth          bool              `json:"-"`
}

type WebhookRequest struct {
	Id        string `json:"id"`
	Provider  string `json:"provider"`
	Messenger string `json:"messenger"`
}

// markdown is a global instance of Markdown parser and renderer.
var markdown = goldmark.New(
	goldmark.WithParserOptions(
		parser.WithAutoHeadingID(),
	),
	goldmark.WithRendererOptions(
		html.WithXHTML(),
		html.WithUnsafe(),
	),
	goldmark.WithExtensions(
		extension.Table,
		extension.Strikethrough,
		extension.TaskList,
	),
)

// GetIDs returns the list of subscriber IDs.
func (subs Subscribers) GetIDs() []int64 {
	IDs := make([]int64, len(subs))
	for i, c := range subs {
		IDs[i] = c.ID
	}

	return IDs
}

// LoadLists lazy loads the lists for all the subscribers
// in the Subscribers slice and attaches them to their []Lists property.
func (subs Subscribers) LoadLists(stmt *sqlx.Stmt) error {
	var sl []subLists
	err := stmt.Select(&sl, pq.Array(subs.GetIDs()))
	if err != nil {
		return err
	}

	if len(subs) != len(sl) {
		return errors.New("campaign stats count does not match")
	}

	for i, s := range sl {
		if s.SubscriberID == subs[i].ID {
			subs[i].Lists = s.Lists
		}
	}

	return nil
}

// Value returns the JSON marshalled SubscriberAttribs.
func (s JSON) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan unmarshals JSONB from the DB.
func (s JSON) Scan(src interface{}) error {
	if src == nil {
		s = make(JSON)
		return nil
	}

	if data, ok := src.([]byte); ok {
		return json.Unmarshal(data, &s)
	}
	return fmt.Errorf("could not not decode type %T -> %T", src, s)
}

// Scan unmarshals JSONB from the DB.
func (s StringIntMap) Scan(src interface{}) error {
	if src == nil {
		s = make(StringIntMap)
		return nil
	}

	if data, ok := src.([]byte); ok {
		return json.Unmarshal(data, &s)
	}
	return fmt.Errorf("could not not decode type %T -> %T", src, s)
}

// GetIDs returns the list of campaign IDs.
func (camps Campaigns) GetIDs() []int {
	IDs := make([]int, len(camps))
	for i, c := range camps {
		IDs[i] = c.ID
	}

	return IDs
}

// LoadStats lazy loads campaign stats onto a list of campaigns.
func (camps Campaigns) LoadStats(stmt *sqlx.Stmt) error {
	var meta []CampaignMeta
	if err := stmt.Select(&meta, pq.Array(camps.GetIDs())); err != nil {
		return err
	}

	if len(camps) != len(meta) {
		return errors.New("campaign stats count does not match")
	}

	for i, c := range meta {
		if c.CampaignID == camps[i].ID {
			camps[i].Lists = c.Lists
			camps[i].Views = c.Views
			camps[i].Clicks = c.Clicks
			camps[i].Bounces = c.Bounces
		}
	}

	return nil
}

// CompileTemplate compiles a campaign body template into its base
// template and sets the resultant template to Campaign.Tpl.
func (c *Campaign) CompileTemplate(f template.FuncMap, escapeContent bool) error {
	// If the subject line has a template string, compile it.
	c.IsCachePresent = false
	c.IsRealTimePresent = false

	if !c.CampaignParentUUID.Valid || c.CampaignParentUUID.String == "" {
		c.BroadcastName = c.UUID
	} else {
		c.BroadcastName = c.CampaignParentUUID.String
	}

	subj := c.Subject

	if escapeContent {
		subj = strings.ReplaceAll(subj, "\n", "")
		c.Body = escapeSpecificCharacters(c.Body)
	}

	if c.Messenger == "mqtt" {
		subj = strings.TrimSpace(subj)
	}

	for _, r := range regTplFuncs {
		subj = r.regExp.ReplaceAllString(subj, r.replace)
	}

	if strings.Contains(c.Subject, ".Cache.") {
		c.IsCachePresent = true
	}

	if strings.Contains(c.Subject, ".RealTime.") {
		c.IsRealTimePresent = true
	}
	if c.Messenger != "email" {
		subj = unescape.UnescapeString(subj)
	}

	var txtFuncs map[string]interface{} = f
	subjTpl, err := txttpl.New(ContentTpl).Funcs(txtFuncs).Parse(subj)
	if err != nil {
		return fmt.Errorf("error compiling subject: %v", err)
	}
	c.SubjectTpl = subjTpl

	// Compile the base template.
	if c.Messenger == "email" {
		err := c.SetEmailTemplateBody(f)
		if err != nil {
			return err
		}
	} else {
		err := c.SetNonEmailTemplateBody(f, escapeContent)
		if err != nil {
			return err
		}
	}
	// compile ended
	if strings.Contains(c.Body, "{{") {

		if !c.IsCachePresent && strings.Contains(c.Body, ".Cache.") {
			c.IsCachePresent = true
		}

		if !c.IsRealTimePresent && strings.Contains(c.Body, ".RealTime.") {
			c.IsRealTimePresent = true
		}
	}
	if strings.Contains(c.AltBody.String, "{{") {

		if !c.IsCachePresent && strings.Contains(c.AltBody.String, ".Cache.") {
			c.IsCachePresent = true
		}

		if !c.IsRealTimePresent && strings.Contains(c.AltBody.String, ".RealTime.") {
			c.IsRealTimePresent = true
		}

		b := c.AltBody.String

		for _, r := range regTplFuncs {
			b = r.regExp.ReplaceAllString(b, r.replace)
		}
		bTpl, err := template.New(ContentTpl).Funcs(f).Parse(b)
		if err != nil {
			return fmt.Errorf("error compiling alt plaintext message: %v", err)
		}
		c.AltBodyTpl = bTpl
	}

	fmt.Println("After compiling ", c.IsCachePresent, c.IsRealTimePresent)

	if c.FcmKeyval == nil {
		return nil
	}

	jsonResult, err := json.Marshal(c.FcmKeyval)

	if err != nil {
		return nil
	}

	jsonString := string(jsonResult)

	if !strings.Contains(jsonString, "{{") {
		c.KeyValRenReq = false
		var data []map[string]string
		err = json.Unmarshal(jsonResult, &data)

		if err != nil {
			return nil
		}
		result := make(map[string]string)
		for _, item := range data {
			key, ok := item["key"]
			if !ok {
				continue
			}
			value, ok := item["value"]
			if !ok {
				continue
			}
			result[key] = value
		}
		c.Data = result
		return nil
	}

	c.KeyValRenReq = true
	if !c.IsCachePresent && strings.Contains(jsonString, ".Cache.") {
		c.IsCachePresent = true
	}

	if !c.IsRealTimePresent && strings.Contains(jsonString, ".RealTime.") {
		c.IsRealTimePresent = true
	}

	keyValueTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(jsonString)
	if err != nil {
		return fmt.Errorf("error compiling keyvalues : %v", err)
	}
	c.KeyValueTpl = keyValueTpl
	return nil
}

func (c *Campaign) SetNonEmailTemplateBody(f template.FuncMap, escapeContent bool) error {
	body := c.TemplateBody

	if !c.IsCachePresent && strings.Contains(body, ".Cache.") {
		c.IsCachePresent = true
	}

	if !c.IsRealTimePresent && strings.Contains(body, ".RealTime.") {
		c.IsRealTimePresent = true
	}

	for _, r := range regTplFuncs {
		body = r.regExp.ReplaceAllString(body, r.replace)
	}

	baseTPL, err := txttpl.New(BaseTpl).Funcs(f).Parse(body)
	if err != nil {
		return fmt.Errorf("error compiling base template: %v", err)
	}

	// If the format is markdown, convert Markdown to HTML.
	if c.ContentType == CampaignContentTypeMarkdown {
		var b bytes.Buffer
		if err := markdown.Convert([]byte(c.Body), &b); err != nil {
			return err
		}
		body = b.String()
	} else {
		body = unescape.UnescapeString(c.Body)
	}

	if escapeContent {
		body = escapeSpecificCharacters(c.Body)
	}

	// Compile the campaign message.
	for _, r := range regTplFuncs {
		body = r.regExp.ReplaceAllString(body, r.replace)
	}

	msgTpl, err := txttpl.New(ContentTpl).Funcs(f).Parse(body)
	if err != nil {
		return fmt.Errorf("error compiling message: %v", err)
	}

	out, err := baseTPL.AddParseTree(ContentTpl, msgTpl.Tree)
	if err != nil {
		return fmt.Errorf("error inserting child template: %v", err)
	}

	c.NonEmailTpl = out
	return nil
}

func (c *Campaign) SetEmailTemplateBody(f template.FuncMap) error {
	body := c.TemplateBody

	if !c.IsCachePresent && strings.Contains(body, ".Cache.") {
		c.IsCachePresent = true
	}

	if !c.IsRealTimePresent && strings.Contains(body, ".RealTime.") {
		c.IsRealTimePresent = true
	}

	for _, r := range regTplFuncs {
		body = r.regExp.ReplaceAllString(body, r.replace)
	}
	baseTPL, err := template.New(BaseTpl).Funcs(f).Parse(body)
	if err != nil {
		return fmt.Errorf("error compiling base template: %v", err)
	}

	// If the format is markdown, convert Markdown to HTML.
	if c.ContentType == CampaignContentTypeMarkdown {
		var b bytes.Buffer
		if err := markdown.Convert([]byte(c.Body), &b); err != nil {
			return err
		}
		body = b.String()
	} else {
		body = c.Body
	}

	// Compile the campaign message.
	for _, r := range regTplFuncs {
		body = r.regExp.ReplaceAllString(body, r.replace)
	}

	msgTpl, err := template.New(ContentTpl).Funcs(f).Parse(body)
	if err != nil {
		return fmt.Errorf("error compiling message: %v", err)
	}

	out, err := baseTPL.AddParseTree(ContentTpl, msgTpl.Tree)
	if err != nil {
		return fmt.Errorf("error inserting child template: %v", err)
	}

	c.Tpl = out

	return nil
}

func escapeSpecificCharacters(str string) string {
	escapedStr := ""
	for _, c := range str {
		switch c {
		case '"':
			escapedStr += "\""
		case '\n':
			escapedStr += ""
		case '\t':
			escapedStr += ""
		default:
			escapedStr += string(c)
		}
	}
	return escapedStr
}

// ConvertContent converts a campaign's body from one format to another,
// for example, Markdown to HTML.
func (c *Campaign) ConvertContent(from, to string) (string, error) {
	body := c.Body
	for _, r := range regTplFuncs {
		body = r.regExp.ReplaceAllString(body, r.replace)
	}

	// If the format is markdown, convert Markdown to HTML.
	var out string
	if from == CampaignContentTypeMarkdown &&
		(to == CampaignContentTypeHTML || to == CampaignContentTypeRichtext) {
		var b bytes.Buffer
		if err := markdown.Convert([]byte(c.Body), &b); err != nil {
			return out, err
		}
		out = b.String()
	} else {
		return out, errors.New("unknown formats to convert")
	}

	return out, nil

}

// Used for sms gateway templating
type SmsGateway struct {
	Gateway  map[string]interface{}
	Constant map[string]interface{}
}

var (
	smsG         map[string]interface{}
	gatewayProps map[string]interface{}
)

func InitGateway(props map[string]interface{}) {
	gatewayProps = props
}
func GetGatewayProps() map[string]interface{} {
	return gatewayProps
}
func InitSmsGateway(props map[string]interface{}) {
	smsG = props
}
func (data SmsGateway) NonEnv(key string) string {
	if value, ok := data.Gateway[key]; ok {
		return fmt.Sprintf("%s", value)
	}

	return "{{." + key + "}}"
}

func (data SmsGateway) Fun(key string) string {
	if value, ok := data.Gateway[key]; ok {
		return fmt.Sprintf("%s", value)
	}

	return "{{" + key + "}}"
}

// Compile compiles a template body and subject (only for tx templates) and
// caches the templat references to be executed later.
func (t *Template) Compile(f template.FuncMap) error {
	if t.Body != "" {
		t.Body = escapeSpecificCharacters(t.Body)
	}
	if t.Subject != "" {
		t.Subject = strings.ReplaceAll(t.Subject, "\n", "")
	}
	tpl, err := template.New(BaseTpl).Funcs(f).Parse(t.Body)
	if err != nil {
		return fmt.Errorf("error compiling transactional template: %v", err)
	}
	t.Tpl = tpl

	if strings.Contains(t.Body, ".Cache") {
		t.IsCachePresent = true
	}

	// If the subject line has a template string, compile it.
	if strings.Contains(t.Subject, "{{") {
		if strings.Contains(t.Subject, ".Cache.") {
			t.IsCachePresent = true
		}
		subj := t.Subject

		subjTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(subj)
		if err != nil {
			return fmt.Errorf("error compiling subject: %v", err)
		}
		t.SubjectTpl = subjTpl
	}

	// If the additionalValues line has a template string, compile it.
	var keyval string
	if len(t.KeyVal) > 0 {
		data, err := json.Marshal(t.KeyVal)
		if err != nil {
			return fmt.Errorf("error compiling additional values: %v", err)
		}
		keyval = string(data)
	} else {
		keyval = t.AdditionalValues.String
	}
	if strings.Contains(keyval, "{{") {
		if strings.Contains(keyval, ".Cache.") {
			t.IsCachePresent = true
		}
		addValTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(keyval)
		if err != nil {
			return fmt.Errorf("error compiling additional values: %v", err)
		}
		t.AdditionalValuesTpl = addValTpl
	}

	if t.TemplateParams != nil {
		var jsonData map[string]interface{}
		err = json.Unmarshal([]byte(t.TemplateParams), &jsonData)
		if err == nil {
			t.Constant = jsonData
		}
	}

	if len(t.SegmentConfig.ExcludedSegments) > 0 {
		for s := range t.SegmentConfig.ExcludedSegments {
			t.ExcludedSegments = append(t.ExcludedSegments, string(t.SegmentConfig.ExcludedSegments[s].SegmentId))
		}
	}

	if len(t.SegmentConfig.IncludedSegments) > 0 {
		for s := range t.SegmentConfig.IncludedSegments {
			t.IncludedSegments = append(t.IncludedSegments, string(t.SegmentConfig.IncludedSegments[s].SegmentId))
		}
	}

	return nil
}

func (m *TxMessage) Render(sub Subscriber, tpl *Template) error {
	data := struct {
		Subscriber Subscriber
		Tx         *TxMessage
	}{sub, m}

	// Render the body.
	b := bytes.Buffer{}
	if err := tpl.Tpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
		return err
	}
	m.Body = make([]byte, b.Len())
	copy(m.Body, b.Bytes())
	m.Content = b.String()
	b.Reset()

	// If the subject is also a template, render that.
	if tpl.SubjectTpl != nil {
		if err := tpl.SubjectTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
			return err
		}
		m.Subject = b.String()
		b.Reset()
	} else {
		m.Subject = tpl.Subject
	}

	// If the subject is also a template, render that.
	if tpl.AdditionalValuesTpl != nil {
		if err := tpl.AdditionalValuesTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
			return err
		}
		m.AdditionalValues = b.String()
		b.Reset()
	} else {
		m.AdditionalValues = tpl.AdditionalValues.String
	}

	if m.Messenger == "sms" {

		if tpl.RequestBodyTpl != nil {
			if err := tpl.RequestBodyTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
				return err
			}
			m.RequestBody = b.String()
			b.Reset()
		} else {
			m.RequestBody = tpl.RequestBody.String
		}
		if tpl.RequestParamTpl != nil {
			if err := tpl.RequestParamTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
				return err
			}
			m.RequestParams = b.String()
			b.Reset()
		} else {
			m.RequestParams = tpl.RequestParams.String
		}
		if tpl.HeaderTpl != nil {
			if err := tpl.HeaderTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
				return err
			}
			m.RequestHeaders = b.String()
			b.Reset()
		} else {
			m.RequestHeaders = tpl.Headers.String
		}
	}
	return nil
}
func QuoteString(s string) string {
	quoted := strconv.Quote(s)
	return quoted[1 : len(quoted)-1]
}

func (m *TxMessage) RenderV2(sub Subscriber, tpl *Template, gt *GateWayDetails, cache Cache) error {
	data := struct {
		Subscriber Subscriber
		Tx         *TxMessage
		Constant   map[string]interface{}
		Gateway    map[string]interface{}
		Cache      Cache
		Auth       map[string]string
	}{sub, m, tpl.Constant, gatewayProps, cache, nil}

	// Render the body.
	b := bytes.Buffer{}
	if err := tpl.Tpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
		return err
	}
	m.Body = make([]byte, b.Len())
	copy(m.Body, b.Bytes())
	m.Content = b.String()
	b.Reset()

	// If the subject is also a template, render that.
	if tpl.SubjectTpl != nil {
		if err := tpl.SubjectTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
			return err
		}
		m.Subject = b.String()
		b.Reset()
	} else {
		m.Subject = tpl.Subject
	}

	// If the subject is also a template, render that.
	if tpl.AdditionalValuesTpl != nil {
		if err := tpl.AdditionalValuesTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
			return err
		}
		m.AdditionalValues = b.String()
		b.Reset()
	} else {
		m.AdditionalValues = tpl.AdditionalValues.String
	}

	if gt != nil {

		if gt.Method.String != "smtp" && m.Content != "" {
			m.Content = QuoteString(m.Content)
		}

		if gt.AuthConfigInterface != nil {
			data.Auth = gt.AuthConfigInterface.GetValue()
		}

		if gt.RequestBodyTpl != nil {
			if err := gt.RequestBodyTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
				return err
			}
			m.RequestBody = b.String()
			b.Reset()
		} else {
			m.RequestBody = gt.RequestBody.String
		}
		if gt.RequestParamTpl != nil {
			if err := gt.RequestParamTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
				return err
			}
			m.RequestParams = b.String()
			b.Reset()
		} else {
			m.RequestParams = gt.RequestParams.String
		}
		if gt.HeaderTpl != nil {
			if err := gt.HeaderTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
				return err
			}
			m.RequestHeaders = b.String()
			b.Reset()
		} else {
			m.RequestHeaders = gt.Headers.String
		}
	}
	return nil
}

// FirstName splits the name by spaces and returns the first chunk
// of the name that's greater than 2 characters in length, assuming
// that it is the subscriber's first name.
func (s Subscriber) FirstName() string {
	for _, s := range strings.Split(s.Name, " ") {
		if len(s) > 2 {
			return s
		}
	}

	return s.Name
}

// LastName splits the name by spaces and returns the last chunk
// of the name that's greater than 2 characters in length, assuming
// that it is the subscriber's last name.
func (s Subscriber) LastName() string {
	chunks := strings.Split(s.Name, " ")
	for i := len(chunks) - 1; i >= 0; i-- {
		chunk := chunks[i]
		if len(chunk) > 2 {
			return chunk
		}
	}

	return s.Name
}

// Scan implements the sql.Scanner interface.
func (h *Headers) Scan(src interface{}) error {
	var b []byte
	switch src := src.(type) {
	case []byte:
		b = src
	case string:
		b = []byte(src)
	case nil:
		return nil
	}

	if err := json.Unmarshal(b, h); err != nil {
		return err
	}

	return nil
}

// Value implements the driver.Valuer interface.
func (h Headers) Value() (driver.Value, error) {
	if h == nil {
		return nil, nil
	}

	if n := len(h); n > 0 {
		b, err := json.Marshal(h)
		if err != nil {
			return nil, err
		}

		return b, nil
	}

	return "[]", nil
}

func (t *GateWayDetails) CompileV2(f template.FuncMap) error {

	if t.RequestBody.Valid && strings.Contains(t.RequestBody.String, "{{") {

		reqB := strings.ReplaceAll(t.RequestBody.String, "\n", "")
		reqBTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(reqB)
		if err != nil {
			return fmt.Errorf("error compiling requestBody: %v", err)
		}

		t.RequestBodyTpl = reqBTpl
	}

	if t.RequestParams.Valid && strings.Contains(t.RequestParams.String, "{{") {
		reqP := strings.ReplaceAll(t.RequestParams.String, "\n", "")

		reqPTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(reqP)
		if err != nil {
			return fmt.Errorf("error compiling RequestParams: %v", err)
		}

		t.RequestParamTpl = reqPTpl
	}

	if t.Headers.Valid && strings.Contains(t.Headers.String, "{{") {
		header := strings.ReplaceAll(t.Headers.String, "\n", "")
		headerTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(header)
		if err != nil {
			return fmt.Errorf("error compiling header: %v", err)
		}
		t.HeaderTpl = headerTpl
	}

	if t.Method.String != "smtp" && t.Configuration != nil {

		configTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(t.Configuration.String())
		if err != nil {
			return fmt.Errorf("error compiling header: %v", err)
		}

		if configTpl == nil {
			return nil
		}
		data := struct {
			Gateway map[string]interface{}
		}{gatewayProps}

		b := bytes.Buffer{}

		if err := configTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
			return err
		}

		ad := AuthDetailStringfied{}

		fmt.Printf("%v", b.String())

		if err = json.Unmarshal(b.Bytes(), &ad); err != nil {
			return err
		}

		t.AuthConfig = &AuthDetails{
			Messenger: t.Messenger.String,
			Method:    ad.Method,
			Type:      ad.Type,
			Url:       ad.Url,
		}

		if ad.Expiry != "" {
			v, err := strconv.Atoi(ad.Expiry)
			if err == nil {
				t.AuthConfig.Expiry = v
			}
		}

		if ad.RequestBody != "" {
			if err = json.Unmarshal([]byte(ad.RequestBody), &t.AuthConfig.RequestBody); err != nil {
				return err
			}
		}

		if ad.RequestParams != "" {
			if err = json.Unmarshal([]byte(ad.RequestParams), &t.AuthConfig.RequestParams); err != nil {
				return err
			}
		}

		if ad.Headers != "" {
			if err = json.Unmarshal([]byte(ad.Headers), &t.AuthConfig.Headers); err != nil {
				return err
			}
		}

		if ad.Response != "" {
			if err = json.Unmarshal([]byte(ad.Response), &t.AuthConfig.Response); err != nil {
				return err
			}
		}

	}

	return nil
}

func (wc *WebhookConfig) Compile(f template.FuncMap) error {

	if wc.PayloadTemplate != "" && !strings.Contains(wc.PayloadTemplate, "{{") {
		return fmt.Errorf("error compiling PayloadTemplate: PayloadTemplate is not valid")
	}

	pt := strings.ReplaceAll(wc.PayloadTemplate, "\n", "")
	ptTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(pt)
	if err != nil {
		return fmt.Errorf("error compiling payloadTemplate: %v", err)
	}

	wc.PayloadTpl = ptTpl

	if wc.TransformerTemplate != "" && !strings.Contains(wc.TransformerTemplate, "{{") {
		return fmt.Errorf("error compiling webhook template: TransformerTemplate is not valid")
	}

	tt := strings.ReplaceAll(wc.TransformerTemplate, "\n", "")
	ttTpl, err := txttpl.New(BaseTpl).Funcs(txttpl.FuncMap(f)).Parse(tt)
	if err != nil {
		return fmt.Errorf("error compiling transformer template: %v", err)
	}

	wc.TransformTpl = ttTpl

	if wc.StatusMapper != nil {
		var jsonData map[string]string
		err = json.Unmarshal([]byte(wc.StatusMapper), &jsonData)
		if err == nil {
			wc.StatusM = jsonData
		}
	}

	if wc.AuthType == "API_KEY" && wc.AuthSecretPath != "" {
		wc.APIKey = os.Getenv(wc.AuthSecretPath)
	} else {
		wc.IgnoreAuth = true
	}

	return nil
}

func (wr *WebhookRequest) Render(inp, file interface{}, wc *WebhookConfig) error {
	data := struct {
		Input        interface{}
		File         interface{}
		StatusMapper map[string]string
		WebHook      *WebhookRequest
	}{inp, file, wc.StatusM, wr}

	b := bytes.Buffer{}
	if err := wc.PayloadTpl.ExecuteTemplate(&b, BaseTpl, data); err != nil {
		return err
	}
	return nil
}
