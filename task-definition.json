{"family": "List-Monk<PERSON><PERSON>", "containerDefinitions": [{"name": "log_router", "image": "grafana/fluent-bit-plugin-loki:2.9.1", "cpu": 0, "memoryReservation": 50, "portMappings": [], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "user": "0", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "firelens-container", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "firelens"}}, "firelensConfiguration": {"type": "fluentbit", "options": {"enable-ecs-log-metadata": "true"}}}, {"name": "List-Monk-Container", "image": "581541326165.dkr.ecr.ap-south-1.amazonaws.com/listmonk:build", "cpu": 0, "memory": 2048, "memoryReservation": 1024, "portMappings": [{"containerPort": 9000, "hostPort": 9000, "protocol": "tcp"}], "essential": true, "secrets": [], "environment": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"LabelKeys": "container_name,ecs_task_definition,source,ecs_cluster", "Labels": "{job=\"listmonk\"}", "LineFormat": "key_value", "Name": "grafana-loki", "RemoveKeys": "container_id,ecs_task_arn", "Url": "http://internal-ABSA-Dev-Internal-ALB-2064018937.ap-south-1.elb.amazonaws.com/loki/api/v1/push"}}}], "taskRoleArn": "arn:aws:iam::581541326165:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::581541326165:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"operatingSystemFamily": "LINUX"}}