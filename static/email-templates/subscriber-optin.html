{{ define "subscriber-optin" }}
{{ template "header" . }}
<h2>{{ L.Ts "email.optin.confirmSubTitle" }}</h2>
<p>{{ L.Ts "email.optin.confirmSubWelcome" }} {{ .Subscriber.FirstName }}</p>
<p>{{ L.Ts "email.optin.confirmSubInfo" }}</p>
<ul>
    {{ range $i, $l := .Lists }}
        {{ if eq .Type "public" }}
            <li>{{ .Name }}</li>
        {{ else }}
            <li>{{ L.Ts "email.optin.privateList" }}</li>
        {{ end }}
    {{ end }}
</ul>
<p>{{ L.Ts "email.optin.confirmSubHelp" }}</p>
<p>
    <a href="{{ .OptinURL }}" class="button">{{ L.Ts "email.optin.confirmSub" }}</a>
</p>
<a href="{{ .UnsubURL }}">{{ L.T "email.unsub" }}</a>

{{ template "footer" }}
{{ end }}
