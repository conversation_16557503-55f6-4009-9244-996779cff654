{{ define "optin-campaign" }}

<p>{{ L.Ts "email.optin.confirmSubWelcome" }} {{ "{{" }}.Subscriber.FirstName {{ "}}" }}</p>
<p>{{ L.Ts "email.optin.confirmSubInfo" }}</p>
<ul>
    {{ range $i, $l := .Lists }}
        {{ if eq .Type "public" }}
            <li>{{ .Name }}</li>
        {{ else }}
            <li>{{ L.Ts "email.optin.privateList" }}</li>
        {{ end }}
    {{ end }}
</ul>
<p>
    <a class="button" {{ .OptinURLAttr }}>{{ L.Ts "email.optin.confirmSub" }}</a>
</p>
{{ end }}
