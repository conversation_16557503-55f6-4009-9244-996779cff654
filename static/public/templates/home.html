{{ define "home" }}
{{ template "header" .}}

<section class="center">
	<form class="form" method="get">
		<div>
			<input id="email" required="true" type='email'
				placeholder='Enter Email Address' autofocus="true">
			<br />
			<input id="password"  type='password' required='true'
				placeholder='Enter Password' autofocus='true'>
			<br />
			<p>
				<button type="submit" class="button">Login</button>
			</p>
		</div>

		
	<div class="home-options">
		{{ if .EnablePublicSubPage }}
			<a href="{{ .RootURL }}/subscription/form">{{ L.T "public.sub" }}</a>
		{{ end }}
		{{ if .EnablePublicArchive }}
		    <a href="{{ .RootURL }}/archive">{{ L.T "public.archiveTitle" }}</a>
		{{ end }}
	</div>
	</form>
	<script>
		function handleData() {
			var a = document.querySelector("input[name=email]").value;
			var f = document.querySelector("input[name=password]").value
			// const data = {
			// 	email: a,
			// 	password: f
			// }
			// this.$api.login(data).then((d) => {
			// 	this.$router.push({ name: 'dashboard' });
			// });
			const data = {
				emailss: email,
				passwordss: password
			};
			console.log(data);
			const response = fetch(`https://dev-4.mintoak.com/OneAppAuth/adminPortalLogin`, {
				method: 'GET',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ data })
			})
			console.log(response.json());
			return true;
		}

	</script>
</section>



{{ template "footer" .}}
{{ end }}