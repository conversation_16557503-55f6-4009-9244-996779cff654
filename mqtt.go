package main

import (
	"fmt"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	push "github.com/phuslu/log"
)

var (
	mqttClient mqtt.Client
	qos        int
	mutex      sync.Mutex
)

func setMqttClient(client mqtt.Client, q int) {
	mutex.Lock()
	mqttClient = client
	qos = q
	mutex.Unlock()
}

func reconnectMqttClient(logger push.Logger) {
	mutex.Lock()
	defer mutex.Unlock()

	if mqttClient != nil && mqttClient.IsConnected() {
		logger.Info().Msg("MQTT client reconnected by another thread. Skipping reconnection.")
		return
	}
	logger.Info().Msg("Attempting to reconnect to MQTT server...")
	if mqttClient != nil {
		mqttClient.Connect()
		if mqttClient.IsConnected() {
			logger.Info().Msg("Successfully reconnected to MQTT server.")
		} else {
			logger.Error().Msg("Failed to reconnect to MQTT server.")
		}
	} else {
		logger.Error().Msg("MQTT client is not set")
	}

}

func PublishToMqtt(topic, message string, logger push.Logger) error {

	if mqttClient == nil || !mqttClient.IsConnected() {
		logger.Warn().Msg("MQTT client is not connected. Attempting to reconnect...")
		reconnectMqttClient(logger)
	}

	if mqttClient == nil || !mqttClient.IsConnected() {
		return fmt.Errorf("failed to reconnect to MQTT server")
	}

	token := mqttClient.Publish(topic, byte(qos), false, message)
	token.WaitTimeout(2 * time.Minute)
	if token.Error() != nil {
		logger.Error().Msgf("Failed to publish to topic '%s': %v", topic, token.Error())
		return token.Error()
	}

	logger.Info().Msgf("Successfully published message to topic '%s'", topic)
	return nil
}

func initMqttServer() {

	var mqttConfig struct {
		ServerUrl string `koanf:"serverUrl"`
		Port      int    `koanf:"port"`
		Qos       int    `koanf:"qos"`
		Username  string `koanf:"username"`
		Password  string `koanf:"password"`
		KeepAlive uint16 `koanf:"keepAlive"`
	}
	if err := ko.Unmarshal("mqttConfig", &mqttConfig); err != nil {
		lo.Fatal().Msgf("error loading mqttConfig from properites: %v", err)
	}

	lo.Info().Msgf("Using MQTT settings %v", mqttConfig)

	random, err := generateRandomNumber()

	if err != nil {
		random = ""
	}

	clientID := fmt.Sprintf("communication-service-%v%v", time.Now().UnixMicro(), random)

	lo.Info().Msgf("client name used for mqtt %v", clientID)

	broker := fmt.Sprintf("tcp://%s:%d", mqttConfig.ServerUrl, mqttConfig.Port)

	opts := mqtt.NewClientOptions()
	opts.AddBroker(broker)
	opts.SetClientID(clientID)
	opts.SetUsername(mqttConfig.Username)
	opts.SetPassword(mqttConfig.Password)
	opts.SetKeepAlive(time.Duration(mqttConfig.KeepAlive) * time.Second)

	// Add connection lost handler
	opts.OnConnectionLost = func(client mqtt.Client, err error) {
		lo.Warn().Msgf("MQTT connection lost: %v", err)
		reconnectMqttClient(lo) // Attempt to reconnect
	}

	client := mqtt.NewClient(opts)

	// Connect to MQTT broker
	token := client.Connect()
	token.Wait()
	if token.Error() != nil {
		lo.Fatal().Msgf("Failed to connect to MQTT broker: %v", token.Error())
	}

	lo.Info().Msgf("Connected to MQTT broker at %s", broker)
	setMqttClient(client, mqttConfig.Qos)
}
