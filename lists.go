package main

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

// handleGetLists retrieves lists with additional metadata like subscriber counts. This may be slow.
func handleGetLists(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
		pg  = app.paginator.NewFromURL(c.Request().URL.Query())

		query      = strings.TrimSpace(c.FormValue("query"))
		orderBy    = c.FormValue("order_by")
		order      = c.FormValue("order")
		minimal, _ = strconv.ParseBool(c.FormValue("minimal"))
		listID, _  = strconv.Atoi(c.Param("id"))
		internal   = c.FormValue("internal")
		out        models.PageResults
		logger     = c.Get("logger").(push.Logger)
	)

	//Fetch list which is present in DB only
	if internal == "true" {
		return fetchDbLists(c, app, &out, logger)
	}

	// Fetch one list.
	single := false
	if listID > 0 {
		single = true
	}

	if single {
		out, err := app.core.GetList(listID, "", logger)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, okResp{out})
	}

	// Minimal query simply returns the list of all lists without JOIN subscriber counts. This is fast.
	if !single && minimal {
		res, err := app.core.GetLists("", logger)
		if err != nil {
			return err
		}

		/*
			Fetching Segment List from Segmentation2 via API call
		*/
		dataMap, err := getSegmentList(app, tracer.WrapEchoContextLogger(c))
		if err != nil {
			return err
		}

		var list models.List
		for _, item := range dataMap {
			list.Name = item.SegmentName
			list.ID = int(item.SegmentId)
			list.Description = "Segment"
			res = append(res, list)
		}

		res = removeDuplicates(res)
		// Ended by Deepali

		// Meta.
		out.Results = res
		out.Total = len(res)
		out.Page = 1
		out.PerPage = out.Total

		return c.JSON(http.StatusOK, okResp{out})
	}

	// Full list query.
	res, total, err := app.core.QueryLists(query, orderBy, order, pg.Offset, pg.Limit, logger)
	if err != nil {
		return err
	}

	segmentDataMap := make(map[string]models.Segment)

	/*
		Fetching Segment List from Segmentation2 via API call
	*/
	dataMap, err := getSegmentList(app, tracer.WrapEchoContextLogger(c))
	if err != nil {
		return err
	}

	for _, segment := range dataMap {
		segmentDataMap[segment.SegmentName] = segment
	}

	for i, item := range res {
		if item.SubscriberCount == 0 {
			if segment, exists := segmentDataMap[item.Name]; exists {
				res[i].SubscriberCount = segment.MemberCount
			}
		}
	}

	var list models.ListV2
	for _, item := range dataMap {
		list.Name = item.SegmentName
		list.ID = int(item.SegmentId)
		res = append(res, list)
	}

	res = removeDuplicatesV2(res)
	// Ended by Deepali

	if single && len(res) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.list}"))
	}

	if single {
		return c.JSON(http.StatusOK, okResp{res[0]})
	}

	out.Query = query
	out.Results = res
	out.Total = total
	out.Page = pg.Page
	out.PerPage = pg.PerPage

	return c.JSON(http.StatusOK, okResp{out})
}

// Added by Deepali

func removeDuplicates(segmentList []models.List) []models.List {
	allKeys := make(map[string]bool)
	list := []models.List{}
	for _, item := range segmentList {
		if _, value := allKeys[item.Name]; !value {
			allKeys[item.Name] = true
			list = append(list, item)
		}
	}
	return list
}

func convertRedisMap(m map[string]models.Segment) (values []models.Segment) {
	for k := range m {
		values = append(values, m[k])
	}
	return values
}

//Ended by Deepali

// handleCreateList handles list creation.
func handleCreateList(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		l      = models.List{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&l); err != nil {
		return err
	}

	// Validate.
	if !strHasLen(l.Name, 1, stdInputMaxLen) {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("lists.invalidName"))
	}
	//Added by Deepali for identifier between segmnet app and listmonk app
	l.Description = "Listmonk"
	//Ended by Deepali
	out, err := app.core.CreateList(l, logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpdateList handles list modification.
func handleUpdateList(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	// Incoming params.
	var l models.List
	if err := c.Bind(&l); err != nil {
		return err
	}

	// Validate.
	if !strHasLen(l.Name, 1, stdInputMaxLen) {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("lists.invalidName"))
	}

	out, err := app.core.UpdateList(id, l, logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleDeleteLists handles list deletion, either a single one (ID in the URI), or a list.
func handleDeleteLists(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.ParseInt(c.Param("id"), 10, 64)
		ids    []int
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 && len(ids) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	if id > 0 {
		ids = append(ids, int(id))
	}

	if err := app.core.DeleteLists(ids, logger); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

func fetchDbLists(c echo.Context, app *App, out *models.PageResults, logger push.Logger) error {
	res, err := app.core.GetLists("", logger)
	if err != nil {
		return err
	}
	if len(res) == 0 {
		return c.JSON(http.StatusOK, okResp{[]struct{}{}})
	}
	out.Results = res
	out.Total = len(res)
	out.Page = 1
	out.PerPage = out.Total

	return c.JSON(http.StatusOK, okResp{out})
}

func getSegmentList(app *App, ctx context.Context) (map[int]models.Segment, error) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	config := utils.GetConfigProperties()
	url := config.ServerConfig.SegmentServiceUrl + "SEG2/api/legacy/getSegments"

	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		logger.Error().Msgf("Error creating request: %v", err)
		return nil, err
	}

	client, err := tracer.GetHttpClient(config.ServerConfig.SkipSSLCheck)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return nil, err
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("Error: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %v", resp.Status)
	logger.Info().Msgf("response Headers: %v", resp.Header)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error().Msgf("Error reading response body: %v", err)
		return nil, err
	}

	var response models.SegmentListResponse
	err = json.Unmarshal([]byte(body), &response)
	if err != nil {
		logger.Error().Msgf("Error: %v", err)
		return nil, err
	}

	return response.Segments, nil
}

func removeDuplicatesV2(segmentList []models.ListV2) []models.ListV2 {
	allKeys := make(map[string]bool)
	list := []models.ListV2{}
	for _, item := range segmentList {
		if _, value := allKeys[item.Name]; !value {
			allKeys[item.Name] = true
			list = append(list, item)
		}
	}
	return list
}
