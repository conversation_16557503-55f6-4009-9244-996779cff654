// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/login": {
            "post": {
                "description": "Logs in a user with email and password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User login",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "login",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successful login",
                        "schema": {
                            "$ref": "#/definitions/main.okResp"
                        }
                    },
                    "400": {
                        "description": "Invalid input",
                        "schema": {
                            "$ref": "#/definitions/main.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/main.HTTPError"
                        }
                    }
                }
            }
        },
        "/api/templates": {
            "get": {
                "description": "Fetches one or all email templates based on the provided parameters. If an ID is provided, it retrieves a specific template; otherwise, it returns a list of all templates.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "templates"
                ],
                "summary": "Get email templates",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Template ID",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "No Body Flag",
                        "name": "no_body",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successful Operation\"       // The response for a successful operation",
                        "schema": {
                            "$ref": "#/definitions/main.getTemplatesResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request\"             // The response for an invalid request",
                        "schema": {
                            "$ref": "#/definitions/main.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error\"   // The response for an internal server error",
                        "schema": {
                            "$ref": "#/definitions/main.HTTPError"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "main.HTTPError": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.LoginRequest": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                }
            }
        },
        "main.getTemplatesResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "DefaultPrefs": {
                                "type": "object",
                                "properties": {
                                    "email": {
                                        "type": "boolean"
                                    },
                                    "fcm": {
                                        "type": "boolean"
                                    },
                                    "sms": {
                                        "type": "boolean"
                                    }
                                }
                            },
                            "additional_values": {
                                "type": "string"
                            },
                            "body": {
                                "type": "string"
                            },
                            "category": {
                                "type": "integer"
                            },
                            "created_at": {
                                "type": "string"
                            },
                            "headers": {
                                "type": "string"
                            },
                            "id": {
                                "type": "integer"
                            },
                            "is_default": {
                                "type": "boolean"
                            },
                            "keyval": {
                                "type": "string"
                            },
                            "message_type": {
                                "type": "string"
                            },
                            "method": {
                                "type": "string"
                            },
                            "name": {
                                "type": "string"
                            },
                            "registeredTemplate": {
                                "type": "string"
                            },
                            "request_body": {
                                "type": "string"
                            },
                            "request_params": {
                                "type": "string"
                            },
                            "subject": {
                                "type": "string"
                            },
                            "template_params": {
                                "type": "object"
                            },
                            "type": {
                                "type": "string"
                            },
                            "updated_at": {
                                "type": "string"
                            },
                            "url": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "main.okResp": {
            "type": "object",
            "properties": {
                "data": {}
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
