definitions:
  main.HTTPError:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  main.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    type: object
  main.getTemplatesResponse:
    properties:
      data:
        items:
          properties:
            DefaultPrefs:
              properties:
                email:
                  type: boolean
                fcm:
                  type: boolean
                sms:
                  type: boolean
              type: object
            additional_values:
              type: string
            body:
              type: string
            category:
              type: integer
            created_at:
              type: string
            headers:
              type: string
            id:
              type: integer
            is_default:
              type: boolean
            keyval:
              type: string
            message_type:
              type: string
            method:
              type: string
            name:
              type: string
            registeredTemplate:
              type: string
            request_body:
              type: string
            request_params:
              type: string
            subject:
              type: string
            template_params:
              type: object
            type:
              type: string
            updated_at:
              type: string
            url:
              type: string
          type: object
        type: array
    type: object
  main.okResp:
    properties:
      data: {}
    type: object
info:
  contact: {}
paths:
  /api/login:
    post:
      consumes:
      - application/json
      description: Logs in a user with email and password
      parameters:
      - description: Login credentials
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/main.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successful login
          schema:
            $ref: '#/definitions/main.okResp'
        "400":
          description: Invalid input
          schema:
            $ref: '#/definitions/main.HTTPError'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/main.HTTPError'
      summary: User login
      tags:
      - Authentication
  /api/templates:
    get:
      consumes:
      - application/json
      description: Fetches one or all email templates based on the provided parameters.
        If an ID is provided, it retrieves a specific template; otherwise, it returns
        a list of all templates.
      parameters:
      - description: Template ID
        in: query
        name: id
        type: integer
      - description: No Body Flag
        in: query
        name: no_body
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Successful Operation"       // The response for a successful
            operation
          schema:
            $ref: '#/definitions/main.getTemplatesResponse'
        "400":
          description: Bad Request"             // The response for an invalid request
          schema:
            $ref: '#/definitions/main.HTTPError'
        "500":
          description: Internal Server Error"   // The response for an internal server
            error
          schema:
            $ref: '#/definitions/main.HTTPError'
      summary: Get email templates
      tags:
      - templates
swagger: "2.0"
