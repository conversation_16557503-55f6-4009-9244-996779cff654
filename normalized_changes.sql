--
-- Name: bounce_type; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE bounce_type AS ENUM (
    'soft',
    'hard',
    'complaint'
);

--
-- Name: campaign_status; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE campaign_status AS ENUM (
    'draft',
    'running',
    'scheduled',
    'paused',
    'cancelled',
    'finished'
);

--
-- Name: campaign_type; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE campaign_type AS ENUM (
    'regular',
    'optin'
);


--
-- Name: content_type; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE content_type AS ENUM (
    'richtext',
    'html',
    'plain',
    'markdown'
);

--
-- Name: list_optin; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE list_optin AS ENUM (
    'single',
    'double'
);

--
-- Name: list_type; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE list_type AS ENUM (
    'public',
    'private',
    'temporary'
);

--
-- Name: listmonk_method_type; Type: TYPE; Schema: public; Owner: root
--
CREATE TYPE listmonk_method_type AS ENUM (
    'get',
    'post',
    'put'
);

--
-- Name: subscriber_status; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE subscriber_status AS ENUM (
    'enabled',
    'disabled',
    'blocklisted'
);

--
-- Name: subscription_status; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE subscription_status AS ENUM (
    'unconfirmed',
    'confirmed',
    'unsubscribed'
);

--
-- Name: template_type; Type: TYPE; Schema: public; Owner: root
--

CREATE TYPE template_type AS ENUM (
    'campaign',
    'tx'
);


--
-- Name: awsdms_intercept_ddl(); Type: FUNCTION; Schema: public; Owner: root
--

CREATE FUNCTION awsdms_intercept_ddl() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
  declare _qry text;
BEGIN
  if (tg_tag='CREATE TABLE' or tg_tag='ALTER TABLE' or tg_tag='DROP TABLE') then
	    SELECT current_query() into _qry;
	    insert into awsdms_ddl_audit
	    values
	    (
	    default,current_timestamp,current_user,cast(TXID_CURRENT()as varchar(16)),tg_tag,0,'',current_schema,_qry
	    );
	    delete from awsdms_ddl_audit;
 end if;
END;
$$;

--
-- Name: preference_history(); Type: FUNCTION; Schema: public; Owner: root
--

CREATE FUNCTION preference_history() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
			BEGIN
        		INSERT INTO subscriber_preference_logs (subscriber_id, category_id, channel,value, created_at, created_by)
        			VALUES (NEW.subscriber_id, NEW.category_id, NEW.channel, NEW.value, NOW(), NEW.updated_by);
    			RETURN NEW;
			END;
	$$;

--
-- Name: attribs_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE attribs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: awsdms_ddl_audit; Type: TABLE; Schema: public; Owner: root
--
CREATE TABLE awsdms_ddl_audit (
    c_key bigint NOT NULL,
    c_time timestamp without time zone,
    c_user character varying(64),
    c_txn character varying(16),
    c_tag character varying(24),
    c_oid integer,
    c_name character varying(64),
    c_schema character varying(64),
    c_ddlqry text
);

--
-- Name: awsdms_ddl_audit_c_key_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE awsdms_ddl_audit_c_key_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Dependencies: 236
-- Name: awsdms_ddl_audit_c_key_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE awsdms_ddl_audit_c_key_seq OWNED BY awsdms_ddl_audit.c_key;


--
-- Name: awsdms_heartbeat; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE awsdms_heartbeat (
    hb_key integer NOT NULL,
    hb_created_at timestamp without time zone,
    hb_created_by character varying(64),
    hb_last_heartbeat_at timestamp without time zone,
    hb_last_heartbeat_by character varying(64)
);

--
-- Name: awsdms_heartbeat_hb_key_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE awsdms_heartbeat_hb_key_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: awsdms_heartbeat_hb_key_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE awsdms_heartbeat_hb_key_seq OWNED BY awsdms_heartbeat.hb_key;


--
-- TOC entry 201 (class 1259 OID 1096633)
-- Name: bounces; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE bounces (
    id integer NOT NULL,
    subscriber_id integer NOT NULL,
    campaign_id integer,
    type bounce_type DEFAULT 'hard'::bounce_type NOT NULL,
    source text DEFAULT ''::text NOT NULL,
    meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

--
-- Name: bounces_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE bounces_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: bounces_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE bounces_id_seq OWNED BY bounces.id;


--
-- Name: campaign_attribs_master; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE campaign_attribs_master (
    id integer DEFAULT nextval('attribs_id_seq'::regclass) NOT NULL,
    campaign_id integer NOT NULL,
    attribs jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: campaign_lists; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE campaign_lists (
    id bigint NOT NULL,
    campaign_id integer NOT NULL,
    list_id integer,
    list_name text DEFAULT ''::text NOT NULL
);


--
-- Name: campaign_lists_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE campaign_lists_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: campaign_lists_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE campaign_lists_id_seq OWNED BY campaign_lists.id;


--
-- Name: campaign_targets; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE campaign_targets (
    id bigint NOT NULL,
    campaign_id integer,
    target text,
    created_at timestamp with time zone DEFAULT now(),
    terminal_id character varying,
    fcm_token character varying,
    notification_label character varying
);

--
-- Name: campaign_targets_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE campaign_targets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: campaign_targets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE campaign_targets_id_seq OWNED BY campaign_targets.id;


--
-- Name: campaign_views; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE campaign_views (
    id bigint NOT NULL,
    campaign_id integer NOT NULL,
    subscriber_id integer,
    created_at timestamp with time zone DEFAULT now()
);

--
-- Name: campaign_views_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE campaign_views_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: campaign_views_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE campaign_views_id_seq OWNED BY campaign_views.id;


--
-- Name: campaigns; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE campaigns (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    name text NOT NULL,
    subject text NOT NULL,
    from_email text NOT NULL,
    body text NOT NULL,
    altbody text,
    content_type content_type DEFAULT 'richtext'::content_type NOT NULL,
    send_at timestamp with time zone,
    headers jsonb DEFAULT '[]'::jsonb NOT NULL,
    status campaign_status DEFAULT 'draft'::campaign_status NOT NULL,
    tags character varying(100)[],
    type campaign_type DEFAULT 'regular'::campaign_type,
    messenger text NOT NULL,
    template_id integer DEFAULT 1,
    to_send integer DEFAULT 0 NOT NULL,
    sent integer DEFAULT 0 NOT NULL,
    max_subscriber_id integer DEFAULT 0 NOT NULL,
    last_subscriber_id integer DEFAULT 0 NOT NULL,
    archive boolean DEFAULT false NOT NULL,
    archive_template_id integer DEFAULT 1,
    archive_meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    started_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    cron text,
    fcm_image text DEFAULT ''::text,
    fcm_keyval jsonb DEFAULT '{}'::jsonb,
    fcm_roles text DEFAULT ''::text,
    category integer DEFAULT 0,
    fcm_cta text DEFAULT ''::text,
    de_duplication boolean DEFAULT false,
    duplication_level text,
    process_status text DEFAULT ''::text,
    end_at timestamp with time zone,
    file_path text,
    paused boolean DEFAULT false
);

--
-- Name: campaigns_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE campaigns_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Dependencies: 209
-- Name: campaigns_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE campaigns_id_seq OWNED BY campaigns.id;


--
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE categories_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;

--
-- Name: categories; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE categories (
    id integer DEFAULT nextval('categories_id_seq'::regclass) NOT NULL,
    name text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    is_visible_to_merchant jsonb DEFAULT '{}'::jsonb,
    is_toggleable jsonb DEFAULT '{}'::jsonb,
    description character varying(255) DEFAULT ''::character varying,
    notification_defaults jsonb DEFAULT '{}'::jsonb,
    is_default boolean DEFAULT false NOT NULL
);

--
-- Name: gateway_details; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE gateway_details (
    id integer NOT NULL,
    method listmonk_method_type DEFAULT 'post'::listmonk_method_type NOT NULL,
    request_params text,
    headers text,
    url text,
    request_body text,
    updated_at timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now(),
    messenger character varying(20),
    name character varying(20)
);

--
-- Name: gateway_details_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE gateway_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: gateway_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE gateway_details_id_seq OWNED BY gateway_details.id;


--
-- Name: link_clicks; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE link_clicks (
    id bigint NOT NULL,
    campaign_id integer,
    link_id integer NOT NULL,
    subscriber_id integer,
    created_at timestamp with time zone DEFAULT now()
);

--
-- TOC entry 213 (class 1259 OID 1096708)
-- Name: link_clicks_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE link_clicks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


-- Name: link_clicks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE link_clicks_id_seq OWNED BY link_clicks.id;


--
-- Name: links; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE links (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    url text NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

--
-- Name: links_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE links_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: links_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE links_id_seq OWNED BY links.id;


--
-- Name: lists; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE lists (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    name text NOT NULL,
    type list_type NOT NULL,
    optin list_optin DEFAULT 'single'::list_optin NOT NULL,
    tags character varying(100)[],
    description text DEFAULT ''::text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    status character varying(50) DEFAULT ''::character varying
);

--
-- Name: lists_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE lists_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: lists_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE lists_id_seq OWNED BY lists.id;


--
-- Name: media; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE media (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    provider text DEFAULT ''::text NOT NULL,
    filename text NOT NULL,
    thumb text NOT NULL,
    meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

--
-- Name: media_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE media_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: media_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE media_id_seq OWNED BY media.id;


--
-- Name: settings; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE settings (
    key text NOT NULL,
    value jsonb DEFAULT '{}'::jsonb NOT NULL,
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: subscriber_lists; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE subscriber_lists (
    subscriber_id integer NOT NULL,
    list_id integer NOT NULL,
    status subscription_status DEFAULT 'unconfirmed'::subscription_status NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

--
-- Name: subscriber_preference_logs; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE subscriber_preference_logs (
    subscriber_id integer NOT NULL,
    category_id integer NOT NULL,
    channel character varying(20) NOT NULL,
    value boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    created_by character varying(20)
);

--
-- Name: subscriber_preferences; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE subscriber_preferences (
    subscriber_id integer NOT NULL,
    category_id integer NOT NULL,
    channel character varying(20) NOT NULL,
    value boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by character varying(20),
    updated_by character varying(20)
);

--
-- Name: subscribers; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE subscribers (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    email text,
    name text NOT NULL,
    attribs jsonb DEFAULT '{}'::jsonb NOT NULL,
    status subscriber_status DEFAULT 'enabled'::subscriber_status NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    type text
);

--
-- Name: subscribers_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE subscribers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: subscribers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE subscribers_id_seq OWNED BY subscribers.id;


--
-- Name: template_details; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE template_details (
    id integer NOT NULL,
    template_id integer NOT NULL,
    registered_template_id character varying(100) NOT NULL,
    gateway character varying(20),
    type character varying(10),
    gateway_details_id integer NOT NULL,
    updated_at timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now(),
    template_params jsonb DEFAULT '{}'::jsonb NOT NULL
);

--
-- Name: template_details_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE template_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: template_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE template_details_id_seq OWNED BY template_details.id;


--
-- Name: templates; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE templates (
    id integer NOT NULL,
    name text NOT NULL,
    type template_type DEFAULT 'campaign'::template_type NOT NULL,
    subject text NOT NULL,
    body text NOT NULL,
    is_default boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    category integer DEFAULT 0,
    additional_values jsonb DEFAULT '{}'::jsonb,
    message_type character varying(20) DEFAULT 'notification'::character varying,
    template_params jsonb DEFAULT '{}'::jsonb NOT NULL
);

--
-- Name: templates_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE templates_id_seq OWNED BY templates.id;


--
-- Name: awsdms_ddl_audit c_key; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY awsdms_ddl_audit ALTER COLUMN c_key SET DEFAULT nextval('awsdms_ddl_audit_c_key_seq'::regclass);


--
-- Name: awsdms_heartbeat hb_key; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY awsdms_heartbeat ALTER COLUMN hb_key SET DEFAULT nextval('awsdms_heartbeat_hb_key_seq'::regclass);


--
-- Name: bounces id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY bounces ALTER COLUMN id SET DEFAULT nextval('bounces_id_seq'::regclass);


--
-- Name: campaign_lists id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_lists ALTER COLUMN id SET DEFAULT nextval('campaign_lists_id_seq'::regclass);


--
-- Name: campaign_targets id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_targets ALTER COLUMN id SET DEFAULT nextval('campaign_targets_id_seq'::regclass);


--
-- Name: campaign_views id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_views ALTER COLUMN id SET DEFAULT nextval('campaign_views_id_seq'::regclass);


-- Name: campaigns id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaigns ALTER COLUMN id SET DEFAULT nextval('campaigns_id_seq'::regclass);

--
-- Name: gateway_details id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY gateway_details ALTER COLUMN id SET DEFAULT nextval('gateway_details_id_seq'::regclass);


--
-- Name: link_clicks id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY link_clicks ALTER COLUMN id SET DEFAULT nextval('link_clicks_id_seq'::regclass);


--
-- Name: links id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY links ALTER COLUMN id SET DEFAULT nextval('links_id_seq'::regclass);


--
-- Name: lists id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY lists ALTER COLUMN id SET DEFAULT nextval('lists_id_seq'::regclass);

-- Name: media id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY media ALTER COLUMN id SET DEFAULT nextval('media_id_seq'::regclass);


--
-- Name: subscribers id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscribers ALTER COLUMN id SET DEFAULT nextval('subscribers_id_seq'::regclass);


--
-- Name: template_details id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY template_details ALTER COLUMN id SET DEFAULT nextval('template_details_id_seq'::regclass);

-- Name: templates id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY templates ALTER COLUMN id SET DEFAULT nextval('templates_id_seq'::regclass);

-- Data for Name: awsdms_heartbeat; Type: TABLE DATA; Schema: public; Owner: root
--

INSERT INTO awsdms_heartbeat VALUES (1, '2023-12-18 00:37:43.065115', 'root', '2024-01-25 00:42:47.740932', 'root');


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: root
--

INSERT INTO categories VALUES (1, 'Default Category', '2023-12-04 23:21:14.968309-05:30', '2023-12-04 23:21:14.968309-05:30', '{"fcm": true, "pax": true, "sms": true, "email": true}', '{"fcm": true, "pax": true, "sms": true, "email": true}', 'This is default category', '{"fcm": true, "pax": true, "sms": true, "email": true}', true);

--
-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: root
--

INSERT INTO settings VALUES ('migrations', '["v2.3.0", "v2.4.0", "v2.4.1", "v2.5.0", "v2.5.1", "v2.6.0", "v2.6.1", "v2.6.2", "v2.6.3", "v2.6.4", "v2.6.5", "v2.6.6", "v2.6.7"]', '2023-01-02 16:03:06.522589-05:30');
INSERT INTO settings VALUES ('smtp', '[{"host": "email-smtp.ap-south-1.amazonaws.com", "port": 587, "uuid": "63054b13-6f38-4a58-af65-01dede4e24fe", "enabled": true, "password": "BF/Q3aDykkxTxLUFDtBs2g2AU81UFkfpqnzC73alBh47", "tls_type": "STARTTLS", "username": "AKIAZOTBARATIAUNA6NG", "max_conns": 10, "idle_timeout": "15s", "wait_timeout": "5s", "auth_protocol": "plain", "email_headers": [], "hello_hostname": "<EMAIL>", "max_msg_retries": 2, "tls_skip_verify": false}, {"host": "smtp.gmail.com", "port": 465, "uuid": "c8b980b0-cd80-4b3c-9ad8-61f2b64f5adf", "enabled": false, "password": "password", "tls_type": "TLS", "username": "<EMAIL>", "max_conns": 10, "idle_timeout": "15s", "wait_timeout": "5s", "auth_protocol": "login", "email_headers": [], "hello_hostname": "", "max_msg_retries": 2, "tls_skip_verify": false}]', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.lang', '"en"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.logo_url', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.root_url', '"http://listmonk-service.uat.svc.cluster.local:8515"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.count', '2', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.site_name', '"Mailing list"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.action', '"blocklist"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.url', '"https://hdfcbadgeimages.s3.ap-south-1.amazonaws.com/"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('messengers', '[{"name": "fcm", "uuid": "3a2e0cb2-4722-4c45-a753-00780c3b5284", "enabled": true, "timeout": "11s", "root_url": "http://listmonk-service.uat.svc.cluster.local:8515/api/sendFcmMsg", "username": "", "max_conns": 35, "max_msg_retries": 2}, {"name": "sms", "uuid": "7b1a753c-fc3f-4264-9ece-8bebf4df4c15", "enabled": true, "timeout": "5s", "root_url": "http://listmonk-service.uat.svc.cluster.local:8515/api/sendSms", "username": "", "max_conns": 25, "max_msg_retries": 2}, {"name": "pax", "uuid": "f3505e33-cbf7-49fa-b606-b4d004a72c52", "enabled": true, "timeout": "5s", "root_url": "http://pax-messenger-service.uat.svc.cluster.local:8510/api/sendPaxNotification", "username": "", "max_conns": 25, "max_msg_retries": 2}]', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.batch_size', '700', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.from_email', '"hdfcuatlistmonk <<EMAIL>>"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.enabled', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.concurrency', '21', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.favicon_url', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.provider', '"s3"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.message_rate', '30', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.mailboxes', '[{"host": "pop.yoursite.com", "port": 995, "type": "pop", "uuid": "fb4df42d-3fb7-4a09-91d0-f8ddc433ff22", "enabled": false, "password": "password", "username": "username", "return_path": "<EMAIL>", "tls_enabled": true, "auth_protocol": "userpass", "scan_interval": "15m", "tls_skip_verify": false}]', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.bucket', '"com"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.aws_secret_access_key', '"/ejpgKd8B+gaVv27pQgLNGyF/QO8GzjzGQyTutR+"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.enable_public_subscription_page', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.message_sliding_window_duration', '"1h"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.expiry', '"14d"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.check_updates', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.notify_emails', '["<EMAIL>"]', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.ses_enabled', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.allow_wipe', 'true', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.exportable', '["profile", "subscriptions", "campaign_views", "link_clicks"]', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.max_send_errors', '1000', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.sendgrid_key', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.allow_export', 'true', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.public_url', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.bucket_path', '"/"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.bucket_type', '"public"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.sendgrid_enabled', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('bounce.webhooks_enabled', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.allow_blocklist', 'true', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.bucket_domain', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.domain_blocklist', '[]', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.enable_public_archive', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.allow_preferences', 'true', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.message_sliding_window', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('appearance.admin.custom_js', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.unsubscribe_header', 'true', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.send_optin_confirmation', 'false', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('appearance.admin.custom_css', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('appearance.custom_js', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('privacy.individual_tracking', 'true', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.aws_access_key_id', '"AKIAZOTBARATBIP7S6EQ"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('appearance.custom_css', '""', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.filesystem.upload_uri', '"/uploads"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.s3.aws_default_region', '"ap-south-1"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('upload.filesystem.upload_path', '"uploads"', '2023-01-02 16:03:06.097178-05:30');
INSERT INTO settings VALUES ('app.message_sliding_window_rate', '10000', '2023-01-02 16:03:06.097178-05:30');

--
-- Data for Name: templates; Type: TABLE DATA; Schema: public; Owner: root
--

INSERT INTO templates VALUES (2, 'Default archive template', 'campaign', '', '<!doctype html>
<html>
    <head>
        <title>{{ .Campaign.Subject }}</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
        <base target="_blank">
        <style>
            body {
                background-color: #F0F1F3;
                font-family: ''Helvetica Neue'', ''Segoe UI'', Helvetica, sans-serif;
                font-size: 15px;
                line-height: 26px;
                margin: 0;
                color: #444;
            }

            pre {
                background: #f4f4f4f4;
                padding: 2px;
            }

            table {
                width: 100%;
                border: 1px solid #ddd;
            }
            table td {
                border-color: #ddd;
                padding: 5px;
            }

            .wrap {
                background-color: #fff;
                padding: 30px;
                max-width: 525px;
                margin: 0 auto;
                border-radius: 5px;
            }

            .button {
                background: #0055d4;
                border-radius: 3px;
                text-decoration: none !important;
                color: #fff !important;
                font-weight: bold;
                padding: 10px 30px;
                display: inline-block;
            }
            .button:hover {
                background: #111;
            }

            .footer {
                text-align: center;
                font-size: 12px;
                color: #888;
            }
                .footer a {
                    color: #888;
                    margin-right: 5px;
                }

            .gutter {
                padding: 30px;
            }

            img {
                max-width: 100%;
                height: auto;
            }

            a {
                color: #0055d4;
            }
                a:hover {
                    color: #111;
                }
            @media screen and (max-width: 600px) {
                .wrap {
                    max-width: auto;
                }
                .gutter {
                    padding: 10px;
                }
            }
        </style>
    </head>
<body style="background-color: #F0F1F3;font-family: ''Helvetica Neue'', ''Segoe UI'', Helvetica, sans-serif;font-size: 15px;line-height: 26px;margin: 0;color: #444;">
    <div class="gutter" style="padding: 30px;">&nbsp;</div>
    <div class="wrap" style="background-color: #fff;padding: 30px;max-width: 525px;margin: 0 auto;border-radius: 5px;">
        {{ template "content" . }}
    </div>
    
    <div class="footer" style="text-align: center;font-size: 12px;color: #888;">
        <p>Powered by <a href="https://listmonk.app" target="_blank" style="color: #888;">listmonk</a></p>
    </div>
</body>
</html>
', false, '2023-01-02 16:03:21.93718-05:30', '2023-01-02 16:03:21.93718-05:30', 873594, NULL, 'notification', '{}');
INSERT INTO templates VALUES (4, 'FCM and SMS template', 'campaign', 'FCM template', '{{ template "content" . }}', false, '2023-01-23 12:50:06.225835-05:30', '2023-01-23 17:48:36.218945-05:30', 873594, NULL, 'notification', '{}');
INSERT INTO templates VALUES (5, 'Default Pax Template', 'tx', 'Welcome {{ .Subscriber.Name }}', 'Hello {{ .Subscriber.Name }}
Order number: {{ .Tx.Data.orderId }} and order date is {{ .Tx.Data.date }}.
User Details are: mobile number- {{ .Tx.Data.userTxnSmsReq.mobileNumber }}, mintoak templateId- {{ .Tx.Data.userTxnSmsReq.mintoakTemplateId }}, placeholder values- {{ .Tx.Data.userTxnSmsReq.palceholder }}', false, '2023-04-01 19:55:54.96451-05:30', '2023-04-12 04:25:03.084674-05:30', 873594, '[{"key": "txnid", "value": "{{ .Tx.Data.txnId }}"}]', 'notification', '{}');
INSERT INTO templates VALUES (6, 'FCM data message', 'campaign', 'FCM template', '{{ template "content" . }}', false, '2023-01-23 12:50:06.225835-05:30', '2023-01-23 17:48:36.218945-05:30', 873594, NULL, 'data', '{}');
INSERT INTO templates VALUES (3, 'Sample transactional template', 'tx', 'Welcome {{ .Subscriber.Name }}', '<!doctype html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
        <base target="_blank">

        <style>
            body {
                background-color: #F0F1F3;
                font-family: ''Helvetica Neue'', ''Segoe UI'', Helvetica, sans-serif;
                font-size: 15px;
                line-height: 26px;
                margin: 0;
                color: #444;
            }

            pre {
                background: #f4f4f4f4;
                padding: 2px;
            }

            table {
                width: 100%;
                border: 1px solid #ddd;
            }
            table td {
                border-color: #ddd;
                padding: 5px;
            }

            .wrap {
                background-color: #fff;
                padding: 30px;
                max-width: 525px;
                margin: 0 auto;
                border-radius: 5px;
            }

            .button {
                background: #0055d4;
                border-radius: 3px;
                text-decoration: none !important;
                color: #fff !important;
                font-weight: bold;
                padding: 10px 30px;
                display: inline-block;
            }
            .button:hover {
                background: #111;
            }

            .footer {
                text-align: center;
                font-size: 12px;
                color: #888;
            }
                .footer a {
                    color: #888;
                    margin-right: 5px;
                }

            .gutter {
                padding: 30px;
            }

            img {
                max-width: 100%;
                height: auto;
            }

            a {
                color: #0055d4;
            }
                a:hover {
                    color: #111;
                }
            @media screen and (max-width: 600px) {
                .wrap {
                    max-width: auto;
                }
                .gutter {
                    padding: 10px;
                }
            }
        </style>
    </head>
<body style="background-color: #F0F1F3;font-family: ''Helvetica Neue'', ''Segoe UI'', Helvetica, sans-serif;font-size: 15px;line-height: 26px;margin: 0;color: #444;">
    <div class="gutter" style="padding: 30px;">&nbsp;</div>
    <div class="wrap" style="background-color: #fff;padding: 30px;max-width: 525px;margin: 0 auto;border-radius: 5px;">
        <p>Hello {{ .Subscriber.Name }}</p>
        <p>
            <strong>Order number: </strong> {{ .Tx.Data.order_id }}<br />
            <strong>Shipping date: </strong> {{ .Tx.Data.shipping_date }}<br />
        </p>
        <br />
        <p>
            Transactional templates supports arbitrary parameters.
            Render them using <code>.Tx.Data.YourParamName</code>. For more information,
            see the transactional mailing <a href="https://listmonk.app/docs/transactional">documentation</a>.
        </p>
    </div>
    
    <div class="footer" style="text-align: center;font-size: 12px;color: #888;">
        <p>Powered by <a href="https://listmonk.app" target="_blank" style="color: #888;">listmonk</a></p>
    </div>
</body>
</html>
', false, '2023-01-02 16:03:22.362152-05:30', '2023-01-02 16:03:22.362152-05:30', 873594, NULL, 'notification', '{}');
INSERT INTO templates VALUES (1, 'Default campaign template', 'campaign', 'Default campaign template', '<!doctype html>
<html>
    <head>
        <title>{{ .Campaign.Subject }}</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
        <base target="_blank">
        <style>
            body {
                background-color: #F0F1F3;
                font-family: ''Helvetica Neue'', ''Segoe UI'', Helvetica, sans-serif;
                font-size: 15px;
                line-height: 26px;
                margin: 0;
                color: #444;
            }

            pre {
                background: #f4f4f4f4;
                padding: 2px;
            }

            table {
                width: 100%;
                border: 1px solid #ddd;
            }
            table td {
                border-color: #ddd;
                padding: 5px;
            }

            .wrap {
                background-color: #fff;
                padding: 30px;
                max-width: 525px;
                margin: 0 auto;
                border-radius: 5px;
            }

            .button {
                background: #0055d4;
                border-radius: 3px;
                text-decoration: none !important;
                color: #fff !important;
                font-weight: bold;
                padding: 10px 30px;
                display: inline-block;
            }
            .button:hover {
                background: #111;
            }

            .footer {
                text-align: center;
                font-size: 12px;
                color: #888;
            }
                .footer a {
                    color: #888;
                    margin-right: 5px;
                }

            .gutter {
                padding: 30px;
            }

            img {
                max-width: 100%;
                height: auto;
            }

            a {
                color: #0055d4;
            }
                a:hover {
                    color: #111;
                }
            @media screen and (max-width: 600px) {
                .wrap {
                    max-width: auto;
                }
                .gutter {
                    padding: 10px;
                }
            }
        </style>
    </head>
<body style="background-color: #F0F1F3;font-family: ''Helvetica Neue'', ''Segoe UI'', Helvetica, sans-serif;font-size: 15px;line-height: 26px;margin: 0;color: #444;">
    <div class="gutter" style="padding: 30px;">&nbsp;</div>
    <div class="wrap" style="background-color: #fff;padding: 30px;max-width: 525px;margin: 0 auto;border-radius: 5px;">
        {{ template "content" . }}
    </div>
    
    <div class="footer" style="text-align: center;font-size: 12px;color: #888;">
        <p>
            {{ L.T "email.unsubHelp" }}
            <a href="{{ UnsubscribeURL }}" style="color: #888;">{{ L.T "email.unsub" }}</a>
            <a href="{{ MessageURL }}" style="color: #888;">{{ L.T "email.viewInBrowser" }}</a>
        </p>
        <p>Powered by <a href="https://mintoak.com" target="_blank" style="color: #888;">Mintoak</a></p>
    </div>
    <div class="gutter" style="padding: 30px;">&nbsp;{{ TrackView }}</div>
</body>
</html>
', true, '2023-01-02 16:03:21.512167-05:30', '2023-01-12 05:59:14.754251-05:30', 873594, NULL, 'notification', '{}');

--
-- Name: attribs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('attribs_id_seq', 1, true);


--
-- Name: awsdms_ddl_audit_c_key_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('awsdms_ddl_audit_c_key_seq', 1, true);


--
-- Name: awsdms_heartbeat_hb_key_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('awsdms_heartbeat_hb_key_seq', 1, true);


--
-- Name: bounces_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('bounces_id_seq', 1, false);


--
-- Name: campaign_lists_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('campaign_lists_id_seq', 1, true);


--
-- Name: campaign_targets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('campaign_targets_id_seq', 1, true);


--
-- Name: campaign_views_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('campaign_views_id_seq', 1, true);


--
-- Name: campaigns_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('campaigns_id_seq', 1, true);

-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('categories_id_seq', 2, true);

--
-- Name: gateway_details_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('gateway_details_id_seq', 1, true);


--
-- Name: link_clicks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('link_clicks_id_seq', 1, false);


--
-- Name: links_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('links_id_seq', 1, false);


--
-- Name: lists_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('lists_id_seq', 1, true);


--
-- Name: media_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('media_id_seq', 1, false);


--
-- Name: subscribers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('subscribers_id_seq', 1, true);


--
-- Name: template_details_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('template_details_id_seq', 1, true);


--
-- Name: templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('templates_id_seq', 7, true);


--
-- Name: awsdms_ddl_audit awsdms_ddl_audit_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY awsdms_ddl_audit
    ADD CONSTRAINT awsdms_ddl_audit_pkey PRIMARY KEY (c_key);


--
-- Name: awsdms_heartbeat awsdms_heartbeat_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY awsdms_heartbeat
    ADD CONSTRAINT awsdms_heartbeat_pkey PRIMARY KEY (hb_key);


--
-- Name: bounces bounces_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY bounces
    ADD CONSTRAINT bounces_pkey PRIMARY KEY (id);


-- Name: campaign_lists campaign_lists_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_lists
    ADD CONSTRAINT campaign_lists_pkey PRIMARY KEY (id);


--
-- Name: campaign_targets campaign_targets_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_targets
    ADD CONSTRAINT campaign_targets_pkey PRIMARY KEY (id);


--
-- Name: campaign_views campaign_views_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_views
    ADD CONSTRAINT campaign_views_pkey PRIMARY KEY (id);


--
-- Name: campaigns campaigns_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaigns
    ADD CONSTRAINT campaigns_pkey PRIMARY KEY (id);


--
-- Name: campaigns campaigns_uuid_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaigns
    ADD CONSTRAINT campaigns_uuid_key UNIQUE (uuid);


--
-- Name: gateway_details gateway_details_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY gateway_details
    ADD CONSTRAINT gateway_details_pkey PRIMARY KEY (id);


--
-- Name: link_clicks link_clicks_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY link_clicks
    ADD CONSTRAINT link_clicks_pkey PRIMARY KEY (id);


--
-- Name: links links_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY links
    ADD CONSTRAINT links_pkey PRIMARY KEY (id);


--
-- Name: links links_url_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY links
    ADD CONSTRAINT links_url_key UNIQUE (url);


--
-- Name: links links_uuid_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY links
    ADD CONSTRAINT links_uuid_key UNIQUE (uuid);


--
-- Name: lists lists_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY lists
    ADD CONSTRAINT lists_pkey PRIMARY KEY (id);


--
-- Name: lists lists_uuid_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY lists
    ADD CONSTRAINT lists_uuid_key UNIQUE (uuid);


--
-- Name: media media_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY media
    ADD CONSTRAINT media_pkey PRIMARY KEY (id);


--
-- Name: media media_uuid_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY media
    ADD CONSTRAINT media_uuid_key UNIQUE (uuid);


--
-- Name: categories pk_categories_id; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY categories
    ADD CONSTRAINT pk_categories_id PRIMARY KEY (id);


--
-- Name: settings settings_key_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY settings
    ADD CONSTRAINT settings_key_key UNIQUE (key);


--
-- Name: subscriber_lists subscriber_lists_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_lists
    ADD CONSTRAINT subscriber_lists_pkey PRIMARY KEY (subscriber_id, list_id);


--
-- Name: subscribers subscribers_email_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscribers
    ADD CONSTRAINT subscribers_email_key UNIQUE (name, type);


--
-- Name: subscribers subscribers_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscribers
    ADD CONSTRAINT subscribers_pkey PRIMARY KEY (id);


--
-- Name: subscribers subscribers_uuid_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscribers
    ADD CONSTRAINT subscribers_uuid_key UNIQUE (uuid);


--
-- Name: template_details template_details_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY template_details
    ADD CONSTRAINT template_details_pkey PRIMARY KEY (id);


--
-- Name: templates templates_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY templates
    ADD CONSTRAINT templates_pkey PRIMARY KEY (id);


--
-- Name: campaign_lists_campaign_id_list_id_idx; Type: INDEX; Schema: public; Owner: root
--

CREATE UNIQUE INDEX campaign_lists_campaign_id_list_id_idx ON campaign_lists USING btree (campaign_id, list_id);


--
-- Name: campaign_target_index; Type: INDEX; Schema: public; Owner: root
--

CREATE UNIQUE INDEX campaign_target_index ON campaign_targets USING btree (target);


--
-- Name: category_default_index; Type: INDEX; Schema: public; Owner: root
--

CREATE UNIQUE INDEX category_default_index ON categories USING btree (is_default) WHERE (is_default = true);


--
-- Name: idx_attribs_camp_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_attribs_camp_id ON campaign_attribs_master USING btree (campaign_id);


--
-- Name: idx_bounces_camp_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_bounces_camp_id ON bounces USING btree (campaign_id);


--
-- Name: idx_bounces_date; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_bounces_date ON bounces USING btree (((timezone('UTC'::text, created_at))::date));


--
-- Name: idx_bounces_source; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_bounces_source ON bounces USING btree (source);


--
-- Name: idx_bounces_sub_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_bounces_sub_id ON bounces USING btree (subscriber_id);


--
-- Name: idx_camp_lists_camp_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_camp_lists_camp_id ON campaign_lists USING btree (campaign_id);


--
-- Name: idx_camp_lists_list_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_camp_lists_list_id ON campaign_lists USING btree (list_id);


--
-- Name: idx_clicks_camp_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_clicks_camp_id ON link_clicks USING btree (campaign_id);


--
-- Name: idx_clicks_date; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_clicks_date ON link_clicks USING btree (((timezone('UTC'::text, created_at))::date));


--
-- Name: idx_clicks_link_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_clicks_link_id ON link_clicks USING btree (link_id);


--
-- Name: idx_clicks_sub_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_clicks_sub_id ON link_clicks USING btree (subscriber_id);


--
-- Name: idx_settings_key; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_settings_key ON settings USING btree (key);


--
-- Name: idx_sub_lists_list_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_sub_lists_list_id ON subscriber_lists USING btree (list_id);


--
-- Name: idx_sub_lists_status; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_sub_lists_status ON subscriber_lists USING btree (status);


--
-- Name: idx_sub_lists_sub_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_sub_lists_sub_id ON subscriber_lists USING btree (subscriber_id);


--
-- Name: idx_subs_status; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_subs_status ON subscribers USING btree (status);


--
-- Name: idx_views_camp_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_views_camp_id ON campaign_views USING btree (campaign_id);


--
-- Name: idx_views_date; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_views_date ON campaign_views USING btree (((timezone('UTC'::text, created_at))::date));


--
-- Name: idx_views_subscriber_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_views_subscriber_id ON campaign_views USING btree (subscriber_id);


--
-- Name: subscriber_id_index; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX subscriber_id_index ON subscriber_preferences USING btree (subscriber_id);


--
-- Name: subscriber_preferences_subscriber_id_category_id_channel_idx; Type: INDEX; Schema: public; Owner: root
--

CREATE UNIQUE INDEX subscriber_preferences_subscriber_id_category_id_channel_idx ON subscriber_preferences USING btree (subscriber_id, category_id, channel);


--
-- Name: templates_is_default_idx; Type: INDEX; Schema: public; Owner: root
--

CREATE UNIQUE INDEX templates_is_default_idx ON templates USING btree (is_default) WHERE (is_default = true);


--
-- Name: subscriber_preferences preference_changes; Type: TRIGGER; Schema: public; Owner: root
--

CREATE TRIGGER preference_changes AFTER INSERT OR UPDATE ON subscriber_preferences FOR EACH ROW EXECUTE FUNCTION preference_history();


--
-- Name: bounces bounces_campaign_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY bounces
    ADD CONSTRAINT bounces_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: bounces bounces_subscriber_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY bounces
    ADD CONSTRAINT bounces_subscriber_id_fkey FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: campaign_lists campaign_lists_campaign_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_lists
    ADD CONSTRAINT campaign_lists_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: campaign_lists campaign_lists_list_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_lists
    ADD CONSTRAINT campaign_lists_list_id_fkey FOREIGN KEY (list_id) REFERENCES lists(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: campaign_views campaign_views_campaign_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_views
    ADD CONSTRAINT campaign_views_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: campaign_views campaign_views_subscriber_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaign_views
    ADD CONSTRAINT campaign_views_subscriber_id_fkey FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: campaigns campaigns_archive_template_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaigns
    ADD CONSTRAINT campaigns_archive_template_id_fkey FOREIGN KEY (archive_template_id) REFERENCES templates(id) ON DELETE SET DEFAULT;


--
-- Name: campaigns campaigns_template_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY campaigns
    ADD CONSTRAINT campaigns_template_id_fkey FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET DEFAULT;


--
-- Name: link_clicks link_clicks_campaign_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY link_clicks
    ADD CONSTRAINT link_clicks_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: link_clicks link_clicks_link_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY link_clicks
    ADD CONSTRAINT link_clicks_link_id_fkey FOREIGN KEY (link_id) REFERENCES links(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: link_clicks link_clicks_subscriber_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY link_clicks
    ADD CONSTRAINT link_clicks_subscriber_id_fkey FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: subscriber_lists subscriber_lists_list_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_lists
    ADD CONSTRAINT subscriber_lists_list_id_fkey FOREIGN KEY (list_id) REFERENCES lists(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: subscriber_lists subscriber_lists_subscriber_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_lists
    ADD CONSTRAINT subscriber_lists_subscriber_id_fkey FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: subscriber_preference_logs subscriber_preference_logs_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_preference_logs
    ADD CONSTRAINT subscriber_preference_logs_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: subscriber_preference_logs subscriber_preference_logs_subscriber_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_preference_logs
    ADD CONSTRAINT subscriber_preference_logs_subscriber_id_fkey FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: subscriber_preferences subscriber_preferences_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_preferences
    ADD CONSTRAINT subscriber_preferences_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: subscriber_preferences subscriber_preferences_subscriber_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY subscriber_preferences
    ADD CONSTRAINT subscriber_preferences_subscriber_id_fkey FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: template_details template_details_gateway_details_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY template_details
    ADD CONSTRAINT template_details_gateway_details_id_fkey FOREIGN KEY (gateway_details_id) REFERENCES templates(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: template_details template_details_template_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY template_details
    ADD CONSTRAINT template_details_template_id_fkey FOREIGN KEY (template_id) REFERENCES templates(id) ON UPDATE CASCADE ON DELETE CASCADE;