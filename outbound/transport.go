package outbound

import (
	"crypto/tls"
	"net/http"
	"time"
)

// CustomTransport is wrapper around default transport
type CustomTransport struct {
	T *http.Transport
}

// Outbound metrics collector
type MetricCollector func(*http.Request, *http.Response, float64)

// OMC is during prometheus middleware initialization
var OMC MetricCollector

func (ct *CustomTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	start := time.Now()
	res, err := ct.T.RoundTrip(req)
	elapsed := float64(time.Since(start)) / float64(time.Second)
	if OMC != nil {
		OMC(req, res, elapsed)
	}
	return res, err
}

func GetOutboundClient(skipSSl bool) *http.Client {

	b := &CustomTransport{
		T: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: skipSSl,
			},
		}}
	return &http.Client{
		Transport: b,
	}
}
