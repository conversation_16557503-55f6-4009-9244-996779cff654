package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"net/http"
	"strconv"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
	"gopkg.in/volatiletech/null.v6"
)

// handleCreateGateway handles template creation.
func handleCreateGateway(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		o      = models.GateWayDetails{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("error occured while creating gateway %v", err)
		return err
	}
	payload, err := json.Marshal(o)
	if err != nil {
		logger.Error().Msgf("Error occured while creating gateway %v", err)
		return err
	}
	logger.Info().Msgf("Got request for CreateGateway api: %v", string(payload))

	var f template.FuncMap = app.manager.GenericTemplateFuncs()

	if err := o.CompileV2(f); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		o.CreatedBy = null.StringFrom(userID)
	}
	//INSERT INTO public.gateway_details(request_params, headers, url, request_body, updated_at, created_at, method, messenger, name) VALUES ($1, $2, $3, $4, now(), now(), $5, $6, $7) returning id
	out, err := app.core.CreateGateway(o.RequestParams.String, o.Headers.String, o.Url.String, o.RequestBody.String, o.Method.String, o.Messenger.String, o.Name.String, o.Configuration, o.IsDefault, o.CreatedBy)
	if err != nil {
		logger.Error().Msgf("Error occured while creating gateway %v", err)
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpdateGateway handles template modification.
func handleUpdateGateway(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)
	var o models.GateWayDetails

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	if err := c.Bind(&o); err != nil {
		logger.Error().Msgf("Error occured while updating gateway %v", err)
		return err
	}

	payload, err := json.Marshal(o)

	if err != nil {
		logger.Error().Msgf("Error occured while updating gateway %v", err)
		return err
	}

	logger.Info().Msgf("Got request for UpdateGateway api: %v", string(payload))

	if o.Method.Valid && o.Method.String != "smtp" {
		f := app.manager.GenericTemplateFuncs()

		// Compile the template and validate.
		if err := o.CompileV2(f); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}

	}

	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		o.UpdatedBy = null.StringFrom(userID)
	}

	out, err := app.core.UpdateGateway(id, o)
	if err != nil {
		return err
	}

	if o.Method.String != "smtp" {
		app.manager.GetSingleGateway(id, logger)
		PublishStateChangeEvents(id, "gatewayUpdated", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))
	} else {
		UpdateSmtpPool(logger)
		PublishStateChangeEvents(0, "gatewayUpdated", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))
	}

	return c.JSON(http.StatusOK, okResp{out})

}

// handleGetGateway handles gateway creation.
func handleGetGateway(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	// Fetch one list.
	if id > 0 {
		out, err := app.core.GetGateway(id)
		if err != nil {
			return err
		}
		if len(out) == 0 {
			return c.JSON(http.StatusOK, okResp{models.GateWayDetails{}})
		}

		return c.JSON(http.StatusOK, okResp{out[0]})
	}

	out, err := app.core.GetGateway(0)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// TODO: set template_params to use default provider
// other state management task
func handleDeleteGateway(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}
	var updatedBy null.String
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}
	if err := app.core.DeleteGateway(id, updatedBy); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// To Do: state management
func UpdateSmtpGateway(smtps map[string]models.SMTP, app *App, logger push.Logger, c echo.Context) error {

	gatewayDetailsMap := make(map[int]*models.GateWayDetails)

	for k, v := range smtps {
		gat, err := app.core.GetSmtpGateway(k)

		if err != nil {
			return err
		}

		if v.Default && !v.Enabled {
			logger.Error().Msg("error occurred in update settings api: cannot disable the default provider")
			return errors.New("default provider cannot be disabled")
		}

		d, err := json.Marshal(v)

		if err != nil {
			logger.Error().Msgf("error occurred while marshalling settings json %v", err)
			return err
		}
		logger.Info().Msgf("%v", gat.ID)

		gatewayDetailsMap[gat.ID] = &models.GateWayDetails{
			Configuration: d,
			IsDefault:     v.Default,
			Name:          null.StringFrom(v.Name),
		}
	}

	tx, err := db.Beginx()

	if err != nil {
		logger.Error().Msgf("Error starting transaction to add subscribers %v", err)
		return err
	}

	sql := "update gateway_details set configuration=$1, name=(case when $2!='' then $2 else name end), is_default=$3 where id=$4"
	stmt, err := tx.Prepare(sql)
	if err != nil {
		logger.Error().Msgf("error updating gateway, detailed error:%v", err)
		tx.Rollback()
		return err
	}

	defer stmt.Close()

	for k, v := range gatewayDetailsMap {
		_, err = stmt.Exec(v.Configuration, v.Name, v.IsDefault, k)
		if err != nil {
			logger.Error().Msgf("error updating gateway with id %d, detailed error:%v", k, err)
			err = fmt.Errorf("error updating model %d", k)
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit(); err != nil {
		logger.Error().Msgf("Error committing transaction: %v", err)
		return err
	}
	UpdateSmtpPool(logger)
	PublishStateChangeEvents(0, "gatewayUpdated", app.manager.GetBroadcastSubject(), tracer.WrapEchoContextLogger(c))
	return nil
}

func UpdateSmtpPool(logger push.Logger) error {

	smtpList, err := app.core.GetSMTPList()
	if err != nil {
		return err
	}
	if smtpList == nil {
		return nil
	}
	smtpDetails := make(map[string]models.SMTP)
	var ids []int
	for _, v := range smtpList {
		if !v.Enabled {
			continue
		}
		smtpDetails[v.UUID] = v
		ids = append(ids, int(v.ID))
	}

	if len(smtpDetails) == 0 {
		return nil
	}
	app.messengers[emailMsgr] = initSMTPMessenger(smtpDetails, config.ServerConfig.SkipSSLCheck, config.ServerConfig.MaxEmailAttachmentSize)

	for _, k := range ids {
		app.manager.GetSingleGateway(k, logger)
	}
	return nil
}
