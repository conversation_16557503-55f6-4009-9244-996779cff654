pipeline {
    agent any
      environment {
          mvnHome = tool name: 'maven', type: 'maven'
          scannerHome = tool name: 'Sonar', type: 'hudson.plugins.sonar.SonarRunnerInstallation'
          Key = jiraIssueSelector(issueSelector: [$class: 'DefaultIssueSelector'])
          awsAccess = credentials('jenkins-aws-access-key')
          awsSecret = credentials('jenkins-aws-secret-key')
          argocdPassDev = credentials('argo-pass-dev')
          argocdPassUAT = credentials('argo-pass')
	        appname = "listmonk"
	        tag = "${BUILD_NUMBER}"
	        Ecr_registry = "${EcrRegistryUrl}/${appname}"
          }

   stages {
   stage('Create Git Tag for Production'){
        when { branch 'master' }
        steps {
          withCredentials([usernamePassword(credentialsId: 'Mintoak_Bitbucket', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
            sh '''
              TAGVERSION=$(git log --merges --grep="pull request #" master | grep -E "release/|hotfix/" | head -n 1 | grep -oE "(release|hotfix)/[0-9.]*" | sed 's/\\(release\\|hotfix\\)\\///')
              echo v$TAGVERSION
              git tag -f -a v$TAGVERSION -m "New release for v$TAGVERSION"
              git push -f --tags https://lokeshbuddappagari:$<EMAIL>/mintoak/listmonk.git
              '''
            }
        }
      }
    stage ('GitVersion Determine'){
      steps {
        sh '''
          export DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
          gitversion /config /var/lib/jenkins/gitversion/GitVersion.yml /output buildserver
        '''
            script {
            def props = readProperties file: 'gitversion.properties'
            env.GitVersion_SemVer = props.GitVersion_SemVer
            env.GitVersion_BranchName = props.GitVersion_BranchName
            env.GitVersion_AssemblySemVer = props.GitVersion_AssemblySemVer
            env.GitVersion_MajorMinorPatch = props.GitVersion_MajorMinorPatch
            env.GitVersion_Sha = props.GitVersion_Sha
            echo "Current Version: ${GitVersion_SemVer}"
            currentBuild.displayName = "${GitVersion_SemVer}-${BUILD_NUMBER}"
          }
        }
  }

  stage('Docker Absa build Dev'){
        when { allOf {
                  branch 'develop';
                  environment name: 'IsABSA', value: 'true'
                  } }
          steps {
            script {      
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} -f ABSADockerfile --build-arg FOLDER_NAME=Dev-4_ABSA ."
         sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:dev"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker push ${Ecr_registry}:dev || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer}"
         sh "docker rmi -f ${Ecr_registry}:dev"
         sh "sleep 60"
        }  
      }
    post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }     
   
  
      stage('Docker Absa build UAT'){
        when { allOf {
                  branch '*release/**';
                  environment name: 'IsABSA', value: 'true'
                  } }
          steps {
            script { 
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} -f ABSADockerfile --build-arg FOLDER_NAME=UAT_ABSA ."
         sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:uat"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker push ${Ecr_registry}:uat || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:uat"
         
        }   
      }
    post {
           always {
             jiraSendDeploymentInfo site: 'mintoak.atlassian.net', environmentId: 'uat-env', environmentName: 'ecs_uat', environmentType: 'testing'
             }
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }
       
    stage('Docker Absa build Production'){
        when { allOf {
                  branch 'master';
                  environment name: 'IsABSA', value: 'true'
                  } }
          steps {
            script {      
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} -f ABSADockerfile --build-arg FOLDER_NAME=PROD_ABSA ."
         sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:prod"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker push ${Ecr_registry}:prod || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer}"
         sh "docker rmi -f ${Ecr_registry}:prod"
         sh "sleep 60"
        }  
      }
    post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }     
       

  
  stage('ECS-listmonk-deploy'){
      when { allOf {
                branch 'develop';
                environment name: 'IsABSA', value: 'true'
            } }
      steps{
         script{
            sh '''
            pwd
            echo "executing deploy "
            aws --version
            sed -i "s#build#${GitVersion_SemVer}#g" task-definition.json
            aws ecs register-task-definition --cli-input-json file://task-definition.json
            REVISION=`aws ecs describe-task-definition --task-definition List-Monk-Dev | jq .taskDefinition.revision`
            echo "REVISION= " "${REVISION}"
            aws ecs update-service --cluster ABSA-Dev --service LIST-MONK-SERVICE --task-definition List-Monk-Dev:"${REVISION}"
            '''
         }
	    }
    } 
  
    stage('Clean Workspace After build') {
            when { not { branch 'master' } }
            steps {
                sh "ls -all"
                cleanWs()
                sh "ls -all"
                sh "pwd"
            }
        }
  }
}