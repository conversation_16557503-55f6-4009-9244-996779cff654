pipeline {
    agent any
      environment {
          mvnHome = tool name: 'maven', type: 'maven'
          scannerHome = tool name: 'Sonar', type: 'hudson.plugins.sonar.SonarRunnerInstallation'
          Key = jiraIssueSelector(issueSelector: [$class: 'DefaultIssueSelector'])
          awsAccess = credentials('jenkins-aws-access-key')
          awsSecret = credentials('jenkins-aws-secret-key')
          argocdPassDev = credentials('argo-pass-aub-dev')
          argocdPassUAT = credentials('argo-pass')
          appname = "aub-listmonk"
          tag = "${BUILD_NUMBER}"
          Ecr_registry = "${EcrRegistryUrl}/${appname}"
          branch = "${GIT_BRANCH}"
          }

   stages {
   stage('Create Git Tag for Production'){
        when { branch 'master' }
        steps {
          withCredentials([usernamePassword(credentialsId: 'Mintoak_Bitbucket', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
            sh '''
              TAGVERSION=$(git log --merges --grep="pull request #" master | grep -E "release/|hotfix/" | head -n 1 | grep -oE "(release|hotfix)/[0-9.]*" | sed 's/\\(release\\|hotfix\\)\\///')
              echo v$TAGVERSION
              git tag -f -a v$TAGVERSION -m "New release for v$TAGVERSION"
              git push -f --tags https://lokeshbuddappagari:$<EMAIL>/mintoak/listmonk.git
              '''
            }
        }
      }
    stage ('GitVersion Determine'){
      steps {
        sh '''
          export DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
          gitversion /config /var/lib/jenkins/gitversion/GitVersion.yml /output buildserver
        '''
            script {
            def props = readProperties file: 'gitversion.properties'
            env.GitVersion_SemVer = props.GitVersion_SemVer
            env.GitVersion_BranchName = props.GitVersion_BranchName
            env.GitVersion_AssemblySemVer = props.GitVersion_AssemblySemVer
            env.GitVersion_MajorMinorPatch = props.GitVersion_MajorMinorPatch
            env.GitVersion_Sha = props.GitVersion_Sha
            echo "Current Version: ${GitVersion_SemVer}"
            currentBuild.displayName = "${GitVersion_SemVer}-${BUILD_NUMBER}"
          }
        }
  }

  stage('SCA trivy FS scan'){
          steps{
          script {
          sh "trivy fs . --format template --template \"@/usr/local/share/trivy/templates/html.tpl\" -o trivyfsreport.html" 
          def branchName = env.BRANCH_NAME ?: 'unknown' // Get branch name or default to 'unknown'
            env.TRIVY_FS_SUBJECT = "Trivy FS Report for Branch: ${branchName}"
        }
      }
    post {
        always{
           emailext(
           subject: "AUB-Listmonk_${env.TRIVY_FS_SUBJECT}", body: 'Trivy FS Listmonk report.', attachmentsPattern: '**/trivyfsreport.html', to: '$DEFAULT_RECIPIENTS'
           )
           publishHTML (target: [
      allowMissing: true,
      alwaysLinkToLastBuild: true,
      keepAll: false,
      reportDir: '',
      reportFiles: 'trivyfsreport.html',
      reportName: "Trivy FS scan Report"
       ])
        }
      }
   }

stage('trivy FS SBOM Generation and Scan'){
          steps{
          script {
          
          sh "trivy fs . --format cyclonedx --timeout 600s -o trivyfsbomreport.json"
          if (env.TrivyGateEnabled == "True"){
            sh "trivy sbom trivyfsbomreport.json --exit-code 1 --severity HIGH,CRITICAL"
          }
        }
      }
      }
     
      stage('Docker Image build'){
          steps {
            script { 
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker build -t ${Ecr_registry}:${GitVersion_SemVer} --build-arg VERSION=${GitVersion_SemVer} ."
         if (env.branch == 'develop'){ 
            sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:dev"
         }else if((env.branch).startsWith('release') ){
            sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:uat"
         }else if (env.branch == 'master') {
            sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:preprod"
            sh "docker tag ${Ecr_registry}:${GitVersion_SemVer} ${Ecr_registry}:prod"
         }
        }   
      }
    post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }

  stage('trivy image SBOM Generate'){
          steps{
          script {
          sh "trivy image --format spdx-json -o result.json ${Ecr_registry}:${GitVersion_SemVer}"
        }
      }
   }

  stage('trivy image scan'){
          steps{
          script {
          sh "trivy image --timeout 20m --format template --template \"@/usr/local/share/trivy/templates/html.tpl\" -o trivyreport.html ${Ecr_registry}:${GitVersion_SemVer} --timeout 25m " //--no-progress --exit-code 1 --severity HIGH,CRITICAL
          if (env.TrivyGateEnabled == "True"){
            sh "trivy image --no-progress --exit-code 1 --severity HIGH,CRITICAL ${Ecr_registry}:${tag}"
          }
          def branchName = env.BRANCH_NAME ?: 'unknown' // Get branch name or default to 'unknown'
            env.TRIVY_SUBJECT = "Trivy Report for Branch: ${branchName}"
        }
      }
    post {
        always{
           emailext(
           subject: "AUB-Listmonk_${env.TRIVY_SUBJECT}", body: 'Trivy Listmonk report.', attachmentsPattern: '**/trivyreport.html', to: '$DEFAULT_RECIPIENTS'
           )
           publishHTML (target: [
      allowMissing: true,
      alwaysLinkToLastBuild: true,
      keepAll: false,
      reportDir: '',
      reportFiles: 'trivyreport.html',
      reportName: "Trivy Image Scan Report"
       ])
        }
      }
   }
  
  stage('Docker Image Push'){
          steps {
            script { 
         sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${EcrRegistryUrl}"
         sh "docker push ${Ecr_registry}:${GitVersion_SemVer} || true"
         sh "docker rmi -f ${Ecr_registry}:${GitVersion_SemVer}"
         if (env.branch == 'develop'){ 
            sh "docker push ${Ecr_registry}:dev || true"
            sh "docker rmi -f ${Ecr_registry}:dev"
         }else if((env.branch).startsWith('release') ){
            sh "docker push ${Ecr_registry}:uat || true"
            sh "docker rmi -f ${Ecr_registry}:uat"
         }else if (env.branch == 'master') {
            sh "docker push ${Ecr_registry}:preprod || true"
            sh "docker rmi -f ${Ecr_registry}:preprod"
            sh "docker push ${Ecr_registry}:prod || true"
            sh "docker rmi -f ${Ecr_registry}:prod"
         }
        }   
      }
    post {
           failure {
               script{
                  emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    ---------------------------
    Docker image-build was failed
    Please check the Dockerfile
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
               }
            }
          }  
       }     

  stage('K8s Dev-Deploy'){
     when { branch 'develop' }
     steps{
           sh "yes | argocd login ${AUBArgoCDDevUrl} --username admin --password ${argocdPassDev}"
           sh "argocd app actions run listmonk-aub restart --kind Rollout --resource-name listmonk-deploy"
         }
       post {
	  always {
             jiraSendDeploymentInfo site: 'mintoak.atlassian.net', environmentId: 'App-cluster', environmentName: 'Develop', environmentType: 'development'
             }
	 success {
             script{
                     emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    Deployment Server: app-cluster
    ---------------------------
    SonarQube Report generated (code analysis + code coverage)
    Sonar Quality Gate is Passed, check URL: ${SONAR_URL}

    Junit Test-Cases:
    Total tests = $TEST_COUNTS
    Passed = ${TEST_COUNTS,var="pass"}
    Failed = ${TEST_COUNTS,var="fail"}
    Test cases Results:
    ${FAILED_TESTS}
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
             }
           }       
         }
       }

  stage('K8s UAT-Deploy'){
      when { branch '*release/**' }
     steps{
           sh "yes | argocd login ${ArgoCDUatUrl} --username admin --password ${argocdPassUAT}"
           sh "argocd app actions run listmonk restart --kind Rollout --resource-name listmonk-deploy"
         }
       post {
	  always {
             jiraSendDeploymentInfo site: 'mintoak.atlassian.net', environmentId: 'App-cluster', environmentName: 'UAT', environmentType: 'testing'
             }
	 success {
             script{
                     emailext attachLog: true, body: '''$DEFAULT_CONTENT
    Job Details:
    Application: ${JOB_NAME}
    Branch Name: ${BRANCH_NAME}
    Deployment Server: app-cluster
    ---------------------------
    SonarQube Report generated (code analysis + code coverage)
    Sonar Quality Gate is Passed, check URL: ${SONAR_URL}

    Junit Test-Cases:
    Total tests = $TEST_COUNTS
    Passed = ${TEST_COUNTS,var="pass"}
    Failed = ${TEST_COUNTS,var="fail"}
    Test cases Results:
    ${FAILED_TESTS}
    --------------------------
    Code changes:
    ${CHANGES}''', subject: '$DEFAULT_SUBJECT', to: '$DEFAULT_RECIPIENTS'
             }
           }       
	    }   
     } 
     
     stage('Clean Workspace After build') {
            when { not { branch 'master' } }
            steps {
                sh "ls -all"
                cleanWs()
                sh "ls -all"
                sh "pwd"
            }
        }
  }
}