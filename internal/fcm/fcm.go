package fcm

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	push "github.com/phuslu/log"
)

const (
	CONTENT_TYPE     = "Content-Type"
	APPLICATION_JSON = "application/json"
	FORM_DATA        = "application/x-www-form-urlencoded"
)

type Request struct {
	TerminalId string `json:"terminalId"`
}

func GetFcmToken(terminalId string, ctx context.Context) []models.FcmCache {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	config := utils.GetConfigProperties()
	url := config.ServerConfig.OneAppAuthUrl + "OneAppAuth/" + "getFCMTokensForTerminalId"
	logger.Info().Msgf("URL:> %v", url)
	m := Request{terminalId}
	jsonStr, err := json.Marshal(m)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set(CONTENT_TYPE, APPLICATION_JSON)
	client, err := tracer.GetHttpClient(config.ServerConfig.SkipSSLCheck)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return []models.FcmCache{}
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)

	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while fetching fcmTokes from api %v", err)
		return []models.FcmCache{}
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %v", resp.Status)
	logger.Info().Msgf("response Headers: %v", resp.Header)
	body, _ := io.ReadAll(resp.Body)
	response := models.FcmTokenOject{}
	err = json.Unmarshal((body), &response)
	// fmt.Println("response Body:", string(body))
	if response.Status == "Success" {
		return response.RoleAndFCMTokens
	}
	return []models.FcmCache{}
}
