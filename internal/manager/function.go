package manager

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/knadh/listmonk/models"
)

func base64File(data []byte) string {

	enc := base64.StdEncoding.EncodeToString(data)
	return enc
}

func transformer(desired string, input map[string]interface{}) interface{} {

	mapper := make(map[string]string)
	json.Unmarshal([]byte(desired), &mapper)

	output := make(map[string]interface{})

	for key, value := range mapper {
		output[key] = input[value]
	}
	response, _ := json.Marshal(output)
	return response
}

func downloadFile(presignedURL string) ([]byte, error) {

	resp, err := http.Get(presignedURL)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status code: %d", resp.StatusCode)
	}

	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	return fileContent, nil
}

func objectListT(desired string, input []models.Files) interface{} {

	mapper := make(map[string]string)
	json.Unmarshal([]byte(desired), &mapper)
	result := make([]interface{}, 0)

	for _, item := range input {

		output := make(map[string]interface{})
		for key, value := range mapper {
			if strings.Contains(value, "Func") {
				newKey := strings.Replace(value, "Func", "", -1)
				newKey = strings.Replace(newKey, " ", "", -1)

				if newKey == "FileUrl" {
					file, err := downloadFile(item.FileUrl)
					if err == nil {
						output[key] = base64File(file)
					}
				} else if newKey == "MimeType" {
					output[key] = getMIME(item.FileName)
				}
			} else if value == "ContentType" {
				output[key] = item.ContentType
			} else if value == "FileUrl" {
				output[key] = item.FileUrl
			} else if value == "FileName" {
				output[key] = item.FileName
			} else if value == "Encoding" {
				output[key] = item.Encoding
			} else {
				output[key] = value
			}

		}
		result = append(result, output)
	}
	resultString, _ := json.Marshal(result)
	return string(resultString)
}

func getMIME(fileName string) string {
	ext := filepath.Ext(fileName)
	mimeType := mime.TypeByExtension(ext)
	return mimeType
}

func getCurrentMonth() string {
	return getDateResult("currentmonth")
}
func getLastMonth() string {
	return getDateResult("lastmonth")
}
func getNextMonth() string {
	return getDateResult("nextmonth")
}
func getCurrentYear() string {
	return getDateResult("currentyear")
}
func getLastYear() string {
	return getDateResult("lastyear")
}
func getNextYear() string {
	return getDateResult("nextyear")
}

func getDateResult(value string) string {
	current := time.Now()
	switch value {
	default:
		return current.Month().String()
	case "lastmonth":
		return current.AddDate(0, -1, 0).Month().String()
	case "nextmonth":
		return current.AddDate(0, 1, 0).Month().String()
	case "currentyear":
		return fmt.Sprintf("%d", current.AddDate(1, 0, 0).Year())
	case "lastyear":
		return fmt.Sprintf("%d", current.AddDate(-1, 0, 0).Year())
	case "nextyear":
		return fmt.Sprintf("%d", current.AddDate(-1, 0, 0).Year())
	}
}
