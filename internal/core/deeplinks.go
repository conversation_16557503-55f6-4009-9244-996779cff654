package core

import (
	"net/http"

	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

func (c *Core) GetAllDeepLinks(logger push.Logger) ([]models.DeepLinks, error) {
	out := []models.DeepLinks{}

	if err := c.q.GetDeepLinks.Select(&out); err != nil {
		logger.Error().Msgf("error fetching deeplinks: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.deeplinks}", "error", pqErrMsg(err)))
	}

	return out, nil
}

func (c *Core) GetDeepLink(id int, logger push.Logger) (models.DeepLinks, error) {
	out := models.DeepLinks{}
	if err := c.q.GetDeepLinkById.Get(&out, id); err != nil {
		logger.Error().Msgf("error fetching deeplinks by id: %v", err)
		return models.DeepLinks{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.deeplinks}", "error", pqErrMsg(err)))
	}

	return out, nil
}

func (c *Core) ImportClientDeepLinks(deeplinks []models.DeepLinks, logger push.Logger) (map[string]interface{}, error) {
	var (
		created int
		updated int
		skipped int
		failed  int
	)

	tx, err := c.db.Beginx()
	if err != nil {
		logger.Error().Msgf("error starting transaction: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUploading", "name", "{globals.terms.deeplinks}", "error", err.Error()))
	}

	if _, err := tx.Exec(`TRUNCATE TABLE deeplinks`); err != nil {
		tx.Rollback()
		logger.Error().Msgf("error truncating deeplinks: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	for _, dl := range deeplinks {
		var out models.DeepLinks
		err := tx.Get(&out, c.q.InsertDeeplinks, dl.Category, dl.Deeplink, dl.DeeplinkName, dl.IsActive, dl.CreatedBy, dl.UpdatedBy)
		if err != nil {
			logger.Error().Msgf("error inserting deeplink %s: %v", dl.Deeplink, err)
			failed++
			continue
		}
		if out.ID == 0 {
			created++
		} else {
			updated++
		}
	}

	if err := tx.Commit(); err != nil {
		logger.Error().Msgf("error committing transaction: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return map[string]interface{}{
		"total":   len(deeplinks),
		"created": created,
		"updated": updated,
		"skipped": skipped,
		"failed":  failed,
	}, nil
}
