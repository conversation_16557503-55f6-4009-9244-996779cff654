package core

import (
	"encoding/json"
	"net/http"

	"github.com/jmoiron/sqlx/types"
	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	push "github.com/phuslu/log"
)

// GetSettings returns settings from the DB.
func (c *Core) GetSettings() (models.Settings, error) {
	var (
		b   types.JSONText
		out models.Settings
	)

	if err := c.q.GetSettings.Get(&b); err != nil {
		return out, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching",
				"name", "{globals.terms.settings}", "error", pqErrMsg(err)))
	}

	// Unmarshal the settings and filter out sensitive fields.
	if err := json.Unmarshal([]byte(b), &out); err != nil {
		return out, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("settings.errorEncoding", "error", err.Error()))
	}

	out.SMTP, _ = c.GetSMTPList()
	return out, nil
}

func (c *Core) GetSMTPList() ([]models.SMTP, error) {

	gd, err := c.GetSMTPDetails()

	if err != nil || len(gd) == 0 {
		return nil, err
	}

	var count []int
	gatewayCount := 0

	if err := c.q.GetMessengerCount.Select(&count, "email"); err != nil {
		return nil, err
	}

	if len(count) != 0 {
		gatewayCount = count[0]
	}

	smtpMap := make(map[string]models.SMTP)

	for _, val := range gd {
		smtp := models.SMTP{}
		if err := json.Unmarshal([]byte(val.Configuration), &smtp); err != nil {
			return nil, err
		}
		_, has := smtpMap[smtp.UUID]

		if gatewayCount == 1 {
			smtp.Default = true
		}
		if !has {
			smtp.Default = val.IsDefault
			smtp.ID = int64(val.ID)
			smtpMap[smtp.UUID] = smtp
		}

	}

	smtpList := make([]models.SMTP, 0, len(smtpMap))

	for _, val := range smtpMap {
		smtpList = append(smtpList, val)
	}
	return smtpList, nil
}

// UpdateSettings updates settings.
func (c *Core) UpdateSettings(s models.Settings) error {
	// Marshal settings.
	b, err := json.Marshal(s)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("settings.errorEncoding", "error", err.Error()))
	}

	// Update the settings in the DB.
	if _, err := c.q.UpdateSettings.Exec(b); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.settings}", "error", pqErrMsg(err)))
	}

	return nil
}

// GetMessengerSettings returns messenger settings from the DB.
func (c *Core) GetMessengerSettings(logger push.Logger) ([]models.Messengers, error) {
	var (
		b   types.JSONText
		out []models.Messengers
	)

	if err := c.q.GetMessengerSettings.Get(&b); err != nil {
		return out, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching",
				"messengers", "{globals.terms.settings}", "error", pqErrMsg(err)))
	}

	// Unmarshal the settings and filter out sensitive fields.
	if err := json.Unmarshal([]byte(b), &out); err != nil {
		return out, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("settings.errorEncoding", "error", err.Error()))
	}

	return out, nil
}

func (c *Core) GetWebhookConfig(ids []string, logger push.Logger) ([]models.WebhookConfig, error) {
	out := []models.WebhookConfig{}
	if err := c.q.GetWebhookConfig.Select(&out, pq.Array(ids)); err != nil {
		return out, err
	}
	return out, nil
}

func (c *Core) ValidateWebhookConfig(provider, messenger, id string) (int, error) {
	var count int
	if err := c.q.ValidateWebhookConfig.Get(&count, provider, messenger, id); err != nil {
		return 0, err
	}
	return count, nil
}

func (c *Core) UpsertWebhookConfig(l models.WebhookConfig, logger push.Logger) (models.WebhookConfig, error) {

	webhookConfig := models.WebhookConfig{}
	if err := c.q.UpsertWebhookConfig.Get(&webhookConfig, l.Id, l.Provider, l.Messenger, l.PayloadType, l.AuthType, l.AuthSecretPath, l.PayloadTemplate, l.StatusMapper, l.CreatedBy, l.UpdatedBy, l.TransformerTemplate); err != nil {
		logger.Error().Msgf("error upserting webhook config: %v", err)
		return models.WebhookConfig{}, err
	}
	return webhookConfig, nil
}

func (c *Core) DeleteWebhookConfig(id string, logger push.Logger) error {
	var webConfigId string
	if err := c.q.DeleteWebhookConfig.Get(&webConfigId, id); err != nil {
		return err
	}
	logger.Info().Msgf("successfully deleted webhook_config with id %v", webConfigId)
	return nil
}
