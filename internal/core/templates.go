package core

import (
	"database/sql"
	"net/http"

	"github.com/jmoiron/sqlx/types"
	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	"gopkg.in/volatiletech/null.v6"
)

// GetTemplates retrieves all templates.
func (c *Core) GetTemplates(status string, noBody bool) ([]models.Template, error) {
	out := []models.Template{}

	if err := c.q.GetTemplates.Select(&out, 0, noBody, status); err != nil {

		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.templates}", "error", pqErrMsg(err)))

	}
	return out, nil
}

// GetTemplate retrieves a given template.
func (c *Core) GetTemplate(id int, noBody bool) (models.Template, error) {
	var out []models.Template
	if err := c.q.GetTemplates.Select(&out, id, noBody, ""); err != nil {
		return models.Template{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.templates}", "error", pqErrMsg(err)))
	}

	if len(out) == 0 {
		return models.Template{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	return out[0], nil
}

// CreateTemplate creates a new template.
func (c *Core) CreateTemplate(name, typ, subject string, body []byte, category int, additional_values string, msgType string, templateParams types.JSONText, segmentConfig models.TemplateSegmentConfig, deDuplication bool, duplicationLevel string, processDuration int, createdBy null.String) (models.Template, error) {
	var newID int
	if err := c.q.CreateTemplate.Get(&newID, name, typ, subject, body, category, additional_values, msgType, templateParams, segmentConfig, deDuplication, duplicationLevel, processDuration, createdBy); err != nil {
		return models.Template{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	return c.GetTemplate(newID, false)
}

// UpdateTemplate updates a given template.
func (c *Core) UpdateTemplate(id int, name, subject string, body []byte, category int, additional_values string, msgType string, templateParams types.JSONText, segmentConfig models.TemplateSegmentConfig, deDuplication bool, duplicationLevel string, processDuration int, updatedBy null.String) (models.Template, error) {
	res, err := c.q.UpdateTemplate.Exec(id, name, subject, body, category, additional_values, msgType, templateParams, segmentConfig, deDuplication, duplicationLevel, processDuration, updatedBy)
	if err != nil {
		return models.Template{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	if n, _ := res.RowsAffected(); n == 0 {
		return models.Template{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	return c.GetTemplate(id, false)
}

// SetDefaultTemplate sets a template as default.
func (c *Core) SetDefaultTemplate(id int, updatedBy null.String) error {
	if _, err := c.q.SetDefaultTemplate.Exec(id, updatedBy); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	return nil
}

// DeleteTemplate deletes a given template.
func (c *Core) DeleteTemplate(id int, updatedBy null.String) error {
	var delID int
	if err := c.q.DeleteTemplate.Get(&delID, id, updatedBy); err != nil && err != sql.ErrNoRows {
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorDeleting", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}
	if delID == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("templates.cantDeleteDefault"))
	}

	return nil
}

func (c *Core) GetContentTeplate(id int) ([]models.ContentTemplateModel, error) {
	out := []models.ContentTemplateModel{}

	if err := c.q.GetContentTeplate.Select(&out, id); err != nil {
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.templates}", "error", pqErrMsg(err)))

	}
	return out, nil
}

// create entry in content_templates table
func (c *Core) CreateContentTemplate(source, name, htmlData string) (models.ContentTemplateModel, error) {
	var newID int
	if err := c.q.CreateContentTemplate.Get(&newID, source, name, htmlData); err != nil {
		return models.ContentTemplateModel{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	//Get content template details by id
	val, err := c.GetContentTeplate(newID)

	if len(val) == 0 || err != nil {
		return models.ContentTemplateModel{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	return val[0], nil
}

func (c *Core) UpdateContentTemplate(id int, content string, name, htmlData string) (models.ContentTemplateModel, error) {
	res, err := c.q.UpdateContentTemplate.Exec(id, content, name, htmlData)
	if err != nil {
		return models.ContentTemplateModel{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	if n, _ := res.RowsAffected(); n == 0 {
		return models.ContentTemplateModel{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	val, err := c.GetContentTeplate(id)

	if len(val) == 0 || err != nil {
		return models.ContentTemplateModel{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	return val[0], nil
}

func (c *Core) DeleteContentTemplate(id int) error {
	var delID int
	if err := c.q.DeleteContentTemplate.Get(&delID, id); err != nil && err != sql.ErrNoRows {
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorDeleting", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}
	if delID == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("templates.cantDeleteDefault"))
	}

	return nil
}
