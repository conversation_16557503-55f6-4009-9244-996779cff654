package core

import (
	"net/http"

	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	push "github.com/phuslu/log"
)

// GetCategories gets all categories.
func (c *Core) GetCategories(logger push.Logger) ([]models.Category, error) {
	out := []models.Category{}

	if err := c.q.GetCategories.Select(&out); err != nil {
		logger.Error().Msgf("error fetching categories: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.categories}", "error", pqErrMsg(err)))
	}

	return out, nil
}

// QueryCategories gets multiple categories based on multiple query params. Along with the  paginated and sliced
// results, the total number of categories in the DB is returned.
func (c *Core) QueryCategory(searchStr, orderBy, order string, offset, limit int, logger push.Logger) ([]models.Category, int, error) {
	out := []models.Category{}

	queryStr, stmt := makeSearchQuery(searchStr, orderBy, order, c.q.QueryCategories)

	if err := c.db.Select(&out, stmt, 0, queryStr, offset, limit); err != nil {
		logger.Error().Msgf("error fetching categories: %v", err)
		return nil, 0, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.categories}", "error", pqErrMsg(err)))
	}

	total := 0
	if len(out) > 0 {
		total = out[0].Total
	}

	return out, total, nil
}

// GetCategory gets a category by its ID.
func (c *Core) GetCategory(id int, logger push.Logger) (models.Category, error) {
	var res []models.Category
	queryStr, stmt := makeSearchQuery("", "", "", c.q.QueryCategories)
	if err := c.db.Select(&res, stmt, id, queryStr, 0, 1); err != nil {
		logger.Error().Msgf("error fetching categories: %v", err)
		return models.Category{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.categories}", "error", pqErrMsg(err)))
	}

	if len(res) == 0 {
		return models.Category{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.categories}"))
	}

	out := res[0]

	return out, nil
}

// CreateCategory creates a new category.
func (c *Core) CreateCategory(l models.Category, logger push.Logger) (models.Category, error) {

	// Insert and read ID.
	var newID int
	if err := c.q.CreateCategory.Get(&newID, l.Name, l.IsToggleable, l.IsVisibleToMerchant, l.NotificationDefaults, l.Description.String, l.FrequencyDetailsJSON, l.CreatedBy); err != nil {
		logger.Error().Msgf("error creating category: %v", err)
		return models.Category{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.categories}", "error", pqErrMsg(err)))
	}

	return c.GetCategory(newID, logger)
}

// UpdateCategory updates a given category.
func (c *Core) UpdateCategory(id int, l models.Category, logger push.Logger) (models.Category, error) {
	res, err := c.q.UpdateCategory.Exec(id, l.Name, l.IsToggleable, l.IsVisibleToMerchant, l.NotificationDefaults, l.Description.String, l.FrequencyDetailsJSON, l.UpdatedBy)
	if err != nil {
		logger.Error().Msgf("error updating category: %v", err)
		return models.Category{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.categories}", "error", pqErrMsg(err)))
	}

	if n, _ := res.RowsAffected(); n == 0 {
		return models.Category{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.categories}"))
	}

	return c.GetCategory(id, logger)
}

// DeleteCategory deletes a category.
func (c *Core) DeleteCategory(id int, logger push.Logger, updatedBy string) error {
	return c.DeleteCategories([]int{id}, logger, updatedBy)
}

// DeleteCategories deletes multiple category.
func (c *Core) DeleteCategories(ids []int, logger push.Logger, updatedBy string) error {
	if _, err := c.q.DeleteCategory.Exec(pq.Array(ids), updatedBy); err != nil {
		logger.Error().Msgf("error deleting categories: %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorDeleting", "name", "{globals.terms.categories}", "error", pqErrMsg(err)))
	}
	return nil
}
