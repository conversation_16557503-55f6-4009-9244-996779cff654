package core

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofrs/uuid"
	"github.com/jmoiron/sqlx"
	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	push "github.com/phuslu/log"
	"gopkg.in/volatiletech/null.v6"
)

const (
	CampaignAnalyticsViews   = "views"
	CampaignAnalyticsClicks  = "clicks"
	CampaignAnalyticsBounces = "bounces"

	campaignTplDefault = "default"
	campaignTplArchive = "archive"
)

// QueryCampaigns retrieves paginated campaigns optionally filtering them by the given arbitrary
// query expression. It also returns the total number of records in the DB.
func (c *Core) QueryCampaigns(searchStr string, statuses []string, orderBy, order string, offset, limit int, logger push.Logger) (models.Campaigns, int, error) {
	queryStr, stmt := makeSearchQuery(searchStr, orderBy, order, c.q.QueryCampaigns)

	if statuses == nil {
		statuses = []string{}
	}

	var createdFromTime *time.Time
	var createdToTime *time.Time
	var startedFromTime *time.Time
	var startedToTime *time.Time
	var endFromTime *time.Time
	var endToTime *time.Time

	loc := time.Local
	createdFrom := ""
	layout := "2006-01-02 15:04:05"
	if createdFrom != "" {
		parsedTime, err := time.ParseInLocation(layout, createdFrom, loc)
		logger.Info().Msgf("parsed createdFrom: %v", parsedTime)
		if err != nil {
			logger.Error().Msgf("error while parsing createdFrom time: %v", err)
		}
		createdFromTime = &parsedTime

	} else {
		createdFromTime = nil
	}

	createdTo := ""
	if createdTo != "" {
		parsedTime, err := time.ParseInLocation(layout, createdTo, loc)
		logger.Info().Msgf("parsed createdTo: %v", parsedTime)
		if err != nil {
			logger.Error().Msgf("error while parsing createdTo time: %v", err)
		}
		createdToTime = &parsedTime

	} else {
		createdToTime = nil
	}

	startedFrom := ""
	if startedFrom != "" {
		parsedTime, err := time.ParseInLocation(layout, startedFrom, loc)
		if err != nil {
			logger.Error().Msgf("error while parsing startedFrom time: %v", err)
		}
		startedFromTime = &parsedTime
	} else {
		startedFromTime = nil
	}

	startedTo := ""
	if startedTo != "" {
		parsedTime, err := time.ParseInLocation(layout, startedTo, loc)
		if err != nil {
			logger.Error().Msgf("error while parsing startedTo time: %v", err)
		}
		startedToTime = &parsedTime
	} else {
		startedToTime = nil
	}

	endFrom := ""
	if endFrom != "" {
		parsedTime, err := time.ParseInLocation(layout, endFrom, loc)
		if err != nil {
			logger.Error().Msgf("error while parsing endFrom time: %v", err)
		}
		endFromTime = &parsedTime
	} else {
		endFromTime = nil
	}

	endTo := ""
	if endTo != "" {
		parsedTime, err := time.ParseInLocation(layout, endTo, loc)
		if err != nil {
			logger.Error().Msgf("error while parsing endTo time: %v", err)
		}
		endToTime = &parsedTime
	} else {
		endToTime = nil
	}

	// Unsafe to ignore scanning fields not present in models.Campaigns.
	var out models.Campaigns
	if err := c.db.Select(&out, stmt, 0, pq.Array(statuses), queryStr, "", createdFromTime, startedFromTime, false, offset, limit, endFromTime, createdToTime, startedToTime, endToTime); err != nil {
		logger.Error().Msgf("error fetching campaigns: %v", err)
		return nil, 0, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	for i := 0; i < len(out); i++ {
		// Replace null tags.
		if out[i].Tags == nil {
			out[i].Tags = []string{}
		}
	}

	// Lazy load stats.
	if err := out.LoadStats(c.q.GetCampaignStats); err != nil {
		logger.Error().Msgf("error fetching campaign stats: %v", err)
		return nil, 0, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaigns}", "error", pqErrMsg(err)))
	}

	total := 0
	if len(out) > 0 {
		total = out[0].Total
	}

	return out, total, nil
}

// GetCampaign retrieves a campaign.
func (c *Core) GetCampaign(id int, uuid string, logger push.Logger) (models.Campaign, error) {
	return c.getCampaign(id, uuid, campaignTplDefault, logger)
}

// GetArchivedCampaign retreives a campaign with the archive template body.
func (c *Core) GetArchivedCampaign(id int, uuid string, logger push.Logger) (models.Campaign, error) {
	out, err := c.getCampaign(id, uuid, campaignTplArchive, logger)
	if err != nil {
		return out, err
	}

	if !out.Archive {
		return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.campaign}"))
	}

	return out, nil
}

// getCampaign retrieves a campaign. If typlType=default, then the campaign's
// template body is returned as "template_body". If tplType="archive",
// the archive template is returned.
func (c *Core) getCampaign(id int, uuid string, tplType string, logger push.Logger) (models.Campaign, error) {
	// Unsafe to ignore scanning fields not present in models.Campaigns.
	var uu interface{}
	if uuid != "" {
		uu = uuid
	}

	var out models.Campaigns
	if err := c.q.GetCampaign.Select(&out, id, uu, tplType); err != nil {
		// if err := c.db.Select(&out, stmt, 0, pq.Array([]string{}), queryStr, 0, 1); err != nil {
		logger.Error().Msgf("error fetching campaign: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	if len(out) == 0 {
		return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.campaign}"))
	}
	// Added by Deepali
	if id != 0 {
		campAttribs, err := c.getCampaignAttribs(id, logger)
		if err != nil {
			logger.Error().Msgf("failed getting attributes data for campaign %v", err)
			out[0].CampAttribs = "[]"
			out[0].SubAttribs = "[]"
		} else {
			out[0].CampAttribs = campAttribs.Attribs
			var attribsList []models.Atribs
			err := json.Unmarshal([]byte(campAttribs.Attribs), &attribsList)
			if err != nil {
				logger.Error().Msgf("error occured while unmarshalling json %v", err)
				out[0].SubAttribs = "[]"
			}

			var subAttribs []models.Atribs
			for _, attrib := range attribsList {
				if strings.Contains(attrib.Text, "subscriber attribs") {
					subAttribs = append(subAttribs, attrib)
				}
			}
			subAtt, err := json.Marshal(subAttribs)

			if err != nil {
				logger.Error().Msgf("error occured while unmarshalling json %v", err)
				out[0].SubAttribs = "[]"
			} else if len(subAttribs) > 0 {
				out[0].SubAttribs = string(subAtt)
			} else {
				out[0].SubAttribs = "[]"
			}
		}
	}
	// Ended by Deepali

	for i := 0; i < len(out); i++ {
		// Replace null tags.
		if out[i].Tags == nil {
			out[i].Tags = []string{}
		}
	}

	// Lazy load stats.
	if err := out.LoadStats(c.q.GetCampaignStats); err != nil {
		logger.Error().Msgf("error fetching campaign stats: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}
	return out[0], nil
}

func (c *Core) ReplaceSubscriberAttribs(campaignAttribsMasterId int, campaign *models.Campaign, logger push.Logger, attribs string) {
	//to remove Subscriber Attribs
	var attribsList []models.Atribs
	err := json.Unmarshal([]byte(attribs), &attribsList)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
	}

	var updatedAttribsList []models.Atribs
	for _, attrib := range attribsList {
		if !strings.Contains(attrib.Text, "subscriber attribs") {
			updatedAttribsList = append(updatedAttribsList, attrib)
		}
	}

	//to append Subscriber Attribs
	subAttribs, _ := c.AppendSubscriberAttribs(logger, &updatedAttribsList)

	if subAttribs == "" {
		campaign.SubAttribs = "[]"
	} else {
		campaign.SubAttribs = subAttribs
	}

	//to update Subscriber Attribs
	campaignAttribs, err := json.Marshal(updatedAttribsList)
	if err != nil {
		logger.Error().Msgf("error occured while marshalling json %v", err)
	}
	c.UpdateCampaignAttribs(campaignAttribsMasterId, string(campaignAttribs), logger)
}

// append SubscriberAttributes List to campaigns attribs master table
func (c *Core) AppendSubscriberAttribs(logger push.Logger, attribsList *[]models.Atribs) (string, map[string]string) {
	attribMap := make(map[string]string)
	vals, err := c.getSubscriberAttribs(logger)
	if err != nil {
		logger.Error().Msgf("error fetching subscriber attributes %v", err)
		return "", attribMap
	}
	attribs := models.Atribs{}
	subAttribs := []models.Atribs{}
	for _, v := range vals {
		attribs.Text = "subscriber attribs " + v
		attribs.Value = "{{ .Subscriber.Attribs." + v + " }}"
		attribMap[attribs.Value] = attribs.Text
		*attribsList = append(*attribsList, attribs)
		subAttribs = append(subAttribs, attribs)
	}

	subAttrib, err := json.Marshal(subAttribs)
	if err != nil {
		logger.Error().Msgf("error occured while marshalling json %v", err)
		return "", attribMap
	}

	logger.Info().Msg("created subscribers attributes")

	return string(subAttrib), attribMap

}

// GetCampaignForPreview retrieves a campaign with a template body.
func (c *Core) GetCampaignForPreview(id, tplID int, logger push.Logger) (models.Campaign, error) {
	var out models.Campaign
	if err := c.q.GetCampaignForPreview.Get(&out, id, tplID); err != nil {
		if err == sql.ErrNoRows {
			return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest,
				c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.campaign}"))
		}

		logger.Error().Msgf("error fetching campaign: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	return out, nil
}

// GetArchivedCampaigns retrieves campaigns with a template body.
func (c *Core) GetArchivedCampaigns(offset, limit int, logger push.Logger) (models.Campaigns, int, error) {
	var out models.Campaigns
	if err := c.q.GetArchivedCampaigns.Select(&out, offset, limit); err != nil {
		logger.Error().Msgf("error fetching public campaigns: %v", err)
		return models.Campaigns{}, 0, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	total := 0
	if len(out) > 0 {
		total = out[0].Total
	}

	return out, total, nil
}

// CreateCampaign creates a new campaign.
func (c *Core) CreateCampaign(o models.Campaign, listIDs []int, logger push.Logger) (models.Campaign, error) {
	uu, err := uuid.NewV4()
	if err != nil {
		logger.Error().Msgf("error generating UUID: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUUID", "error", err.Error()))
	}

	jsonData, err := json.Marshal(o.KeyVal)
	if err != nil {
		fmt.Println("error:", err)
	}
	// Insert and read ID.
	var newID int
	if err := c.q.CreateCampaign.Get(&newID,
		uu,
		o.Type,
		o.Name,
		o.Subject,
		o.FromEmail,
		o.Body,
		o.AltBody,
		o.ContentType,
		o.SendAt,
		o.Headers,
		pq.StringArray(normalizeTags(o.Tags)),
		o.Messenger,
		o.TemplateID,
		pq.Array(listIDs),
		o.Archive,
		o.ArchiveTemplateID,
		o.ArchiveMeta,
		o.Cron,
		o.FcmImage,
		string(jsonData),
		o.FCMRoles,
		o.Category,
		o.FCMCta,
		o.DeDuplication,
		o.DuplicationLevel,
		o.EndAt,
		o.ConversionEventName,
		o.ConversionEventTime,
		"",
		o.CampaignType,
		o.IgnoreFrequencyCap,
		o.CreatedBy,
	); err != nil {
		if err == sql.ErrNoRows {
			return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("campaigns.noSubs"))
		}

		logger.Error().Msgf("error creating campaign: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	out, err := c.GetCampaign(newID, "", logger)
	if err != nil {
		return models.Campaign{}, err
	}

	return out, nil
}

func (c *Core) CreateFileCampaign(o models.Campaign, toSend int, status string, logger push.Logger) (models.Campaign, error) {
	uu, err := uuid.NewV4()
	if err != nil {
		logger.Error().Msgf("error generating UUID: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUUID", "error", err.Error()))
	}

	jsonData, err := json.Marshal(o.KeyVal)
	if err != nil {
		fmt.Println("error:", err)
	}
	// Insert and read ID.
	var newID int
	if err := c.q.CreateFileCampaign.Get(&newID,
		uu,
		o.Type,
		o.Name,
		o.Subject,
		o.FromEmail,
		o.Body,
		o.AltBody,
		o.ContentType,
		o.SendAt,
		o.Headers,
		pq.StringArray(normalizeTags(o.Tags)),
		o.Messenger, //12
		o.TemplateID,
		o.Archive,
		o.ArchiveTemplateID,
		o.ArchiveMeta,
		o.Cron,
		o.FcmImage,
		string(jsonData),
		o.FCMRoles,
		o.Category,
		o.FCMCta,
		o.DeDuplication,
		o.DuplicationLevel,
		o.EndAt,
		o.FilePath,
		toSend,
		o.ReportName,
		status,
		o.ConversionEventName,
		o.ConversionEventTime,
		"",
		o.CampaignType,
		o.IgnoreFrequencyCap,
		o.CreatedBy,
	); err != nil {
		if err == sql.ErrNoRows {
			logger.Error().Msgf("Error during file campaign creation %v", err)
			return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("campaigns.noSubs"))
		}

		logger.Error().Msgf("error creating campaign: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	out, err := c.GetCampaign(newID, "", logger)
	if err != nil {
		return models.Campaign{}, err
	}

	return out, nil
}

// UpdateCampaign updates a campaign.
func (c *Core) UpdateCampaign(id int, o models.Campaign, listIDs []int, sendLater bool, additionalValues string, logger push.Logger) (models.Campaign, error) {
	_, err := c.q.UpdateCampaign.Exec(id,
		o.Name,
		o.Subject,
		o.FromEmail,
		o.Body,
		o.AltBody,
		o.ContentType,
		o.SendAt,
		sendLater,
		o.Headers,
		pq.StringArray(normalizeTags(o.Tags)),
		o.Messenger,
		o.TemplateID,
		pq.Array(listIDs),
		o.Archive,
		o.ArchiveTemplateID,
		o.ArchiveMeta,
		o.Cron,
		o.FcmImage,
		additionalValues,
		o.FCMRoles,
		o.Category,
		o.FCMCta,
		o.DeDuplication,
		o.DuplicationLevel,
		o.EndAt,
		o.FilePath,
		o.ReportName,
		o.ConversionEventName,
		o.ConversionEventTime,
		o.CampaignType,
		o.IgnoreFrequencyCap,
		o.UpdatedBy)
	if err != nil {
		logger.Error().Msgf("error updating campaign: %v", err)
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	//to update campaign attribs master in update flow
	if id != 0 {
		campAttribs, err := c.getCampaignAttribs(id, logger)
		if err != nil {
			logger.Error().Msgf("failed getting attributes data for campaign %v", err)
		} else {
			//replace subscriber attribs in campaign attribs master
			c.ReplaceSubscriberAttribs(campAttribs.ID, &o, logger, campAttribs.Attribs)
		}
	}
	out, err := c.GetCampaign(id, "", logger)
	if err != nil {
		return models.Campaign{}, err
	}

	if o.SubAttribs != "" {
		out.SubAttribs = o.SubAttribs
	} else {
		out.SubAttribs = "[]"
	}

	return out, nil
}

// UpdateCampaignStatus updates a campaign's status, eg: draft to running.
func (c *Core) UpdateCampaignStatus(id int, status string, logger push.Logger, updatedBy null.String) (models.Campaign, error) {

	cm, err := c.GetCampaign(id, "", logger)
	if err != nil {
		return models.Campaign{}, err
	}

	errMsg := ""
	switch status {
	case models.CampaignStatusDraft:
		if cm.Status != models.CampaignStatusScheduled {
			errMsg = c.i18n.T("campaigns.onlyScheduledAsDraft")
		}
	case models.CampaignStatusScheduled:
		if cm.Status != models.CampaignStatusDraft {
			errMsg = c.i18n.T("campaigns.onlyDraftAsScheduled")
		}
		if !cm.SendAt.Valid {
			errMsg = c.i18n.T("campaigns.needsSendAt")
		}

	case models.CampaignStatusRunning:
		if cm.Status != models.CampaignStatusPaused && cm.Status != models.CampaignStatusDraft {
			errMsg = c.i18n.T("campaigns.onlyPausedDraft")
		}
	case models.CampaignStatusPaused:
		if cm.Status != models.CampaignStatusRunning {
			errMsg = c.i18n.T("campaigns.onlyActivePause")
		}
	case models.CampaignStatusCancelled:
		if cm.Status != models.CampaignStatusRunning && cm.Status != models.CampaignStatusPaused {
			errMsg = c.i18n.T("campaigns.onlyActiveCancel")
		}
	}

	if len(errMsg) > 0 {
		return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest, errMsg)
	}

	res, err := c.q.UpdateCampaignStatus.Exec(cm.ID, status, updatedBy)
	if err != nil {
		return models.Campaign{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	if n, _ := res.RowsAffected(); n == 0 {
		return models.Campaign{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	cm.Status = status
	return cm, nil
}

// UpdateCampaignArchive updates a campaign's archive properties.
func (c *Core) UpdateCampaignArchive(id int, enabled bool, tplID int, meta models.JSON, logger push.Logger, updatedBy null.String) error {
	if _, err := c.q.UpdateCampaignArchive.Exec(id, enabled, tplID, meta, updatedBy); err != nil {
		logger.Error().Msgf("error updating campaign: %v", err)

		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	return nil
}

// DeleteCampaign deletes a campaign.
func (c *Core) DeleteCampaign(id int, logger push.Logger, updatedBy null.String) error {
	res, err := c.q.DeleteCampaign.Exec(id, updatedBy)
	if err != nil {
		logger.Error().Msgf("error deleting campaign: %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorDeleting", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))

	}

	if n, _ := res.RowsAffected(); n == 0 {
		return echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.campaign}"))
	}

	return nil
}

// GetRunningCampaignStats returns the progress stats of running campaigns.
func (c *Core) GetRunningCampaignStats(logger push.Logger) ([]models.CampaignStats, error) {
	out := []models.CampaignStats{}
	if err := c.q.GetCampaignStatus.Select(&out, models.CampaignStatusRunning); err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}

		logger.Error().Msgf("error fetching campaign stats: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	} else if len(out) == 0 {
		return nil, nil
	}

	return out, nil
}

func (c *Core) GetCampaignAnalyticsCounts(campIDs []int, typ, fromDate, toDate string, logger push.Logger) ([]models.CampaignAnalyticsCount, error) {
	// Pick campaign view counts or click counts.
	var stmt *sqlx.Stmt
	switch typ {
	case "views":
		stmt = c.q.GetCampaignViewCounts
	case "clicks":
		stmt = c.q.GetCampaignClickCounts
	case "bounces":
		stmt = c.q.GetCampaignBounceCounts
	default:
		return nil, echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("globals.messages.invalidData"))
	}

	if !strHasLen(fromDate, 10, 30) || !strHasLen(toDate, 10, 30) {
		return nil, echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("analytics.invalidDates"))
	}

	out := []models.CampaignAnalyticsCount{}
	if err := stmt.Select(&out, pq.Array(campIDs), fromDate, toDate); err != nil {
		logger.Error().Msgf("error fetching campaign %s: %v", typ, err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.analytics}", "error", pqErrMsg(err)))
	}

	return out, nil
}

// GetCampaignAnalyticsLinks returns link click analytics for the given campaign IDs.
func (c *Core) GetCampaignAnalyticsLinks(campIDs []int, typ, fromDate, toDate string, logger push.Logger) ([]models.CampaignAnalyticsLink, error) {
	out := []models.CampaignAnalyticsLink{}
	if err := c.q.GetCampaignLinkCounts.Select(&out, pq.Array(campIDs), fromDate, toDate); err != nil {
		logger.Error().Msgf("error fetching campaign %s: %v", typ, err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.analytics}", "error", pqErrMsg(err)))
	}

	return out, nil
}

// RegisterCampaignView registers a subscriber's view on a campaign.
func (c *Core) RegisterCampaignView(campUUID, subUUID string, logger push.Logger) error {
	if _, err := c.q.RegisterCampaignView.Exec(campUUID, subUUID); err != nil {
		logger.Error().Msgf("error registering campaign view: %s", err)
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}
	return nil
}

// RegisterCampaignLinkClick registers a subscriber's link click on a campaign.
func (c *Core) RegisterCampaignLinkClick(linkUUID, campUUID, subUUID string, logger push.Logger) (string, error) {
	var url string
	if err := c.q.RegisterLinkClick.Get(&url, linkUUID, campUUID, subUUID); err != nil {
		if pqErr, ok := err.(*pq.Error); ok && pqErr.Column == "link_id" {
			return "", echo.NewHTTPError(http.StatusBadRequest, c.i18n.Ts("public.invalidLink"))
		}

		logger.Error().Msgf("error registering link click: %s", err)
		return "", echo.NewHTTPError(http.StatusInternalServerError, c.i18n.Ts("public.errorProcessingRequest"))
	}

	return url, nil
}

// DeleteCampaignViews deletes campaign views older than a given date.
func (c *Core) DeleteCampaignViews(before time.Time, logger push.Logger) error {
	if _, err := c.q.DeleteCampaignViews.Exec(before); err != nil {
		logger.Error().Msgf("error deleting campaign views: %s", err)
		return echo.NewHTTPError(http.StatusInternalServerError, c.i18n.Ts("public.errorProcessingRequest"))
	}

	return nil
}

// DeleteCampaignLinkClicks deletes campaign views older than a given date.
func (c *Core) DeleteCampaignLinkClicks(before time.Time, logger push.Logger) error {
	if _, err := c.q.DeleteCampaignLinkClicks.Exec(before); err != nil {
		logger.Error().Msgf("error deleting campaign link clicks: %s", err)
		return echo.NewHTTPError(http.StatusInternalServerError, c.i18n.Ts("public.errorProcessingRequest"))
	}

	return nil
}

//Added by Deepali

// GetCampaign retrieves a campaign.
func (c *Core) getCampaignAttribs(camp int, logger push.Logger) (models.CampaignAtribsMaster, error) {
	var res []models.CampaignAtribsMaster
	if err := c.q.GetCampaignAttribs.Select(&res, camp); err != nil {
		logger.Error().Msgf("error fetching campaign attributes: %v", err)
		return models.CampaignAtribsMaster{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.lists}", "error", pqErrMsg(err)))
	}
	if len(res) == 0 {
		return models.CampaignAtribsMaster{}, nil
	}

	out := res
	return out[0], nil
}

// GetCampaign retrieves a campaign.
func (c *Core) getSubscriberAttribs(logger push.Logger) ([]string, error) {

	var res []string
	if err := c.q.GetSubscriberAttribs.Select(&res); err != nil {
		logger.Error().Msgf("error fetching subscriber attributes: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.lists}", "error", pqErrMsg(err)))
	}
	out := res
	return out, nil
}

// Create campaign attribs
func (c *Core) CreateCampaignAttribs(a models.CampaignAtribsMaster, logger push.Logger) (string, error) {
	// Insert and read ID.
	var attribs string
	if err := c.q.CreateCampaignAttribs.Get(&attribs, a.CampaignId, a.Attribs); err != nil {
		logger.Error().Msgf("error creating campaign attribs: %v", err)
		return "", echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.list}", "error", pqErrMsg(err)))
	}
	return attribs, nil
}

//Ended by Deepali

func (c *Core) UpdateCampaignAttribs(campaignAttribsMasterId int, attribs string, logger push.Logger) (models.CampaignAtribsMaster, error) {
	var campaignAttribs models.CampaignAtribsMaster
	if _, err := c.q.UpdateCampaignAttribs.Exec(campaignAttribsMasterId, attribs); err != nil {
		logger.Error().Msgf("error updating campaign attribs: %v", err)
		return models.CampaignAtribsMaster{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.list}", "error", pqErrMsg(err)))
	}
	return campaignAttribs, nil
}

func (c *Core) QueryCampaignsWithFilter(o models.CampaignsSearchFilter, offset, limit int, logger push.Logger) (models.Campaigns, int, error) {

	var num int64

	var statusSlice []string

	if o.Status != "" {
		// Split the comma-separated string into a string slice
		statusSlice = strings.Split(o.Status, ",")
	}

	if n, err := strconv.ParseInt(strings.ReplaceAll(o.SearchQuery, " ", ""), 10, 64); err == nil {
		num = n
		o.SearchQuery = ""
	} else {
		num = 0
	}

	var key string
	var value []string
	if o.FcmKeyVal != nil {
		key = o.FcmKeyVal.Key
		value = o.FcmKeyVal.Value
	}
	queryStr, stmt := makeSearchQuery(o.SearchQuery, o.OrderBy, o.Order, c.q.QueryCampaigns)

	if statusSlice == nil {
		statusSlice = []string{}
	}

	// Parse time range filters
	loc := time.Local
	createdFromTime := parseTime(o.CreatedFrom, loc, "createdFrom", logger)
	createdToTime := parseTime(o.CreatedTo, loc, "createdTo", logger)
	startedFromTime := parseTime(o.StartedFrom, loc, "startedFrom", logger)
	startedToTime := parseTime(o.StartedTo, loc, "startedTo", logger)
	endFromTime := parseTime(o.EndFrom, loc, "endFrom", logger)
	endToTime := parseTime(o.EndTo, loc, "endTo", logger)

	logger.Info().Msgf("parsed created at: %v %v %v %v", createdFromTime, createdToTime, startedFromTime, startedToTime)

	var out models.Campaigns
	if err := c.db.Select(&out, stmt, num, pq.Array(statusSlice), queryStr, o.Channel, createdFromTime, startedFromTime, o.RecurringFlag, offset, limit, endFromTime, createdToTime, startedToTime, endToTime, key, pq.Array(value), o.CampaignType); err != nil {
		logger.Error().Msgf("error fetching campaigns: %v", err)
		return nil, 0, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaign}", "error", pqErrMsg(err)))
	}

	for i := 0; i < len(out); i++ {
		// Replace null tags.
		if out[i].Tags == nil {
			out[i].Tags = []string{}
		}
	}

	// Lazy load stats.
	if err := out.LoadStats(c.q.GetCampaignStats); err != nil {
		logger.Error().Msgf("error fetching campaign stats: %v", err)
		return nil, 0, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.campaigns}", "error", pqErrMsg(err)))
	}

	total := 0
	if len(out) > 0 {
		total = out[0].Total
	}

	return out, total, nil
}

func parseTime(timeStr string, loc *time.Location, timeField string, logger push.Logger) *time.Time {
	if timeStr == "" {
		return nil
	}
	layout := "2006-01-02 15:04:05"
	parsedTime, err := time.ParseInLocation(layout, timeStr, loc)
	if err != nil {
		logger.Error().Msgf("error while parsing %s: %v", timeField, err)
		return nil
	}
	return &parsedTime
}
