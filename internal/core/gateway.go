package core

import (
	"database/sql"
	"net/http"

	"github.com/jmoiron/sqlx/types"
	"github.com/knadh/listmonk/logger"
	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	"gopkg.in/volatiletech/null.v6"
)

func (c *Core) GetGatewayDetails(gatewayNames map[string]interface{}) ([]models.GateWayDetails, error) {
	out := []models.GateWayDetails{}

	gatString := c.getSubQueryString(gatewayNames)
	if len(gatString) == 0 {
		return out, nil
	}
	query := `with gate as (select * from ` + gatString + `) `
	query += `select gd.* from gateway_details gd join gate on gate.messenger = gd.messenger and gate.name= gd.name  union 
	(select * from gateway_details where method= 'smtp' and configuration->>'enabled'='true')`

	logger.Log.Info().Msgf("Gateway query %v", query)
	stmt, _ := c.db.Preparex(query)
	if err := stmt.Select(&out); err != nil {
		logger.Log.Error().Msgf("error occured %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.templates}", "error", pqErrMsg(err)))
	}

	return out, nil

}

func (c *Core) GetSMTPDetails() ([]models.GateWayDetails, error) {
	out := []models.GateWayDetails{}

	query := `select * from gateway_details where method= 'smtp'`

	logger.Log.Info().Msgf("Gateway query %v", query)
	stmt, err := c.db.Preparex(query)
	if err != nil {
		logger.Log.Error().Msgf("Error preparing statement: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError, "Error preparing statement")
	}

	if err := stmt.Select(&out); err != nil {
		logger.Log.Error().Msgf("error occured %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.templates}", "error", pqErrMsg(err)))
	}
	return out, nil
}

/*
 *with gate as (select * from (values('fcm','acl'),('sms','karix'),('email','karix'),('sms','acl')) as k(messenger, name))
 *select gd.id, gd.name, gd.messenger from gateway_details gd join gate on gate.messenger = gd.messenger and gate.name= gd.name;
 */

func (c *Core) getSubQueryString(gatewayNames map[string]interface{}) string {

	length := len(gatewayNames)
	if length == 0 {
		return ""
	}
	length -= 1
	subsString := "(values"
	gatIter := 0
	for key, value := range gatewayNames {
		resultMap, ok := value.([]interface{})
		if !ok {
			panic("List of string required in gatewayConfig messenger")
		}

		var stringSlice []string

		for _, val := range resultMap {
			if str, ok := val.(string); ok {
				stringSlice = append(stringSlice, str)
			} else {
				// Handle the case where the type assertion fails
				panic("String value not found in gatewayConfig messenger")
			}
		}

		valLength := len(stringSlice) - 1
		for j, v := range stringSlice {
			subsString += "('" + key + "'," + "'" + v + "')"
			if j == valLength && gatIter == length {
				subsString += ") as k (messenger, name)"
			} else {
				subsString += ","
			}
		}
		gatIter++
	}

	return subsString
}

func (c *Core) GetGateway(id int) ([]models.GateWayDetails, error) {
	out := []models.GateWayDetails{}

	if err := c.q.GetGateway.Select(&out, id); err != nil {
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.templates}", "error", pqErrMsg(err)))

	}
	return out, nil
}

// createGateway creates a new gateway.
func (c *Core) CreateGateway(requestParams, headers, url, requestBody, method, messenger, name string, configuration types.JSONText, isdefault bool, createdBy null.String) (models.GateWayDetails, error) {
	var newID int
	if err := c.q.CreateGateway.Get(&newID, requestParams, headers, url, requestBody, method, messenger, name, configuration, isdefault, createdBy); err != nil {
		return models.GateWayDetails{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorCreating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	val, err := c.GetGateway(newID)

	if len(val) == 0 || err != nil {
		return models.GateWayDetails{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	return val[0], nil
}

// UpdateGateway updates a given gateway.
func (c *Core) UpdateGateway(id int, gd models.GateWayDetails) (models.GateWayDetails, error) {
	res, err := c.q.UpdateGateway.Exec(id, gd.RequestParams.String, gd.Headers.String, gd.Url.String, gd.RequestBody.String, gd.Method.String, gd.Messenger.String, gd.Name.String, gd.Configuration, gd.IsDefault, gd.UpdatedBy)
	if err != nil {
		return models.GateWayDetails{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorUpdating", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}

	if n, _ := res.RowsAffected(); n == 0 {
		return models.GateWayDetails{}, echo.NewHTTPError(http.StatusBadRequest,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	val, err := c.GetGateway(id)

	if len(val) == 0 || err != nil {
		return models.GateWayDetails{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}

	return val[0], nil
}

func (c *Core) GetSmtpGateway(uuid string) (models.GateWayDetails, error) {

	var out models.GateWayDetails
	err := c.q.GetSmtpGateway.Get(&out, uuid)
	if err != nil {
		return models.GateWayDetails{}, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.template}"))
	}
	return out, nil
}

// DeleteGateway deletes a given gateway.
// TODO: set template_params to use default provider
func (c *Core) DeleteGateway(id int, updatedBy null.String) error {
	var delID int
	if err := c.q.DeleteGateway.Get(&delID, id, updatedBy); err != nil && err != sql.ErrNoRows {
		return echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorDeleting", "name", "{globals.terms.template}", "error", pqErrMsg(err)))
	}
	if delID == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, c.i18n.T("templates.cantDeleteDefault"))
	}

	return nil
}
