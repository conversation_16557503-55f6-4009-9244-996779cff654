package core

import (
	"fmt"
	"net/http"

	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

// GetNotificationFrequencies gets all notifications frequencies
func (c *Core) GetNotificationFrequencies(logger push.Logger) ([]models.NotificationFrequencies, error) {
	out := []models.NotificationFrequencies{}

	if err := c.q.GetNotificationFrequencies.Select(&out); err != nil {
		logger.Error().Msgf("error fetching notification frequencies: %v", err)
		return nil, echo.NewHTTPError(http.StatusInternalServerError,
			c.i18n.Ts("globals.messages.errorFetching", "name", "{globals.terms.notificationfrequencies}", "error", pqErrMsg(err)))
	}

	return out, nil
}

// Upsert Notification Frequency
func (c *Core) UpsertNotificationFrequency(l models.NotificationFrequencies, logger push.Logger) (models.NotificationFrequencies, error) {

	notificationFreq := models.NotificationFrequencies{}
	if err := c.q.UpsertNotificationFrequencies.Get(&notificationFreq, l.Notification, l.DisplayName, l.Messenger, l.EnableFreqCap, l.FrequencyDays, l.FrequencyValue, l.CreatedBy, l.IsNotificationEnabled, l.UpdatedBy, l.AdditionalDetails); err != nil {
		logger.Error().Msgf("error upserting channel frequency: %v", err)
		return models.NotificationFrequencies{}, fmt.Errorf("error upserting notification frequency")
	}

	return notificationFreq, nil
}
