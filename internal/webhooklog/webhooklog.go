package webhooklog

import (
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/knadh/listmonk/logger"
	"github.com/knadh/listmonk/models"
)

func InsertWebhookLogs(db *sqlx.DB, logs []models.WebhookLog) (int64, error) {
	lo := logger.Log
	if len(logs) == 0 {
		lo.Info().Msg("No logs to insert")
		return 0, fmt.Errorf("no logs to insert")
	}

	query := `
        INSERT INTO webhook_logs(
			id,
            message_id,
			provider,
            provider_status,
            delivery_status,
            messenger,
            event_time,
            provider_failure_reason,
            request_id
        ) VALUES (
		 	:id,
            :message_id,
			:provider,
            :provider_status,
            :delivery_status,
            :messenger,
            :event_time,
            :provider_failure_reason,
            :request_id
        )
    `

	result, err := db.NamedExec(query, logs)
	if err != nil {
		lo.Info().Msgf("Failed to execute insert %v", err.<PERSON><PERSON><PERSON>())
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		lo.Info().Msg("Failed to get rows affected")
		return 0, err
	}

	lo.Info().Msgf("Successfully inserted messenger logs with rows_inserted %v", rowsAffected)
	return rowsAffected, err
}
