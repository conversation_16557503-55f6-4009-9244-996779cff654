package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_3(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	CREATE TABLE IF NOT EXISTS subscriber_preference_logs (
		subscriber_id    INTEGER NOT NULL REFERENCES subscribers(id) ON DELETE CASCADE ON UPDATE CASCADE,
		category_id      INTEGER NOT NULL REFERENCES categories(id) ON DELETE CASCADE ON UPDATE CASCADE,
		channel          VARCHAR(20) NOT NULL,
		value 			 BOOLEAN NOT NULL,
		created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		created_by 		 <PERSON><PERSON><PERSON><PERSON>(20)
	);
	CREATE INDEX IF NOT EXISTS subscriber_id_index ON subscriber_preferences(subscriber_id, updated_at desc);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	CREATE OR REPLACE FUNCTION preference_history()
		RETURNS TRIGGER AS $$
			BEGIN
        		INSERT INTO subscriber_preference_logs (subscriber_id, category_id, channel,value, created_at, created_by)
        			VALUES (NEW.subscriber_id, NEW.category_id, NEW.channel, NEW.value, NOW(), NEW.updated_by);
    			RETURN NEW;
			END;
	$$ LANGUAGE plpgsql;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	CREATE TRIGGER preference_changes
		AFTER INSERT OR UPDATE ON subscriber_preferences
			FOR EACH ROW
			EXECUTE FUNCTION preference_history();
	`); err != nil {
		return err
	}

	return nil
}
