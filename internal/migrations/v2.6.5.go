package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_5(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE gateway_details ADD COLUMN IF NOT EXISTS "messenger" varchar(20)
	`); err != nil {
		return err
	}
	if _, err := db.Exec(`
	ALTER TABLE gateway_details ADD COLUMN IF NOT EXISTS "name" varchar(20)
	`); err != nil {
		return err
	}
	return nil
}
