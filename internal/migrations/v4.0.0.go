package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Add new column `campaign_type` to `campaigns` table
	if _, err := db.Exec(`
        ALTER TABLE messenger_logs ADD PRIMARY KEY (uuid);
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
        ALTER TYPE member_type_enum ADD VALUE 'customer';
    `); err != nil {
		return err
	}

	return nil
}
