package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// Created new file to run migration again
func V4_0_10(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	if _, err := db.Exec(`
		ALTER TABLE notification_frequencies ADD COLUMN IF NOT EXISTS "additional_details" JSONB NOT NULL DEFAULT '{}'
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		ALTER TABLE notification_frequency_audits ADD COLUMN IF NOT EXISTS "additional_details" JSONB NOT NULL DEFAULT '{}'
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION notification_frequency_audits_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO notification_frequency_audits (notification, display_name, messenger, enable_freq_cap, frequency_days, frequency_value, created_at, updated_at, created_by, updated_by, additional_details)
			VALUES (OLD.notification, OLD.display_name, OLD.messenger, OLD.enable_freq_cap, OLD.frequency_days, OLD.frequency_value, OLD.created_at, OLD.updated_at, OLD.created_by, OLD.updated_by, OLD.additional_details);
			RETURN NEW;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_notification_frequencies_update ON notification_frequencies;

		CREATE TRIGGER before_notification_frequencies_update
		BEFORE UPDATE ON notification_frequencies
		FOR EACH ROW
		EXECUTE FUNCTION notification_frequency_audits_insert();
	`); err != nil {
		return err
	}
	return nil
}
