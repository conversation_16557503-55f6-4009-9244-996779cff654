package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_3(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Add new columns `de_duplication` and `duplication_level` in `templates` table
	if _, err := db.Exec(`
        ALTER TABLE templates ADD COLUMN IF NOT EXISTS de_duplication boolean;
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
		ALTER TABLE templates ADD COLUMN IF NOT EXISTS duplication_level VARCHAR(24);
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
		ALTER TABLE templates ADD COLUMN IF NOT EXISTS process_duration INT DEFAULT 0;
    `); err != nil {
		return err
	}

	return nil
}
