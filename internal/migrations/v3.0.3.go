package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// V3_0_3 performs the DB migrations for v3.0.3.
func V3_0_3(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Begin a transaction
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Create the cta_deep_links table
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS deeplinks (
			id SERIAL PRIMARY KEY,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			category VARCHAR(255) DEFAULT '',
			deeplink TEXT,
			deeplinkname VARCHAR(255) UNIQUE,
			isactive BOOLEAN,
			created_by VARCHAR(255) DEFAULT '',
			updated_by VARCHAR(255) DEFAULT ''
		);
	`)
	if err != nil {
		return err
	}

	// Insert the default values
	_, err = tx.Exec(`
		INSERT INTO deeplinks (deeplinkname, deeplink, isactive, created_by, updated_by)
	VALUES
		('Payments Page', 'hdfc://mintoak.com/PaymentsFragment/fragment/0', true, '', ''),
		('Static QR', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/StaticBharatQrActivity/activity', true, '', ''),
		('TransactionsViewMore (Transaction Details Card)', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/TransactionsViewMoreActivity/activity', true, '', ''),
		('PayLaterCustomers (Pay Later View-All)', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/PayLaterCustomersActivity/activity', true, '', ''),
		('ThreeSixtyBizview', 'hdfc://mintoak.com/ThreeSixtyViewFragment/fragment/1', true, '', ''),
		('MyVyaparDashboard (Bizz Growth)', 'hdfc://mintoak.com/MyVyaparFragment/fragment/4', true, '', ''),
		('Transaction Page Summary', 'hdfc://mintoak.com/TransactionFragment/fragment/2', true, '', ''),
		('Settlement Page dashboard', 'hdfc://mintoak.com/CreditFragment/fragment/3', true, '', ''),
		('Reports Page dashboard', 'hdfc://mintoak.com/ReportsFragment/fragment/4', true, '', ''),
		('Reports- Settlement Generate', 'hdfc://mintoak.com/ReportsFragment/fragment/4/GenerateReportActivity/activity/?type=Settlement', true, '', ''),
		('Reports - Transaction Generate', 'hdfc://mintoak.com/ReportsFragment/fragment/4/GenerateReportActivity/activity/?type=Transaction', true, '', ''),
		('Biz Growth', 'hdfc://mintoak.com/MyVyaparFragment/fragment/4', true, '', ''),
		('Apply FD Page', 'hdfc://mintoak.com/MyVyaparFragment/fragment/4/ActivityApplyForFD/activity', true, '', ''),
		('Loans', 'hdfc://mintoak.com/MyVyaparFragment/fragment/4/MyVyaparBankingLoans/activity', true, '', ''),
		('Register V2P', 'hdfc://mintoak.com/MyVyaparFragment/fragment/4/ActivityRegisterV2P/activity', true, '', ''),
		('CC Apply', 'hdfc://mintoak.com/MyVyaparFragment/fragment/4/ActivityApplyForCC/activity', true, '', ''),
		('Profile Dashboard', 'hdfc://mintoak.com/ProfileFragment/fragment/4', true, '', ''),
		('User Access', 'hdfc://mintoak.com/ProfileFragment/fragment/4/UserAccessActivity/activity', true, '', ''),
		('Leader Board', 'hdfc://mintoak.com/ProfileFragment/fragment/4/LeaderBoardActivity/activity', true, '', ''),
		('Badges', 'hdfc://mintoak.com/ProfileFragment/fragment/4/BadgesActivity/activity', true, '', ''),
		('FAQ', 'hdfc://mintoak.com/ProfileFragment/fragment/4/FAQActivity/activity', true, '', ''),
		('Profile-Settings', 'hdfc://mintoak.com/ProfileFragment/fragment/4/SettingsActivity/activity', true, '', ''),
		('Profile-Store List', 'hdfc://mintoak.com/ProfileFragment/fragment/4/StoreListActivity/activity', true, '', ''),
		('Profile-Accounts', 'hdfc://mintoak.com/ProfileFragment/fragment/4/AccountActivity/activity', true, '', ''),
		('Profile-MyOrder', 'hdfc://mintoak.com/ProfileFragment/fragment/4/MyOrderActivity/activity', true, '', ''),
		('Profile-Personal', 'hdfc://mintoak.com/ProfileFragment/fragment/4/Profile_PersonalActivity/activity', true, '', ''),
		('Link Pay - SMS Pay', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/?type=c&paymentModeId=7', true, '', ''),
		('Bharat QR', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/?type=c&paymentModeId=2', true, '', ''),
		('Service pop-up (Profile)', 'hdfc://mintoak.com/ServicePopUp/fragment/4', true, '', ''),
		('Profile notification setting', 'hdfc://mintoak.com/ProfileFragment/fragment/4/Notification/activity', true, '', ''),
		('Initate TaponPhone Transaction (Payments - Card)', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/?type=c&paymentModeId=9', true, '', ''),
		('Initate Cash Transactions (Payments - cash)', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/?type=c&paymentModeId=4', true, '', ''),
		('RewardBox- Active', 'hdfc://mintoak.com/ProfileFragment/fragment/4/CampaignActivityRewardsAndBadges/activity?type=active', true, '', ''),
		('OAR - OfferHistory', 'hdfc://mintoak.com/OfferRewards/activity/1/?type=OfferHistory', true, '', ''),
		('OAR - Addoffer', 'hdfc://mintoak.com/OfferRewards/activity/1/?type=AddOffer', true, '', ''),
		('OAR - Dashboard', 'hdfc://mintoak.com/OfferRewards/activity/1', true, '', ''),
		('MAC - CreateCampaign', 'hdfc://mintoak.com/MacDashBoard/activity/1/?type=CreateCampaign', true, '', ''),
		('MAC - History', 'hdfc://mintoak.com/MacDashBoard/activity/1/?type=OfferHistoryMac', true, '', ''),
		('MAC OfferDraft', 'hdfc://mintoak.com/MacDashBoard/activity/1/?type=OfferHistoryMac', true, '', ''),
		('Rating - storefeedback page', 'hdfc://mintoak.com/StoresFeedback/activity/4', true, '', ''),
		('Previous Rewardbox', 'hdfc://mintoak.com/ProfileFragment/fragment/4/CampaignActivity/activity?type=previous', true, '', ''),
		('In app review', 'hdfc://mintoak.com/InAppReview/activity/4', true, '', ''),
		('Flexi settlement page', 'hdfc://mintoak.com/ProfileFragment/fragment/4/UPIFlexiSettlementActivity/activity', true, '', ''),
		('Performance graph', 'hdfc://mintoak.com/ThreeSixtyViewFragment/fragment/1/ActivityLineGraphList/activity', true, '', ''),
		('Sales Counter List', 'hdfc://mintoak.com/ThreeSixtyViewFragment/fragment/1/SalesCounterList/activity', true, '', ''),
		('Customer card List', 'hdfc://mintoak.com/ThreeSixtyViewFragment/fragment/1/Activity_CustomerCardList/activity', true, '', ''),
		('Paylater store list', 'hdfc://mintoak.com/ThreeSixtyViewFragment/fragment/1/PayLaterStoreListActivity/activity', true, '', ''),
		('Payments- UPI Collect', 'hdfc://mintoak.com/PaymentsFragment/fragment/0/?type=c&paymentModeId=3', true, '', '')
        ON CONFLICT (deeplinkName) DO NOTHING;
	`)
	if err != nil {
		return err
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return err
	}

	return nil
}
