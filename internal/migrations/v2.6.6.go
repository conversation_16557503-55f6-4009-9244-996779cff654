package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_6(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE templates ADD COLUMN IF NOT EXISTS "template_params" JSONB NOT NULL DEFAULT '{}'
	`); err != nil {
		return err
	}
	if _, err := db.Exec(`
	WITH templateParams AS (
		SELECT template_id, template_params
		FROM template_details
	  )
	  UPDATE templates tt SET template_params = tp.template_params
	  FROM templateParams tp
	  WHERE tt.id=  tp.template_id
	  `); err != nil {
		return err
	}
	return nil
}
