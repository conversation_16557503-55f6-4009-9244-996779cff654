package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_5_1(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS campaign_target_index ON public.campaign_targets USING btree (target);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	DROP INDEX IF EXISTS campaign_id_target_index
	`); err != nil {
		return err
	}
	return nil
}
