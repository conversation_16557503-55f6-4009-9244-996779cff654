package migrations

import (
	"strings"

	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// Database Id changes for subscriber table
func V2_8_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	var dataType string

	if err := db.Get(&dataType, `
	SELECT data_type
	FROM information_schema.columns
	WHERE table_name = 'subscribers' AND column_name = 'id';`); err != nil {
		return err
	}

	if strings.EqualFold(dataType, "bigint") {
		return nil
	}

	if err := changeExistingIdType(db); err != nil {
		return err
	}

	if err := changeSubscriberIdType(db); err != nil {
		return err
	}
	if err := updateUniqueIndex(db); err != nil {
		return err
	}
	if err := dropPreviousIndices(db); err != nil {
		return err
	}
	if err := createNewIndecies(db); err != nil {
		return err
	}

	if err := alterSequence(db); err != nil {
		return err
	}

	return nil

}

/*
 * This changes data types of columns where subscriber_id is foreign key
 */
func changeExistingIdType(db *sqlx.DB) error {

	if _, err := db.Exec(`ALTER TABLE subscriber_preferences ALTER COLUMN subscriber_id TYPE BIGINT USING subscriber_id::BIGINT;`); err != nil {
		return err
	}

	if _, err := db.Exec(`ALTER TABLE subscriber_preference_logs ALTER COLUMN subscriber_id TYPE BIGINT USING subscriber_id::BIGINT;`); err != nil {
		return err
	}

	if _, err := db.Exec(`ALTER TABLE subscriber_lists ALTER COLUMN subscriber_id TYPE BIGINT USING subscriber_id::BIGINT;`); err != nil {
		return err
	}

	if _, err := db.Exec(`ALTER TABLE link_clicks ALTER COLUMN subscriber_id TYPE BIGINT USING subscriber_id::BIGINT;`); err != nil {
		return err
	}

	if _, err := db.Exec(`ALTER TABLE campaign_views ALTER COLUMN subscriber_id TYPE BIGINT USING subscriber_id::BIGINT;`); err != nil {
		return err
	}

	if _, err := db.Exec(`ALTER TABLE bounces ALTER COLUMN subscriber_id TYPE BIGINT USING subscriber_id::BIGINT;`); err != nil {
		return err
	}

	return nil
}

func changeSubscriberIdType(db *sqlx.DB) error {

	if _, err := db.Exec(`ALTER TABLE subscribers ALTER COLUMN id TYPE BIGINT USING id::BIGINT;`); err != nil {
		return err
	}
	return nil
}

func updateUniqueIndex(db *sqlx.DB) error {
	if _, err := db.Exec(`drop index subscriber_preferences_subscriber_id_category_id_channel_idx;
	CREATE UNIQUE INDEX subscriber_preferences_subscriber_id_category_id_channel_idx ON subscriber_preferences USING btree (subscriber_id, category_id, channel);`); err != nil {
		return err
	}
	return nil

}

func dropPreviousIndices(db *sqlx.DB) error {
	if _, err := db.Exec(`
	drop index subscriber_id_index;
	drop index idx_sub_lists_sub_id;
	drop index idx_clicks_sub_id;
	drop index idx_views_subscriber_id;
	drop index idx_bounces_sub_id;
	`); err != nil {
		return err
	}
	return nil

}

func createNewIndecies(db *sqlx.DB) error {
	if _, err := db.Exec(`
	CREATE INDEX subscriber_id_index ON subscriber_preferences USING btree (subscriber_id);
	CREATE INDEX idx_sub_lists_sub_id ON subscriber_lists USING btree (subscriber_id);
	CREATE INDEX idx_clicks_sub_id ON link_clicks USING btree (subscriber_id);
	CREATE INDEX idx_views_subscriber_id ON campaign_views USING btree (subscriber_id);
	CREATE INDEX idx_bounces_sub_id ON bounces USING btree (subscriber_id);
	`); err != nil {
		return err
	}
	return nil

}

func alterSequence(db *sqlx.DB) error {
	if _, err := db.Exec(`
	ALTER SEQUENCE subscribers_id_seq as BIGINT increment by 1  MAXVALUE 9223372036854775807;
	`); err != nil {
		return err
	}
	return nil

}
