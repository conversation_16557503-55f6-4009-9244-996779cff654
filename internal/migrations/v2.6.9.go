package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_9(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE subscribers ALTER email set default ''
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	update subscribers set email ='' where email is null
	`); err != nil {
		return err
	}
	return nil
}
