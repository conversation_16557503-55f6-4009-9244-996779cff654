package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_1(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE categories ADD CONSTRAINT pk_categories_id PRIMARY KEY (id);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS subscriber_preferences (
		    subscriber_id    INTEGER NOT NULL REFERENCES subscribers(id) ON DELETE CASCADE ON UPDATE CASCADE,
		    category_id      INTEGER NOT NULL REFERENCES categories(id) ON DELETE CASCADE ON UPDATE CASCADE,
		    channel          VARCHAR(20) NOT NULL,
		    value 			 BOOLEAN NOT NULL,
		    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			created_by 		 VARCHAR(20),
			updated_by 		 VARCHAR(20)
		);
		CREATE UNIQUE INDEX ON subscriber_preferences (subscriber_id, category_id,channel);
		CREATE INDEX IF NOT EXISTS subscriber_id_index ON subscriber_preferences(subscriber_id);
	`); err != nil {
		return err
	}

	return nil
}
