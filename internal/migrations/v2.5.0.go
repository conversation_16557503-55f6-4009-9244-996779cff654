package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_5_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE campaign_targets ADD COLUMN IF NOT EXISTS "terminal_id" varchar
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE campaign_targets ADD COLUMN IF NOT EXISTS "fcm_token" varchar
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE campaign_targets ADD COLUMN IF NOT EXISTS "notification_label" varchar
	`); err != nil {
		return err
	}

	return nil
}
