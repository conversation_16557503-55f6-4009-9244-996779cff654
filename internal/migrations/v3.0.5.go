package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V3_0_5(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Check if the primary key constraint already exists
	var exists bool
	query := `
        SELECT EXISTS (
            SELECT 1
            FROM pg_constraint
            WHERE conname = 'subscriber_preferences_pk'
        );
    `
	if err := db.Get(&exists, query); err != nil {
		return err
	}

	// Add the constraint if it does not exist
	if !exists {
		if _, err := db.Exec(`
            ALTER TABLE subscriber_preferences 
            ADD CONSTRAINT subscriber_preferences_pk PRIMARY KEY (subscriber_id, category_id, channel);
        `); err != nil {
			return err
		}
	}

	return nil
}
