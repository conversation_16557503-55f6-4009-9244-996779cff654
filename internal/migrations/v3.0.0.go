package migrations

import (
	"encoding/json"

	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/types"
	"github.com/knadh/koanf"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/stuffbin"
	"github.com/phuslu/log"
)

func V3_0_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE gateway_details ADD COLUMN IF NOT EXISTS is_default boolean DEFAULT false;
	`); err != nil {
		return err
	}
	//
	if _, err := db.Exec(`
	CREATE UNIQUE INDEX IF NOT EXISTS gateway_default_index ON gateway_details USING btree (is_default,messenger) WHERE (is_default = true);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE gateway_details ADD COLUMN IF NOT EXISTS configuration JSONB DEFAULT '{}';
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	DO $$
	BEGIN
	  IF EXISTS (
		select 1 from pg_type where typname='listmonk_method_type'
	  ) THEN
		ALTER TYPE listmonk_method_type ADD VALUE IF NOT EXISTS 'smtp';
	  END IF;
	END $$;
	`); err != nil {
		return err
	}
	var out types.JSONText
	settings := []models.SMTP{}

	if err := db.Get(&out, `select value from settings where key = 'smtp'`); err != nil {
		panic(err)
	}

	if err := json.Unmarshal([]byte(out), &settings); err != nil {
		panic(err)
	}

	updated := false

	for _, set := range settings {
		b, err := json.Marshal(set)
		if err != nil {
			log.Error().Msgf("error occured %v", err)
			panic(err)
		}
		db.Exec("insert into gateway_details(method, messenger, name, configuration) values($1,$2,$3,$4)", "smtp", "email", set.Name, b)
		updated = true
	}

	if updated {
		db.Exec("delete FROM settings where key ='smtp'")
	}
	return nil
}
