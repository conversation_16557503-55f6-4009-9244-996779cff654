package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// Created new file to run migration again
func V4_0_9(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	if _, err := db.Exec(`
		DO $$
		DECLARE
    		constraint_name text;
		BEGIN
    		SELECT con.conname
    	INTO constraint_name
    	FROM pg_constraint con
    	JOIN pg_class rel ON rel.oid = con.conrelid
    	JOIN pg_attribute att ON att.attrelid = rel.oid AND att.attnum = ANY(con.conkey)
    	WHERE rel.relname = 'deeplinks'
    	  AND con.contype = 'u'
    	  AND att.attname = 'deeplinkname';
    	IF constraint_name IS NOT NULL THEN
    	    EXECUTE format('ALTER TABLE deeplinks DROP CONSTRAINT %I', constraint_name);
    	END IF;
		END$$;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        UPDATE campaigns c
		SET subject = LEFT(t.body, 140)
		FROM templates t
		WHERE c.template_id = t.id
		  AND c.messenger = 'sms'
		  AND (
		    c.subject = 'test \n' OR 
		    c.subject = 'test\n' OR 
		    c.subject = '\n' OR 
		    c.subject = ' \n' OR
			c.subject = 'test'
		  );
    `); err != nil {
		return err
	}
	return nil
}
