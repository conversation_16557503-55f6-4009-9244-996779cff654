package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_4(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	if _, err := db.Exec(`
        ALTER TABLE categories ADD COLUMN IF NOT EXISTS frequency_details jsonb;
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
        ALTER TABLE campaigns ADD COLUMN IF NOT EXISTS ignore_freq_cap boolean;
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS category_audits (
			id bigserial PRIMARY KEY,
			category_id integer NOT NULL,
			name text NOT NULL,
			created_at timestamp with time zone DEFAULT now(),
			updated_at timestamp with time zone DEFAULT now(),
			is_visible_to_merchant jsonb DEFAULT '{}'::jsonb,
			is_toggleable jsonb DEFAULT '{}'::jsonb,
			description character varying(255) DEFAULT ''::character varying,
			notification_defaults jsonb DEFAULT '{}'::jsonb,
			is_default boolean DEFAULT false NOT NULL,
			frequency_details jsonb DEFAULT '{}'::jsonb
		);
		CREATE INDEX IF NOT EXISTS idx_category_id ON category_audits (category_id);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS notification_frequencies (
			notification VARCHAR(16) PRIMARY KEY NOT NULL,
			display_name VARCHAR(32) NOT NULL,
			messenger VARCHAR(16) NOT NULL,
			enable_freq_cap boolean DEFAULT false NOT NULL,
			frequency_days integer,
			frequency_value integer,
			created_at timestamp with time zone DEFAULT now(),
			updated_at timestamp with time zone DEFAULT now(),
			created_by VARCHAR(36) NOT NULL,
			updated_by VARCHAR(36)
		);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS notification_frequency_audits (
			id bigserial PRIMARY KEY,
			notification VARCHAR(16) NOT NULL,
			display_name VARCHAR(32) NOT NULL,
			messenger VARCHAR(16) NOT NULL,
			enable_freq_cap boolean DEFAULT false NOT NULL,
			frequency_days integer,
			frequency_value integer,
			created_at timestamp with time zone DEFAULT now(),
			updated_at timestamp with time zone DEFAULT now(),
			created_by VARCHAR(36) NOT NULL,
			updated_by VARCHAR(36)
		);
		CREATE INDEX IF NOT EXISTS idx_notification ON notification_frequency_audits (notification);
	`); err != nil {
		return err
	}

	return nil
}
