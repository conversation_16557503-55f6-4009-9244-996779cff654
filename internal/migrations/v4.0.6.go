package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_6(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
		DO $$
		BEGIN
  		IF EXISTS (
    		SELECT 1
    		FROM information_schema.tables
    		WHERE table_name = 'channel_frequencies'
      		AND table_schema = 'public'  -- adjust if your schema is different
  		) THEN
    	EXECUTE 'ALTER TABLE channel_frequencies RENAME TO notification_frequencies';
  		END IF;
		END
		$$;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		DO $$
		BEGIN
  		IF EXISTS (
    		SELECT 1
    		FROM information_schema.tables
    		WHERE table_name = 'channel_frequency_audits'
      		AND table_schema = 'public'  -- adjust if your schema is different
  		) THEN
    	EXECUTE 'ALTER TABLE channel_frequency_audits RENAME TO notification_frequency_audits';
  		END IF;
		END
		$$;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		DO $$
		BEGIN
  		-- Step 1: Check if 'channel' column exists
  		IF EXISTS (
    		SELECT 1
    		FROM information_schema.columns
    		WHERE table_name = 'notification_frequencies'
      		AND column_name = 'channel'
      		AND table_schema = 'public'  -- adjust if using a different schema
  		) THEN

    	-- Step 2: Rename 'channel' to 'notification'
    	EXECUTE 'ALTER TABLE notification_frequencies RENAME COLUMN channel TO notification';
  		END IF;

  		-- Step 3: Increase column length to varchar(32) if 'notification' exists
  		IF EXISTS (
    		SELECT 1
    		FROM information_schema.columns
    		WHERE table_name = 'notification_frequencies'
      		AND column_name = 'notification'
      		AND table_schema = 'public'
  		) THEN
    	EXECUTE 'ALTER TABLE notification_frequencies ALTER COLUMN notification TYPE VARCHAR(32)';
  		END IF;
		END
		$$;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		DO $$
		BEGIN
  		-- Step 1: Check if 'channel' column exists
  		IF EXISTS (
    		SELECT 1
    		FROM information_schema.columns
    		WHERE table_name = 'notification_frequency_audits'
      		AND column_name = 'channel'
      		AND table_schema = 'public'  -- adjust if using a different schema
  		) THEN

    	-- Step 2: Rename 'channel' to 'notification'
    	EXECUTE 'ALTER TABLE notification_frequency_audits RENAME COLUMN channel TO notification';
  		END IF;

  		-- Step 3: Increase column length to varchar(32) if 'notification' exists
  		IF EXISTS (
    		SELECT 1
    		FROM information_schema.columns
    		WHERE table_name = 'notification_frequency_audits'
      		AND column_name = 'notification'
      		AND table_schema = 'public'
  		) THEN
    	EXECUTE 'ALTER TABLE notification_frequency_audits ALTER COLUMN notification TYPE VARCHAR(32)';
  		END IF;
		END
		$$;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		ALTER TABLE notification_frequencies ALTER COLUMN notification TYPE VARCHAR(32);
		ALTER TABLE notification_frequency_audits ALTER COLUMN notification TYPE VARCHAR(32);
		
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		ALTER TABLE notification_frequencies ADD COLUMN IF NOT EXISTS "is_notification_enabled" boolean DEFAULT true NOT NULL;
		ALTER TABLE notification_frequency_audits ADD COLUMN IF NOT EXISTS "is_notification_enabled" boolean DEFAULT true NOT NULL;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION category_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO category_audits (category_id, name, created_at, updated_at, is_visible_to_merchant, is_toggleable, description, notification_defaults, is_default, frequency_details)
			VALUES (OLD.id, OLD.name, OLD.created_at, OLD.updated_at, OLD.is_visible_to_merchant, OLD.is_toggleable, OLD.description, OLD.notification_defaults, OLD.is_default, OLD.frequency_details);
			IF TG_OP = 'DELETE' THEN
    			RETURN OLD;
  			ELSE
    			RETURN NEW;
  			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_categories_update ON categories;

		CREATE TRIGGER before_categories_update
		BEFORE UPDATE OR DELETE ON categories
		FOR EACH ROW
		EXECUTE FUNCTION category_audit_insert();
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION notification_frequency_audits_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO notification_frequency_audits (notification, display_name, messenger, enable_freq_cap, frequency_days, frequency_value, is_notification_enabled, created_at, updated_at, created_by, updated_by)
			VALUES (OLD.notification, OLD.display_name, OLD.messenger, OLD.enable_freq_cap, OLD.frequency_days, OLD.frequency_value, OLD.is_notification_enabled, OLD.created_at, OLD.updated_at, OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
    			RETURN OLD;
  			ELSE
    			RETURN NEW;
  			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_notification_frequencies_update ON notification_frequencies;

		CREATE TRIGGER before_notification_frequencies_update
		BEFORE UPDATE OR DELETE ON notification_frequencies
		FOR EACH ROW
		EXECUTE FUNCTION notification_frequency_audits_insert();
	`); err != nil {
		return err
	}

	return nil
}
