package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// Created new file to run migration again
func V3_0_8(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
		DO $$
		BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'request_type_enum') THEN
				CREATE TYPE request_type_enum AS ENUM ('campaign', 'process', 'real_time_message');
			END IF;

			IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'member_type_enum') THEN
				CREATE TYPE member_type_enum AS ENUM ('terminal', 'merchant');
			END IF;

			IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'status_enum') THEN
				CREATE TYPE status_enum AS ENUM ('success', 'failure', 'rejected');
			END IF;

		END
		$$;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS messenger_logs (
		    uuid uuid NOT NULL,
    		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    		request_type request_type_enum NOT NULL,
			messenger_type VARCHAR(16),
			member_name VARCHAR(1024) NOT NULL,
			member_type member_type_enum NOT NULL,
			target VARCHAR(1024) NOT NULL,
    		status status_enum NOT NULL,
    		remarks TEXT,
    		response JSONB,
    		reference_id VARCHAR(24) NOT NULL,
    		additional_details JSONB,
    		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    		created_by VARCHAR(36) NOT NULL,
    		updated_by VARCHAR(36)
		);
		CREATE INDEX IF NOT EXISTS idx_reference_id ON messenger_logs (reference_id);
	`); err != nil {
		return err
	}

	return nil
}
