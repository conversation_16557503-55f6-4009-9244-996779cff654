package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_9_1(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	DO $$
	BEGIN
	  IF NOT EXISTS (
		SELECT 1 FROM pg_enum WHERE enumlabel = 'failed'
	  ) THEN
		ALTER TYPE campaign_status ADD VALUE 'failed';
	  END IF;
	END $$;
	`); err != nil {
		return err
	}
	return nil
}
