package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_1(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Add new columns `de_duplication` and `duplication_level` in `templates` table
	if _, err := db.Exec(`
        CREATE INDEX IF NOT EXISTS idx_member_name ON messenger_logs (member_name);
    `); err != nil {
		return err
	}

	return nil
}
