package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// V3_0_3 performs the DB migrations for v3.0.3.
func V3_0_4(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Begin a transaction
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Create the cta_deep_links table
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS subscriber_attrib_keys (
			id SERIAL PRIMARY KEY,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			name VARCHAR(255) UNIQUE NOT NULL,
			created_by VARCHAR(255) DEFAULT '',
			updated_by VARCHAR(255) DEFAULT ''
		);
	`)
	if err != nil {
		return err
	}

	// Insert the default values
	_, err = tx.Exec(`
		INSERT INTO subscriber_attrib_keys (name, created_by, updated_by)
		select DISTINCT jsonb_object_keys(attribs), 'migrations','migrations' AS name FROM subscribers where jsonb_typeof(attribs) = 'object'
        ON CONFLICT (name) DO NOTHING;
	`)
	if err != nil {
		return err
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return err
	}

	return nil
}
