package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// V2_2_0 performs the DB migrations for v.2.2.0.
func V2_4_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	CREATE TYPE listmonk_method_type AS ENUM ('get', 'post', 'put');
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`CREATE TABLE IF NOT EXISTS gateway_details (
		id               SERIAL PRIMARY KEY,
		method listmonk_method_type NOT NULL DEFAULT 'post',
		request_params TEXT,
		headers TEXT,
		url TEXT,
		request_body TEXT,
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	CREATE TABLE IF NOT EXISTS template_details (
		id               SERIAL PRIMARY KEY,
		template_id    INTEGER NOT NULL REFERENCES templates(id) ON DELETE CASCADE ON UPDATE CASCADE,
		registered_template_id             varchar(100) NOT NULL,
		gateway varchar(20),
		type varchar(10),
		gateway_details_id INTEGER NOT NULL REFERENCES templates(id) ON DELETE CASCADE ON UPDATE CASCADE,
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);
	`); err != nil {
		return err
	}

	return nil
}
