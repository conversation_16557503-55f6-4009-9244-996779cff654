package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_2_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS webhook_config_audits (
			id bigserial PRIMARY KEY,
			webhook_config_id UUID NOT NULL,
    		provider VARCHAR(50) NOT NULL,
    		messenger VARCHAR(50) NOT NULL,
    		payload_type TEXT NOT NULL,
    		auth_type TEXT NOT NULL,
    		auth_secret_path TEXT,
    		payload_template TEXT NOT NULL,
			transformer TEXT NOT NULL,
    		status_mapper JSONB NOT NULL DEFAULT '{}'::JSONB,
    		created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    		updated_at TIMESTAMP WITHOUT TIME ZONE,
    		created_by VARC<PERSON>R(255),
    		updated_by VARCHAR(255)
		);`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION webhook_config_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO webhook_config_audits(webhook_config_id, provider, messenger, payload_type, auth_type, auth_secret_path, payload_template, transformer, 
					status_mapper, created_at, updated_at, created_by, updated_by)
			VALUES (OLD.id, OLD.provider, OLD.messenger, OLD.payload_type, OLD.auth_type, OLD.auth_secret_path, OLD.payload_template, OLD.transformer, 
					OLD.status_mapper, OLD.created_at, OLD.updated_at, OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
				RETURN OLD;
			ELSE
				RETURN NEW;
			END IF;
		END;
		$$ LANGUAGE plpgsql;
		DROP TRIGGER IF EXISTS before_web_config_update ON webhook_config;
		CREATE TRIGGER before_web_config_update
		BEFORE UPDATE OR DELETE ON webhook_config
		FOR EACH ROW
		EXECUTE FUNCTION webhook_config_audit_insert();
	`); err != nil {
		return err
	}
	return nil
}
