package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_2(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	WITH defaultCat AS (
		SELECT id
		FROM categories
		WHERE is_default = true
	  )
	  UPDATE campaigns c SET category = dc.id
	  FROM defaultCat dc
	  WHERE c.category IS NULL OR c.category = 0;
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	WITH defaultCat AS (
		SELECT id
		FROM categories
		WHERE is_default = true
	  )
	  UPDATE templates t SET category = dc.id
	  FROM defaultCat dc
	  WHERE t.category IS NULL OR t.category = 0;
	`); err != nil {
		return err
	}

	return nil
}
