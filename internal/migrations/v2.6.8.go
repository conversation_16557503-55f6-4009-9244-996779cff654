package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_8(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE campaigns ADD COLUMN IF NOT EXISTS "report_request_id" varchar(40)
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE campaigns ADD COLUMN IF NOT EXISTS "report_name" text
	`); err != nil {
		return err
	}
	return nil
}
