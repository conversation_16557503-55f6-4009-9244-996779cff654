package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V3_0_1(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	// Step 1: Add new column `delivered` to `campaigns` table
	if _, err := db.Exec(`
        ALTER TABLE campaigns ADD COLUMN IF NOT EXISTS delivered integer DEFAULT 0;
    `); err != nil {
		return err
	}

	// Step 2: Create index on campaign_id in campaign_targets table
	if _, err := db.Exec(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaign_targets_campaign_id ON campaign_targets (campaign_id);
    `); err != nil {
		return err
	}

	// Step 3: Update delivered column using CTE query
	_, err := db.Exec(`
        WITH campaign_counts AS (
            SELECT ct.campaign_id, COUNT(*) AS target_count
            FROM campaign_targets ct
            GROUP BY ct.campaign_id
        )
        UPDATE campaigns c
        SET delivered = cc.target_count
        FROM campaign_counts cc
        WHERE c.id = cc.campaign_id;
    `)
	if err != nil {
		return err
	}

	return nil
}
