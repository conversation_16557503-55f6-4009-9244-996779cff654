package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_5(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION category_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO category_audits (category_id, name, created_at, updated_at, is_visible_to_merchant, is_toggleable, description, notification_defaults, is_default, frequency_details)
			VALUES (OLD.id, OLD.name, OLD.created_at, OLD.updated_at, OLD.is_visible_to_merchant, OLD.is_toggleable, OLD.description, OLD.notification_defaults, OLD.is_default, OLD.frequency_details);
			RETURN NEW;
		END;
		$$ LANGUAGE plpgsql;

		CREATE TRIGGER before_categories_update
		BEFORE UPDATE ON categories
		FOR EACH ROW
		EXECUTE FUNCTION category_audit_insert();
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION notification_frequency_audits_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO notification_frequency_audits (notification, display_name, messenger, enable_freq_cap, frequency_days, frequency_value, created_at, updated_at, created_by, updated_by)
			VALUES (OLD.notification, OLD.display_name, OLD.messenger, OLD.enable_freq_cap, OLD.frequency_days, OLD.frequency_value, OLD.created_at, OLD.updated_at, OLD.created_by, OLD.updated_by);
			RETURN NEW;
		END;
		$$ LANGUAGE plpgsql;

		CREATE TRIGGER before_notification_frequencies_update
		BEFORE UPDATE ON notification_frequencies
		FOR EACH ROW
		EXECUTE FUNCTION notification_frequency_audits_insert();
	`); err != nil {
		return err
	}

	return nil
}
