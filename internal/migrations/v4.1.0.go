package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

// Created new file to run migration again
func V4_1_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`CREATE TABLE IF NOT EXISTS webhook_config (
	id UUID PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    messenger VARCHAR(50) NOT NULL,
    payload_type TEXT NOT NULL,
    auth_type TEXT NOT NULL,
    auth_secret_path TEXT,
    payload_template TEXT NOT NULL,
	transformer TEXT NOT NULL,
    status_mapper JSONB NOT NULL DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255))`); err != nil {
		return err
	}

	if _, err := db.Exec(`CREATE TABLE IF NOT EXISTS webhook_logs (
		id UUID PRIMARY KEY,
		message_id VARCHAR(255) NOT NULL,
    	provider VARCHAR(50) NOT NULL,
    	provider_status VARCHAR(20),
    	delivery_status VARCHAR(20),
    	messenger VARCHAR(50) NOT NULL,
    	event_time TIMESTAMP WITHOUT TIME ZONE,
    	provider_failure_reason TEXT,
		request_id VARCHAR(255),
		created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW())`); err != nil {
		return err
	}

	if _, err := db.Exec(`ALTER TABLE messenger_logs ADD COLUMN IF NOT EXISTS template_id INTEGER DEFAULT 0;`); err != nil {
		return err
	}
	return nil
}
