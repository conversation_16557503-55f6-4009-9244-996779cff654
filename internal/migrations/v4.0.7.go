package migrations

import (
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_7(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	tables := []string{"campaigns", "templates", "gateway_details", "categories", "category_audits"}
	for _, table := range tables {
		if _, err := db.Exec(fmt.Sprintf(`
            ALTER TABLE %s ADD COLUMN IF NOT EXISTS created_by VARCHAR(64);
            ALTER TABLE %s ADD COLUMN IF NOT EXISTS updated_by VARCHAR(64);
        `, table, table)); err != nil {
			return err
		}
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS campaigns_audits (
			id bigserial PRIMARY KEY,
			campaign_id integer NOT NULL,
    		uuid uuid NOT NULL,
    		name text NOT NULL,
    		subject text NOT NULL,
    		from_email text NOT NULL,
    		body text NOT NULL,
    		altbody text,
    		content_type content_type DEFAULT 'richtext'::content_type NOT NULL,
    		send_at timestamp with time zone,
    		headers jsonb DEFAULT '[]'::jsonb NOT NULL,
    		status campaign_status DEFAULT 'draft'::campaign_status NOT NULL,
    		tags character varying(100)[],
    		type campaign_type DEFAULT 'regular'::campaign_type,
    		messenger text NOT NULL,
    		template_id integer DEFAULT 1,
    		to_send integer DEFAULT 0 NOT NULL,
    		sent integer DEFAULT 0 NOT NULL,
    		max_subscriber_id bigint DEFAULT 0 NOT NULL,
    		last_subscriber_id bigint DEFAULT 0 NOT NULL,
    		archive boolean DEFAULT false NOT NULL,
    		archive_template_id integer DEFAULT 1,
    		archive_meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    		started_at timestamp with time zone,
    		created_at timestamp with time zone DEFAULT now(),
    		updated_at timestamp with time zone DEFAULT now(),
    		cron text,
    		fcm_image text DEFAULT ''::text,
    		fcm_keyval jsonb DEFAULT '{}'::jsonb,
    		fcm_roles text DEFAULT ''::text,
    		category integer DEFAULT 0,
    		fcm_cta text DEFAULT ''::text,
    		de_duplication boolean DEFAULT false,
    		duplication_level text,
    		process_status text DEFAULT ''::text,
    		end_at timestamp with time zone,
    		file_path text,
    		paused boolean DEFAULT false,
			report_request_id character varying(40),
			report_name text,
			parent_campaign_uuid character varying(40),
			conversion_event_name text, 
			conversion_event_time integer,
			delivered integer DEFAULT 0,
			campaign_type character varying(36),
			name_tsv tsvector,
			ignore_freq_cap boolean,
			created_by VARCHAR(64),
			updated_by VARCHAR(64)
		);
		CREATE INDEX IF NOT EXISTS idx_campaigns_audits_campaign_id ON campaigns_audits (campaign_id);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS templates_audits (
			id bigserial PRIMARY KEY,
			template_id integer NOT NULL,
    		name text NOT NULL,
    		type template_type DEFAULT 'campaign'::template_type NOT NULL,
    		subject text NOT NULL,
    		body text NOT NULL,
    		is_default boolean DEFAULT false NOT NULL,
    		created_at timestamp with time zone DEFAULT now(),
    		updated_at timestamp with time zone DEFAULT now(),
    		category integer DEFAULT 0,
    		additional_values jsonb DEFAULT '{}'::jsonb,
    		message_type character varying(20) DEFAULT 'notification'::character varying,
    		template_params jsonb DEFAULT '{}'::jsonb NOT NULL,
			de_duplication boolean DEFAULT false,
			duplication_level character varying(24),
			segment_config jsonb NOT NULL DEFAULT '{}'::jsonb,
			process_duration integer DEFAULT 0,
			created_by VARCHAR(64),
			updated_by VARCHAR(64)
		);
		CREATE INDEX IF NOT EXISTS idx_templates_audits_template_id ON templates_audits (template_id);
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS gateway_details_audits (
			id bigserial PRIMARY KEY,
			gateway_id integer NOT NULL,
    		method listmonk_method_type DEFAULT 'post'::listmonk_method_type NOT NULL,
    		request_params text,
    		headers text,
    		url text,
    		request_body text,
    		updated_at timestamp with time zone DEFAULT now(),
    		created_at timestamp with time zone DEFAULT now(),
    		messenger character varying(20),
    		name character varying(20),
			is_default boolean DEFAULT false,
    		configuration jsonb DEFAULT '{}'::jsonb,
			created_by VARCHAR(64),
			updated_by VARCHAR(64)
		);
		CREATE INDEX IF NOT EXISTS idx_gateway_details_audits_gateway_id ON gateway_details_audits (gateway_id);
	`); err != nil {
		return err
	}

	//Triggers
	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION category_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO category_audits (category_id, name, created_at, updated_at, is_visible_to_merchant, is_toggleable, description, notification_defaults, is_default, frequency_details, created_by, updated_by)
			VALUES (OLD.id, OLD.name, OLD.created_at, OLD.updated_at, OLD.is_visible_to_merchant, OLD.is_toggleable, OLD.description, OLD.notification_defaults, OLD.is_default, OLD.frequency_details, OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
    			RETURN OLD;
  			ELSE
    			RETURN NEW;
  			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_categories_update ON categories;

		CREATE TRIGGER before_categories_update
		BEFORE UPDATE OR DELETE ON categories
		FOR EACH ROW
		EXECUTE FUNCTION category_audit_insert();
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION campaigns_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO campaigns_audits(campaign_id, uuid, name, subject, from_email, body, altbody, content_type, send_at, headers, 
					status, tags, type, messenger, template_id, to_send, sent, max_subscriber_id, last_subscriber_id, archive, archive_template_id, 
					archive_meta, started_at, created_at, updated_at, cron, fcm_image, fcm_keyval, fcm_roles, category, fcm_cta, de_duplication, 
					duplication_level, process_status, end_at, file_path, paused, report_request_id, report_name, parent_campaign_uuid, 
					conversion_event_name, conversion_event_time, delivered, campaign_type, name_tsv, ignore_freq_cap, created_by, updated_by)
			VALUES (OLD.id, OLD.uuid, OLD.name, OLD.subject, OLD.from_email, OLD.body, OLD.altbody, OLD.content_type, OLD.send_at, 
					OLD.headers, OLD.status, OLD.tags, OLD.type, OLD.messenger, OLD.template_id, OLD.to_send, OLD.sent, OLD.max_subscriber_id, 
					OLD.last_subscriber_id, OLD.archive, OLD.archive_template_id, OLD.archive_meta, OLD.started_at, OLD.created_at, OLD.updated_at, 
					OLD.cron, OLD.fcm_image, OLD.fcm_keyval, OLD.fcm_roles, OLD.category, OLD.fcm_cta, OLD.de_duplication, OLD.duplication_level, 
					OLD.process_status, OLD.end_at, OLD.file_path, OLD.paused, OLD.report_request_id, OLD.report_name, OLD.parent_campaign_uuid, 
					OLD.conversion_event_name, OLD.conversion_event_time, OLD.delivered, OLD.campaign_type, OLD.name_tsv, OLD.ignore_freq_cap,
					OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
				RETURN OLD;
			ELSE
				RETURN NEW;
			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_campaigns_update ON campaigns;

		CREATE TRIGGER before_campaigns_update
		BEFORE UPDATE OR DELETE ON campaigns
		FOR EACH ROW
		EXECUTE FUNCTION campaigns_audit_insert();
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION templates_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO templates_audits(template_id, name, type, subject, body, is_default, created_at, updated_at, category, 
					additional_values, message_type, template_params, de_duplication, duplication_level, segment_config, process_duration, 
					created_by, updated_by)
			VALUES (OLD.id, OLD.name, OLD.type, OLD.subject, OLD.body, OLD.is_default, OLD.created_at, OLD.updated_at, OLD.category, 
					OLD.additional_values, OLD.message_type, OLD.template_params, OLD.de_duplication, OLD.duplication_level, OLD.segment_config, 
					OLD.process_duration, OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
				RETURN OLD;
			ELSE
				RETURN NEW;
			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_templates_update ON templates;

		CREATE TRIGGER before_templates_update
		BEFORE UPDATE OR DELETE ON templates
		FOR EACH ROW
		EXECUTE FUNCTION templates_audit_insert();
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION gateway_details_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO gateway_details_audits(gateway_id, method, request_params, headers, url, request_body, updated_at, created_at, 
					messenger, name, is_default, configuration, created_by, updated_by)
			VALUES (OLD.id, OLD.method, OLD.request_params, OLD.headers, OLD.url, OLD.request_body, OLD.updated_at, OLD.created_at, 
					OLD.messenger, OLD.name, OLD.is_default, OLD.configuration, OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
				RETURN OLD;
			ELSE
				RETURN NEW;
			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_gateway_details_update ON gateway_details;

		CREATE TRIGGER before_gateway_details_update
		BEFORE UPDATE OR DELETE ON gateway_details
		FOR EACH ROW
		EXECUTE FUNCTION gateway_details_audit_insert();
	`); err != nil {
		return err
	}

	return nil
}
