package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_0_8(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {
	if _, err := db.Exec(`
        ALTER TABLE campaigns DROP COLUMN IF EXISTS name_tsv;
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
        ALTER TABLE campaigns_audits DROP COLUMN IF EXISTS name_tsv;
    `); err != nil {
		return err
	}

	if _, err := db.Exec(`
		CREATE OR REPLACE FUNCTION campaigns_audit_insert()
		RETURNS TRIGGER AS $$
		BEGIN
			INSERT INTO campaigns_audits(campaign_id, uuid, name, subject, from_email, body, altbody, content_type, send_at, headers, 
					status, tags, type, messenger, template_id, to_send, sent, max_subscriber_id, last_subscriber_id, archive, archive_template_id, 
					archive_meta, started_at, created_at, updated_at, cron, fcm_image, fcm_keyval, fcm_roles, category, fcm_cta, de_duplication, 
					duplication_level, process_status, end_at, file_path, paused, report_request_id, report_name, parent_campaign_uuid, 
					conversion_event_name, conversion_event_time, delivered, campaign_type, ignore_freq_cap, created_by, updated_by)
			VALUES (OLD.id, OLD.uuid, OLD.name, OLD.subject, OLD.from_email, OLD.body, OLD.altbody, OLD.content_type, OLD.send_at, 
					OLD.headers, OLD.status, OLD.tags, OLD.type, OLD.messenger, OLD.template_id, OLD.to_send, OLD.sent, OLD.max_subscriber_id, 
					OLD.last_subscriber_id, OLD.archive, OLD.archive_template_id, OLD.archive_meta, OLD.started_at, OLD.created_at, OLD.updated_at, 
					OLD.cron, OLD.fcm_image, OLD.fcm_keyval, OLD.fcm_roles, OLD.category, OLD.fcm_cta, OLD.de_duplication, OLD.duplication_level, 
					OLD.process_status, OLD.end_at, OLD.file_path, OLD.paused, OLD.report_request_id, OLD.report_name, OLD.parent_campaign_uuid, 
					OLD.conversion_event_name, OLD.conversion_event_time, OLD.delivered, OLD.campaign_type, OLD.ignore_freq_cap,
					OLD.created_by, OLD.updated_by);
			IF TG_OP = 'DELETE' THEN
				RETURN OLD;
			ELSE
				RETURN NEW;
			END IF;
		END;
		$$ LANGUAGE plpgsql;

		DROP TRIGGER IF EXISTS before_campaigns_update ON campaigns;

		CREATE TRIGGER before_campaigns_update
		BEFORE UPDATE OR DELETE ON campaigns
		FOR EACH ROW
		EXECUTE FUNCTION campaigns_audit_insert();
	`); err != nil {
		return err
	}

	return nil
}
