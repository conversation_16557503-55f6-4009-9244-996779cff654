package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V2_6_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
	ALTER TABLE categories ADD COLUMN IF NOT EXISTS "is_visible_to_merchant" jsonb default '{}'
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE categories ADD COLUMN IF NOT EXISTS "is_toggleable" jsonb default '{}'
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE categories ADD COLUMN IF NOT EXISTS "description" varchar(255) default ''
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE categories ADD COLUMN IF NOT EXISTS "notification_defaults" jsonb default '{}'
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	ALTER TABLE categories ADD COLUMN IF NOT EXISTS "is_default" boolean not null default false
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	CREATE UNIQUE INDEX IF NOT EXISTS category_default_index ON categories (is_default) WHERE is_default = true
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	INSERT INTO categories (name, is_toggleable, is_visible_to_merchant, notification_defaults, description, is_default) SELECT 'Default Category', '{"fcm": true, "sms": true, "email": true, "pax": true}', '{"fcm": true, "sms": true, "email": true, "pax": true}', '{"fcm": true, "sms": true, "email": true, "pax": true}',
	'This is default category', true WHERE NOT EXISTS (
    SELECT 1
    FROM categories
    WHERE is_default = true)
	`); err != nil {
		return err
	}

	if _, err := db.Exec(`
	UPDATE categories set notification_defaults='{"fcm": true, "sms": true, "email": true}', is_toggleable = '{"fcm": true, "sms": true, "email": true}', 
	is_visible_to_merchant='{"fcm": true, "sms": true, "email": true}' where notification_defaults='{}' and is_toggleable='{}' and is_visible_to_merchant='{}' 
	`); err != nil {
		return err
	}

	return nil
}
