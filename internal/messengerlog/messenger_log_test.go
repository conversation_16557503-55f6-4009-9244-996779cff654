package messengerlog

// func TestInsertMessengerLogs(t *testing.T) {
// 	tests := []struct {
// 		name          string
// 		logs          []models.MessengerLog
// 		mockSetup     func(sqlmock.Sqlmock)
// 		expectedRows  int64
// 		expectedError error
// 	}{
// 		{
// 			name:          "Empty Logs Slice",
// 			logs:          []models.MessengerLog{},
// 			mockSetup:     func(mock sqlmock.Sqlmock) {},
// 			expectedRows:  0,
// 			expectedError: errors.New("no logs to insert"),
// 		},
// 		{
// 			name: "Transaction Begin Failure",
// 			logs: []models.MessengerLog{
// 				{MessengerType: "email"},
// 			},
// 			mockSetup: func(mock sqlmock.Sqlmock) {
// 				mock.ExpectBegin().WillReturnError(errors.New("begin transaction error"))
// 			},
// 			expectedRows:  0,
// 			expectedError: errors.New("begin transaction error"),
// 		},
// 		{
// 			name: "Insert Failure",
// 			logs: []models.MessengerLog{
// 				{
// 					MessengerType:     "email",
// 					MemberName:        "TestUser",
// 					MemberType:        "merchant",
// 					Target:            "<EMAIL>",
// 					Status:            "success",
// 					Remarks:           "Test Remark",
// 					Response:          models.JSON(map[string]interface{}{}),
// 					ReferenceID:       "12345",
// 					AdditionalDetails: models.JSON(map[string]interface{}{}),
// 					CreatedBy:         "tester",
// 					UpdatedBy:         "",
// 					BaseV2: models.BaseV2{
// 						UpdatedAt: null.TimeFrom(time.Now()),
// 					},
// 				},
// 			},
// 			mockSetup: func(mock sqlmock.Sqlmock) {
// 				mock.ExpectBegin()
// 				mock.ExpectExec("INSERT INTO messenger_logs").WithArgs(
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 					sqlmock.AnyArg(),
// 				).WillReturnError(errors.New("insert error"))
// 				mock.ExpectRollback()
// 			},
// 			expectedRows:  0,
// 			expectedError: errors.New("insert error"),
// 		},
// 		{
// 			name: "Commit Failure",
// 			logs: []models.MessengerLog{
// 				{
// 					MessengerType:     "email",
// 					MemberName:        "TestUser",
// 					MemberType:        "merchant",
// 					Target:            "<EMAIL>",
// 					Status:            "success",
// 					Remarks:           "Test Remark",
// 					Response:          models.JSON(map[string]interface{}{}),
// 					ReferenceID:       "12345",
// 					AdditionalDetails: models.JSON(map[string]interface{}{}),
// 					CreatedBy:         "tester",
// 					UpdatedBy:         "tester",
// 					BaseV2: models.BaseV2{
// 						UpdatedAt: null.TimeFrom(time.Now()),
// 					},
// 				},
// 			},
// 			mockSetup: func(mock sqlmock.Sqlmock) {
// 				mock.ExpectBegin()
// 				mock.ExpectExec("INSERT INTO messenger_logs").
// 					WithArgs(
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 					).
// 					WillReturnResult(sqlmock.NewResult(1, 1))
// 				mock.ExpectCommit().WillReturnError(errors.New("commit error"))
// 			},
// 			expectedRows:  0,
// 			expectedError: errors.New("commit error"),
// 		},
// 		{
// 			name: "Successful Insert",
// 			logs: []models.MessengerLog{
// 				{
// 					MessengerType:     "email",
// 					MemberName:        "TestUser",
// 					MemberType:        "merchant",
// 					Target:            "<EMAIL>",
// 					Status:            "success",
// 					Remarks:           "Test Remark",
// 					Response:          models.JSON(map[string]interface{}{}),
// 					ReferenceID:       "12345",
// 					AdditionalDetails: models.JSON(map[string]interface{}{}),
// 					CreatedBy:         "tester",
// 					UpdatedBy:         "tester",
// 					BaseV2: models.BaseV2{
// 						UpdatedAt: null.TimeFrom(time.Now()),
// 					},
// 				},
// 			},
// 			mockSetup: func(mock sqlmock.Sqlmock) {
// 				mock.ExpectBegin()
// 				mock.ExpectExec("INSERT INTO messenger_logs").
// 					WithArgs(
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 						sqlmock.AnyArg(),
// 					).
// 					WillReturnResult(sqlmock.NewResult(1, 1))
// 				mock.ExpectCommit()
// 			},
// 			expectedRows:  1,
// 			expectedError: nil,
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			db, mock, err := sqlmock.New()
// 			assert.NoError(t, err)
// 			defer db.Close()

// 			sqlxDB := sqlx.NewDb(db, "sqlmock")

// 			tt.mockSetup(mock)

// 			// rowsAffected, err := InsertMessengerLogs(sqlxDB, context.Background(), tt.logs)

// 			assert.Equal(t, tt.expectedRows, rowsAffected)
// 			if tt.expectedError != nil {
// 				assert.EqualError(t, err, tt.expectedError.Error())
// 			} else {
// 				assert.NoError(t, err)
// 			}

// 			if err := mock.ExpectationsWereMet(); err != nil {
// 				t.Errorf("unfulfilled expectations: %s", err)
// 			}
// 		})
// 	}

// }
