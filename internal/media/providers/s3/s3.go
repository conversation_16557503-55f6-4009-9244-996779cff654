package s3

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/internal/media"
	"github.com/knadh/listmonk/tracer"
	push "github.com/phuslu/log"
	"github.com/rhnvrm/simples3"
)

// Opt represents AWS S3 specific params
type Opt struct {
	URL             string        `koanf:"url"`
	PublicURL       string        `koanf:"public_url"`
	AccessKey       string        `koanf:"aws_access_key_id"`
	BucketSubFolder string        `koanf:"bucketSubFolder"`
	SecretKey       string        `koanf:"aws_secret_access_key"`
	Region          string        `koanf:"aws_default_region"`
	Bucket          string        `koanf:"bucket"`
	BucketPath      string        `koanf:"bucket_path"`
	BucketType      string        `koanf:"bucket_type"`
	Expiry          time.Duration `koanf:"expiry"`
	CdnUrl          string        `koanf:"cdnUrl"`
}

// Client implements `media.Store` for S3 provider
type Client struct {
	s3   *simples3.S3
	opts Opt
}

// NewS3Store initialises store for S3 provider. It takes in the AWS configuration
// and sets up the `simples3` client to interact with AWS APIs for all bucket operations.
func NewS3Store(opt Opt) (media.Store, error) {
	var cl *simples3.S3
	if opt.URL == "" {
		opt.URL = fmt.Sprintf("https://s3.%s.amazonaws.com", opt.Region)
	}
	opt.URL = strings.TrimRight(opt.URL, "/")

	if opt.AccessKey == "" && opt.SecretKey == "" {
		// fallback to IAM role if no access key/secret key is provided.
		cl, _ = simples3.NewUsingIAM(opt.Region)
	}

	if cl == nil {
		cl = simples3.New(opt.Region, opt.AccessKey, opt.SecretKey)
	}

	cl.SetEndpoint(opt.URL)

	return &Client{
		s3:   cl,
		opts: opt,
	}, nil
}

// Put takes in the filename, the content type and file object itself and uploads to S3.
func (c *Client) Put(name string, cType string, file io.ReadSeeker, ctx context.Context) (string, error) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	_, err := file.Seek(0, 0)
	if err != nil {
		logger.Error().Msgf("failed to reset file pointer: %v", err)
		return "", err
	}

	// Create a temporary file
	tmpFile, err := os.CreateTemp("", "upload-")
	if err != nil {
		logger.Error().Msgf("failed to create temp file: %v", err)
		return "", err
	}
	defer os.Remove(tmpFile.Name()) // Clean up after upload
	defer tmpFile.Close()

	// Copy file contents to the temporary file
	_, err = io.Copy(tmpFile, file)
	if err != nil {
		logger.Error().Msgf("failed to write file to temp storage: %v", err)
		return "", err
	}

	// Reopen temp file for reading
	tmpFile, err = os.Open(tmpFile.Name())
	if err != nil {
		logger.Error().Msgf("failed to reopen temp file: %v", err)
	}
	defer tmpFile.Close()

	uuid := uuid.NewString()
	name = fmt.Sprintf("%s/%s", uuid, name)

	if c.opts.BucketSubFolder != "" {
		name = fmt.Sprintf("%s/%s", c.opts.BucketSubFolder, name)
	}

	// Upload to S3
	err = external.UploadToS3Bucket(
		c.opts.Bucket,
		name,
		tmpFile,
		c.opts.AccessKey,
		c.opts.SecretKey,
		c.opts.Region,
		cType,
		ctx,
	)

	if err != nil {
		logger.Error().Msgf("upload failed: %v", err)
	}

	return name, nil

}

// Get accepts the filename of the object stored and retrieves from S3.
func (c *Client) Get(name string) string {
	if c.opts.BucketType == "private" {
		return fmt.Sprintf("%s/%s", strings.TrimSuffix(c.opts.CdnUrl, "/"), c.makeBucketPath(name))
	}
	// Generate a public S3 URL if it's a public bucket.
	return c.makeFileURL(name)

}

// Delete accepts the filename of the object and deletes from S3.
func (c *Client) Delete(name string, ctx context.Context) error {
	objectKey := c.makeBucketPath(name)
	err := external.DeleteFileFromS3(
		c.opts.Bucket,
		objectKey,
		c.opts.AccessKey,
		c.opts.SecretKey,
		c.opts.Region,
		ctx,
	)
	return err
}

// makeBucketPath returns the file path inside the bucket. The path should not
// start with a /.
func (c *Client) makeBucketPath(name string) string {
	// If the path is root (/), return the filename without the preceding slash.
	p := strings.TrimPrefix(strings.TrimSuffix(c.opts.BucketPath, "/"), "/")
	if p == "" {
		return name
	}

	// whatever/bucket/path/filename.jpg: No preceding slash.
	return p + "/" + name
}

func (c *Client) makeFileURL(name string) string {
	if c.opts.PublicURL != "" {
		return c.opts.PublicURL + "/" + c.makeBucketPath(name)
	}

	return c.opts.URL + "/" + c.opts.Bucket + "/" + c.makeBucketPath(name)
}
