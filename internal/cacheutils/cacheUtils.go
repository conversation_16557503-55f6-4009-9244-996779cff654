package cacheutils

import (
	"encoding/json"
	"fmt"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/utils"
)

func GetCacheFcmData(memberId string) []models.FcmCache {
	fcmObj := []models.FcmCache{}
	err, fcm := utils.GetRedisData(memberId + "_fcm_token")
	if err != nil {
		fmt.Println(err)
	}
	if fcm != "" {
		err := json.Unmarshal([]byte(fcm), &fcmObj)
		if err != nil {
			fmt.Println(err)
		}
	}
	return fcmObj
}

func GetCacheSegmentTerminalData(key string) models.SegmentMembers {
	obj := models.SegmentMembers{}
	err, data := utils.GetRedisData(key)
	if err != nil {
		fmt.Println(err)
	}
	if data != "" {
		err := json.Unmarshal([]byte(data), &obj)
		if err != nil {
			fmt.Println(err)
		}
	}
	return obj
}
