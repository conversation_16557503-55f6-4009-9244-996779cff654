package postback

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"net/textproto"
	"time"

	"github.com/knadh/listmonk/internal/messenger"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	jwriter "github.com/mailru/easyjson/jwriter"
	zipkinhttp "github.com/openzipkin/zipkin-go/middleware/http"
	"gopkg.in/volatiletech/null.v6"
)

// postback is the payload that's posted as JSON to the HTTP Postback server.
//
// need to run the command if any changes made here: easyjson -all postback.go
//
//easyjson:json
type postback struct {
	Subject           string            `json:"subject"`
	ContentType       string            `json:"content_type"`
	Body              string            `json:"body"`
	AdditionalValues  string            `json:"additional_values"`
	Recipients        []recipient       `json:"recipients"`
	Campaign          *campaign         `json:"campaign"`
	Attachments       []attachment      `json:"attachments"`
	Label             string            `json:"label"`
	DirectFcm         bool              `json:"direct_fcm"`
	MessageType       string            `json:"messageType"`
	RequestParams     string            `json:"request_params,omitempty"`
	Url               string            `json:"url,omitempty"`
	Headers           string            `json:"request_headers,omitempty"`
	RequestBody       string            `json:"request_body,omitempty"`
	Method            string            `json:"method,omitempty"`
	Roles             string            `json:"roles,omitempty"`
	Target            string            `json:"target,omitempty"`
	CacheDetails      map[string]string `json:"cacheDetails,omitempty"`
	Messenger         string            `json:"messenger"`
	Data              map[string]string `json:"data,omitempty"`
	Tokens            []string          `json:"tokens,omitempty"`
	UniqueTargetsList []string          `json:"unique_targets_list"`
	TemplateId        int               `json:"templateId"`
}

type campaign struct {
	Id                 int               `json:"id"`
	FromEmail          string            `json:"from_email"`
	UUID               string            `json:"uuid"`
	Name               string            `json:"name"`
	Headers            models.Headers    `json:"headers"`
	Tags               []string          `json:"tags"`
	Roles              null.String       `json:"roles"`
	FcmImage           null.String       `json:"fcmImage"`
	Cta                null.String       `json:"cta"`
	DeDuplication      bool              `json:"deDuplication"`
	DuplicationLevel   null.String       `json:"duplicationLevel"`
	Data               map[string]string `json:"data"`
	MessageType        string            `json:"messageType"`
	Label              string            `json:"label,omitempty"`
	CacheDetails       map[string]string `json:"cacheDetails,omitempty"`
	ParentCampaignId   null.String       `json:"parentCampaignId"`
	ParentCampaignName null.String       `json:"parentCampaignName"`
}

type recipient struct {
	UUID    string      `json:"uuid"`
	Email   string      `json:"email"`
	Name    string      `json:"name"`
	Attribs models.JSON `json:"attribs"`
	Status  string      `json:"status"`
	Type    string      `json:"type"`
}

type attachment struct {
	Name    string               `json:"name"`
	Header  textproto.MIMEHeader `json:"header"`
	Content []byte               `json:"content"`
}

// Options represents HTTP Postback server options.
type Options struct {
	Name     string        `json:"name"`
	Username string        `json:"username"`
	Password string        `json:"password"`
	RootURL  string        `json:"root_url"`
	MaxConns int           `json:"max_conns"`
	Retries  int           `json:"retries"`
	Timeout  time.Duration `json:"timeout"`
}

// Postback represents an HTTP Message server.
type Postback struct {
	authStr string
	o       Options
	c       *zipkinhttp.Client
}

// New returns a new instance of the HTTP Postback messenger.
func New(o Options, skipSSL bool) (*Postback, error) {
	authStr := ""
	if o.Username != "" && o.Password != "" {
		authStr = fmt.Sprintf("Basic %s", base64.StdEncoding.EncodeToString(
			[]byte(o.Username+":"+o.Password)))
	}

	client, err := tracer.GetPostbackHttpClient(skipSSL, o.MaxConns, o.Timeout)

	if err != nil {
		panic(err)
	}
	return &Postback{
		authStr: authStr,
		o:       o,
		c:       client,
	}, nil
}

// Name returns the messenger's name.
func (p *Postback) Name() string {
	return p.o.Name
}

// Push pushes a message to the server.
func (p *Postback) Push(m messenger.Message) (string, error) {
	pb := postback{
		Subject:           m.Subject,
		ContentType:       m.ContentType,
		Body:              string(m.Body),
		AdditionalValues:  m.AdditionalValues,
		DirectFcm:         m.DirectFcm,
		MessageType:       m.MessageType,
		RequestParams:     m.RequestParams,
		RequestBody:       m.RequestBody,
		Headers:           m.RequestHeaders,
		Method:            m.Method,
		Url:               m.Url,
		Roles:             m.Roles,
		Target:            m.Target,
		CacheDetails:      m.CacheDetails,
		Messenger:         m.Messenger,
		Data:              m.Data,
		Tokens:            m.Tokens,
		UniqueTargetsList: m.UniqueTargetsList,
		TemplateId:        m.TemplateId,

		Recipients: []recipient{{
			UUID:    m.Subscriber.UUID,
			Email:   m.Subscriber.Email,
			Name:    m.Subscriber.Name,
			Status:  m.Subscriber.Status,
			Attribs: m.Subscriber.Attribs,
			Type:    m.Subscriber.Type,
		}},
	}

	if m.Campaign != nil {
		pb.Campaign = &campaign{
			FromEmail:          m.Campaign.FromEmail,
			UUID:               m.Campaign.UUID,
			Name:               m.Campaign.Name,
			Headers:            m.Campaign.Headers,
			Tags:               m.Campaign.Tags,
			Roles:              m.Campaign.FCMRoles,
			FcmImage:           m.Campaign.FcmImage,
			Cta:                m.Campaign.FCMCta,
			Id:                 m.Campaign.ID,
			DuplicationLevel:   m.Campaign.DuplicationLevel,
			DeDuplication:      m.Campaign.DeDuplication,
			Data:               m.Data,
			MessageType:        m.Campaign.MessageType,
			Label:              m.Campaign.Label,
			ParentCampaignId:   m.Campaign.ParentCampaignId,
			ParentCampaignName: m.Campaign.ParentCampaignName,
		}
	} else {
		pb.Label = m.Label
	}

	if len(m.Attachments) > 0 {
		files := make([]attachment, 0, len(m.Attachments))
		for _, f := range m.Attachments {
			a := attachment{
				Name:    f.Name,
				Header:  f.Header,
				Content: make([]byte, len(f.Content)),
			}
			copy(a.Content, f.Content)
			files = append(files, a)
		}
	}

	// b, err := pb.MarshalEasyJSON()
	var bw jwriter.Writer
	bw.NoEscapeHTML = true
	pb.MarshalEasyJSON(&bw)
	// var buf bytes.Buffer
	// pb.MarshalEasyJSON(&buf)
	// encoder.SetEscapeHTML(false)

	// err := encoder.Encode(pb)
	// if err != nil {
	// 	log.Printf("Error encoding JSON: %v", err)
	// 	return err
	// }

	// log.Printf("Got request for Postback api: %v", string(bw.Buffer.BuildBytes()))
	responseBody, err := p.exec(http.MethodPost, p.o.RootURL, bw.Buffer.BuildBytes(), nil, m.Ctx, m.MsgId)
	if err != nil {
		return "", err
	}
	return responseBody, nil
}

// Flush flushes the message queue to the server.
func (p *Postback) Flush() error {
	return nil
}

// Close closes idle HTTP connections.
func (p *Postback) Close() error {
	p.c.CloseIdleConnections()
	return nil
}

func (p *Postback) exec(method, rURL string, reqBody []byte, headers http.Header, ctx context.Context, msgId string) (string, error) {
	var (
		err      error
		postBody io.Reader
	)

	// Encode POST / PUT params.
	if method == http.MethodPost || method == http.MethodPut {
		postBody = bytes.NewReader(reqBody)
	}

	req, err := http.NewRequest(method, rURL, postBody)
	if err != nil {
		return "", err
	}

	if headers != nil {
		req.Header = headers
	} else {
		req.Header = http.Header{}
	}
	req.Header.Set("User-Agent", "listmonk")

	// Optional BasicAuth.
	if p.authStr != "" {
		req.Header.Set("Authorization", p.authStr)
	}

	if msgId != "" {
		req.Header.Set(tracer.MSG_HEADER_KEY, msgId)
	}

	// If a content-type isn't set, set the default one.
	if req.Header.Get("Content-Type") == "" {
		if method == http.MethodPost || method == http.MethodPut {
			req.Header.Add("Content-Type", "application/json")
		}
	}

	// If the request method is GET or DELETE, add the params as QueryString.
	if method == http.MethodGet || method == http.MethodDelete {
		req.URL.RawQuery = string(reqBody)
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)

	r, err := p.c.Do(req)
	if err != nil {
		return "", err
	}
	defer func() {
		// Drain and close the body to let the Transport reuse the connection
		io.Copy(io.Discard, r.Body)
		r.Body.Close()
	}()

	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		return "", err
	}

	if r.StatusCode != http.StatusOK {
		return "", fmt.Errorf("non-OK response from Postback server: %d", r.StatusCode)
	}

	return string(bodyBytes), nil
}
