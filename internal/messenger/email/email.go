package email

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/smtp"
	"net/textproto"
	"net/url"
	"strings"

	"github.com/knadh/listmonk/internal/messenger"
	"github.com/knadh/listmonk/outbound"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/knadh/smtppool"
	push "github.com/phuslu/log"
)

const (
	emName        = "email"
	hdrReturnPath = "Return-Path"
)

var (
	skipSSLCheck           = false
	maxEmailAttachmentSize = int64(26214400)
)

// Server represents an SMTP server's credentials.
type Server struct {
	Username      string            `json:"username"`
	Password      string            `json:"password"`
	AuthProtocol  string            `json:"auth_protocol"`
	TLSType       string            `json:"tls_type"`
	TLSSkipVerify bool              `json:"tls_skip_verify"`
	EmailHeaders  map[string]string `json:"email_headers"`

	// Rest of the options are embedded directly from the smtppool lib.
	// The JSON tag is for config unmarshal to work.
	smtppool.Opt `json:",squash"`
	IsDefault    bool  `json:"default"`
	Id           int64 `json:"id"`

	pool *smtppool.Pool
}

// Emailer is the SMTP e-mail messenger.
type Emailer struct {
	// servers       []*Server
	smtpServerMap map[string]*Server
}

func SetSSLFlag(skipSSl bool) {
	skipSSLCheck = skipSSl
}

func SetMaxEmailAttachmentSize(maxFileSize int64) {
	maxEmailAttachmentSize = maxFileSize
}

// New returns an SMTP e-mail Messenger backend with the given SMTP servers.
func New(servers ...Server) (*Emailer, error) {
	e := &Emailer{
		smtpServerMap: make(map[string]*Server),
	}

	for _, srv := range servers {
		s := srv
		var auth smtp.Auth
		switch s.AuthProtocol {
		case "cram":
			auth = smtp.CRAMMD5Auth(s.Username, s.Password)
		case "plain":
			auth = smtp.PlainAuth("", s.Username, s.Password, s.Host)
		case "login":
			auth = &smtppool.LoginAuth{Username: s.Username, Password: s.Password}
		case "", "none":
		default:
			return nil, fmt.Errorf("unknown SMTP auth type '%s'", s.AuthProtocol)
		}
		s.Opt.Auth = auth

		// TLS config.
		if s.TLSType != "none" {
			s.TLSConfig = &tls.Config{}
			if s.TLSSkipVerify {
				s.TLSConfig.InsecureSkipVerify = s.TLSSkipVerify
			} else {
				s.TLSConfig.ServerName = s.Host
			}

			// SSL/TLS, not STARTTLS.
			if s.TLSType == "TLS" {
				s.Opt.SSL = true
			}
		}

		pool, err := smtppool.New(s.Opt)
		if err != nil {
			return nil, err
		}

		s.pool = pool
		// e.servers = append(e.servers, &s)
		if s.IsDefault {
			e.smtpServerMap["default"] = &s
		}
		e.smtpServerMap[fmt.Sprintf("%v", s.Id)] = &s
	}

	return e, nil
}

// Name returns the Server's name.
func (e *Emailer) Name() string {
	return emName
}

// Push pushes a message to the server.
func (e *Emailer) Push(m messenger.Message) (string, error) {
	// If there are more than one SMTP servers, send to a random
	// one from the list.
	if m.Method != "smtp" {
		return SendToConfiguredEmail(m)
	} else {
		return SendToDefaultEmailer(m, e)
	}
}

func SendToConfiguredEmail(m messenger.Message) (string, error) {

	logger := m.Ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	var req *http.Request
	method := strings.ToUpper(m.Method)
	targetUrl := m.Url
	if method == "GET" {
		if len(m.RequestParams) != 0 {

			params := make(map[string]interface{})

			err := json.Unmarshal([]byte(m.RequestParams), &params)

			if err != nil {
				logger.Error().Msgf("Error %v", err)
				return "", err
			}
			queryParams := url.Values{}
			for key, value := range params {
				queryParams.Add(key, fmt.Sprintf("%v", value))
			}
			targetUrl += `?` + queryParams.Encode()
		}

		req, _ = http.NewRequest(method, targetUrl, nil)
	} else {
		logger.Info().Msgf("sending request to %v", m.RequestBody)
		req, _ = http.NewRequest(method, targetUrl, bytes.NewBufferString(m.RequestBody))
	}

	if len(m.RequestHeaders) != 0 {

		header := make(map[string]string)

		err := json.Unmarshal([]byte(m.RequestHeaders), &header)

		if err != nil {
			return "", err
		}
		for key, value := range header {
			req.Header.Add(key, fmt.Sprintf("%v", value))
		}
	}

	req.Header.Set("Content-Type", "application/json")

	client := outbound.GetOutboundClient(skipSSLCheck)

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error().Msgf("Error reading response body: %v", err)
		return "", err
	}
	logger.Info().Msgf("Email Messenger: response from provider: %v", string(body))

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected response status code: %d", resp.StatusCode)
	} else {
		logger.Info().Msgf("Email Messenger: Successfully sent email")
	}
	return string(body), nil
}

func SendToDefaultEmailer(m messenger.Message, e *Emailer) (string, error) {
	logger := m.Ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	var (
		srv *Server
	)

	has := false
	srv, has = e.smtpServerMap[m.GatewayId]

	if !has {
		srv, has = e.smtpServerMap["default"]
	}
	if !has {
		logger.Error().Msgf("no smtp server found for the email request")
		return "", fmt.Errorf("no smtp server found")
	}

	value, _ := json.Marshal(m)
	logger.Info().Msgf("Sending email to %v", string(value))
	m.From = srv.HelloHostname
	// Are there attachments?
	var files []smtppool.Attachment
	if m.Attachments != nil {
		files = make([]smtppool.Attachment, 0, len(m.Attachments))
		for _, f := range m.Attachments {

			if int64(len(f.Content)) <= maxEmailAttachmentSize {
				a := smtppool.Attachment{
					Filename: f.Name,
					Header:   f.Header,
					Content:  make([]byte, len(f.Content)),
				}
				copy(a.Content, f.Content)
				files = append(files, a)
			}
		}
	} else if m.S3Files != nil {
		files = make([]smtppool.Attachment, 0, len(m.S3Files))

		for _, f := range m.S3Files {

			if f.ContentLength <= maxEmailAttachmentSize {

				content, err := downloadFile(f.FileUrl)

				if err == nil {
					a := smtppool.Attachment{
						Filename: f.FileName,
						Header:   MakeAttachmentHeader(f.FileName, f.Encoding, f.ContentType),
						Content:  content,
					}
					files = append(files, a)
				} else {
					logger.Error().Msgf("Error while processing the attachment: %v email could not be sent", err)
					return "", err
				}
			}
		}

	}

	var emails []string

	if m.Encrypted {

		for _, a := range m.To {
			c := utils.Decryptdata(a, logger)
			emails = append(emails, c)
		}
	} else {
		emails = append(emails, m.To...)
	}

	em := smtppool.Email{
		From:        m.From,
		To:          emails,
		Subject:     m.Subject,
		Attachments: files,
		Bcc:         m.Bcc,
		Cc:          m.Cc,
	}

	em.Headers = textproto.MIMEHeader{}

	// Attach SMTP level headers.
	for k, v := range srv.EmailHeaders {
		em.Headers.Set(k, v)
	}

	// Attach e-mail level headers.
	for k, v := range m.Headers {
		em.Headers.Set(k, v[0])
	}

	// If the `Return-Path` header is set, it should be set as the
	// the SMTP envelope sender (via the Sender field of the email struct).
	if sender := em.Headers.Get(hdrReturnPath); sender != "" {
		em.Sender = sender
		em.Headers.Del(hdrReturnPath)
	}

	switch m.ContentType {
	case "plain":
		em.Text = []byte(m.Body)
	default:
		em.HTML = m.Body
		if len(m.AltBody) > 0 {
			em.Text = m.AltBody
		}
	}

	return "", srv.pool.Send(em)
}

func MakeAttachmentHeader(filename, encoding, contentType string) textproto.MIMEHeader {
	if encoding == "" {
		encoding = "base64"
	}
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	h := textproto.MIMEHeader{}
	h.Set("Content-Disposition", "attachment; filename="+filename)
	h.Set("Content-Type", fmt.Sprintf("%s; name=\""+filename+"\"", contentType))
	h.Set("Content-Transfer-Encoding", encoding)
	return h
}

// Flush flushes the message queue to the server.
func (e *Emailer) Flush() error {
	return nil
}

// Close closes the SMTP pools.
func (e *Emailer) Close() error {
	defer func() {
		if r := recover(); r != nil {
			return
		}
	}()

	for _, s := range e.smtpServerMap {
		s.pool.Close()
	}
	return nil
}

func downloadFile(presignedURL string) ([]byte, error) {

	resp, err := http.Get(presignedURL)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status code: %d", resp.StatusCode)
	}

	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	return fileContent, nil
}
