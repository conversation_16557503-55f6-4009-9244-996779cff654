package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/textproto"
	"strconv"
	"strings"
	"time"

	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/internal/manager"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

// handleSendTxMessage handles the sending of a transactional message.
func handleSendTxMessage(c echo.Context) error {

	var (
		app    = c.Get("app").(*App)
		m      models.TxMessage
		logger = c.Get("logger").(push.Logger)
	)

	logger.Info().Msg("Got request for txn api")

	if err := c.Bind(&m); err != nil {
		logger.Error().Msgf("got error %v", err)
		return err
	}

	request, _ := json.Marshal(m)

	logger.Info().Msgf(" request body : " + string(request))

	ctx := tracer.WrapEchoContextLogger(c)

	if err := ProcessTxnRequest(m, app, logger, ctx); err != nil {
		return err
	}

	logger.Info().Msgf("Got OK response from txn api")
	return c.JSON(http.StatusOK, okResp{true})
}

func ProcessTxnRequest(m models.TxMessage, app *App, logger push.Logger, ctx context.Context) error {

	templateId, _ := strconv.Atoi(string(m.TemplateID))

	if r, err := validateTxMessage(m, app); err != nil {
		logger.Error().Msgf("Error in txn api %v", err)
		return err
	} else {
		m = r
	}
	// Get the cached tx template.
	tpl, err := app.manager.GetTpl(templateId)

	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.notFound", "name", fmt.Sprintf("template %d", templateId)))
	}

	var gt *models.GateWayDetails

	if app.manager.GetGatewayFlag(m.Messenger) {

		key := fmt.Sprintf("%s_default", m.Messenger)
		has := false
		var val interface{}

		if tpl.Constant != nil {
			val, has = tpl.Constant[m.Messenger]
		}

		if has {
			key = fmt.Sprintf("%s_%v", m.Messenger, val)
		}

		gt, err = app.manager.GetGateway(key)

		if err != nil {
			return err
		}

		if gt == nil {
			gt, err = app.manager.GetGateway(fmt.Sprintf("%s_default", m.Messenger))

			if err != nil {
				return err
			}
		}

		if gt != nil && gt.Method.String == "smtp" {
			m.GatewayId = fmt.Sprintf("%v", gt.ID)
		}
	}

	defaultPref := false
	tid := ""
	//for email details
	containsED := false
	if m.Messenger == "fcm" {
		defaultPref = app.manager.GetCatFromMap(tpl.Category).Channels.FCM.Value
		logger.Info().Msgf("inside fcm block, defaultPref = %v", defaultPref)
	} else if m.Messenger == "email" {
		defaultPref = app.manager.GetCatFromMap(tpl.Category).Channels.Email.Value
		logger.Info().Msgf("inside email block, defaultPref = %v", defaultPref)
		if len(m.MemberIds) != 0 {
			tid = m.MemberIds[0]
		}
		if len(m.EmailDetails.To) != 0 {
			m.MemberIds = []string{m.EmailDetails.To[0]}
			containsED = true
		}
	} else if m.Messenger == "sms" {
		defaultPref = app.manager.GetCatFromMap(tpl.Category).Channels.SMS.Value
		logger.Info().Msgf("inside sms block, defaultPref = %v", defaultPref)
	} else if m.Messenger == "mqtt" {
		defaultPref = app.manager.GetCatFromMap(tpl.Category).Channels.MQTT.Value
		logger.Info().Msgf("inside mqtt block, defaultPref = %v", defaultPref)
	} else {
		defaultPref = true
		logger.Info().Msgf("inside else block, defaultPref = %v", defaultPref)
	}

	var (
		num       = len(m.MemberIds)
		isMembers = true
	)
	if len(m.MemberIds) < 0 {
		num = len(m.MemberIds)
		isMembers = false
	}
	subs := make(map[string]interface{})

	notFound := []string{}
	for n := 0; n < num; n++ {
		var (
			subName string
		)
		if isMembers {
			subName = m.MemberIds[n]
		}
		var sub models.Subscriber
		sub.Name = subName
		subs["Target"] = subName
		sub.Type = m.MemberType
		if m.MemberType == "" && (m.DirectFcm == true || m.DirectFcm == "true") {
			sub.Type = "customer"
		} else if m.MemberType == "" && (m.DirectFcm == nil || m.DirectFcm == false || m.DirectFcm == "false") {
			sub.Type = "terminal"
		}

		pref, err := app.manager.CheckPreference(subName, m.MemberType, tpl.Category, m.Messenger)
		logger.Info().Msgf("value returned by db = %v", pref)
		if err != nil {
			logger.Error().Msgf("error while fetching preferences for subcriber(%s,%s): error = %v will consider default pref", subName, m.MemberType, err)
		}

		shouldSend := mustSend(pref, defaultPref)
		logger.Info().Msgf("shouldSend = %v", shouldSend)
		if !shouldSend {
			if m.MemberType != "" {
				createAndSendMessengerLog(logger, m.Messenger, subName, strings.ToLower(m.MemberType), "", "rejected", "preferred_out", "", "system", "preference being disabled", templateId)
				continue
			}
		}
		if m.MemberType != "" && m.MemberType != "customer" {
			shouldSend = validateSegmentMembership(tpl.ExcludedSegments, tpl.IncludedSegments, subName, m.MemberType, logger, ctx)
			if !shouldSend {
				createAndSendMessengerLog(logger, m.Messenger, subName, strings.ToLower(m.MemberType), "", "rejected", "exclusion_check", "", "system", "segment exclusion", templateId)
				continue
			}
		}
		var cacheData models.Cache
		if tpl.IsCachePresent && len(tid) != 0 {
			cacheData = external.GetCacheTerminalData(tid, ctx)
		} else if tpl.IsCachePresent {
			cacheData = external.GetCacheTerminalData(subName, ctx)
		}

		if m.Messenger == emailMsgr && !containsED {
			sub.Email = m.SubscriberEmails[n]
			sub.Type = "customer"
			subs["Target"] = m.SubscriberEmails[n]
		} else if (m.Messenger == smsMsgr || m.Messenger == whatsappMsgr) && m.MemberType != "customer" && m.MemberType != "" {
			cache := external.GetCacheTerminalData(subName, ctx)
			if cache.MobileNumber != "" {
				mobileNo := utils.Decryptdata(cache.MobileNumber, logger)
				sub.Name = mobileNo
				subs["Target"] = mobileNo
				sub.Type = "customer"
			} else if cache.TerminalId == "" {
				return fmt.Errorf("terminalId %v not found in cache and via api", subName)
			}
		} else if (m.Messenger == mqttMsgr) && m.MemberType != "customer" && m.MemberType != "" {
			imei := manager.GetIMEIData(ctx, logger, subName, m.MemberType)
			if len(imei) != 0 {
				sub.Name = imei[0]
				subs["Target"] = imei[0]
				sub.Type = "customer"
			} else {
				return fmt.Errorf("imei for terminalID %v not found in api", subName)
			}
		}
		start := time.Now()
		// Render the message.
		m.Subscriber = subs
		if err := m.RenderV2(sub, tpl, gt, cacheData); err != nil {
			logger.Error().Msgf("Error occured while rendering message: %v", err)
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("globals.messages.errorFetching", "name"))
		}
		logger.Info().Msgf("Rendered message in %v", time.Since(start))
		// Prepare the final message.
		msg := manager.Message{}
		msg.Subscriber = sub
		msg.Target = sub.Name
		if containsED {
			msg.To = m.EmailDetails.To
			msg.Cc = m.EmailDetails.Cc
			msg.Bcc = m.EmailDetails.Bcc
			msg.Target = m.EmailDetails.To[0]
			msg.Subscriber.Type = "customer"
		} else {
			msg.To = []string{sub.Email}
		}
		msg.From = m.FromEmail
		msg.Subject = m.Subject
		msg.ContentType = m.ContentType
		msg.Messenger = m.Messenger
		msg.Body = m.Body
		msg.AdditionalValues = m.AdditionalValues
		msg.Label = tpl.Name
		msg.Encrypted = m.Encrypted
		msg.S3Files = m.Files
		msg.DirectFcm = strings.Compare(fmt.Sprint(m.DirectFcm), "true") == 0
		msg.MessageType = tpl.MessageType
		msg.RequestBody = m.RequestBody
		msg.RequestParams = m.RequestParams
		msg.RequestHeaders = m.RequestHeaders
		msg.Roles = m.Roles
		msg.Ctx = ctx
		msg.GatewayId = m.GatewayId
		//Additional parameters for handling dedupe in processes
		msg.Cache = cacheData
		msg.ProcessId = m.ProcessId
		msg.DeDuplication = tpl.DeDuplication.Bool
		msg.DuplicationLevel = tpl.DuplicationLevel.String
		msg.ProcessDuration = tpl.ProcessDuration
		msg.TemplateId = templateId

		if gt != nil {
			msg.Url = gt.Url.String
			msg.Method = gt.Method.String
		}
		// Optional headers.
		if len(m.Headers) != 0 {
			msg.Headers = make(textproto.MIMEHeader, len(m.Headers))
			for _, set := range m.Headers {
				for hdr, val := range set {
					msg.Headers.Add(hdr, val)
				}
			}
		}

		if err := app.manager.PushMessage(msg); err != nil {
			logger.Error().Msgf("error sending message (%s): %v", msg.Subject, err)
			return err
		}
	}

	if len(notFound) > 0 {
		return echo.NewHTTPError(http.StatusBadRequest, strings.Join(notFound, "; "))
	}
	return nil
}

func mustSend(s string, dPref bool) bool {
	lower := strings.ToLower(s)
	switch lower {
	case "true":
		return true
	case "false":
		return false
	default:
		return dPref
	}
}

func validateTxMessage(m models.TxMessage, app *App) (models.TxMessage, error) {
	// if len(m.MemberIds) > 0 {
	// 	return m, echo.NewHTTPError(http.StatusBadRequest,
	// 		app.i18n.Ts("globals.messages.invalidFields", "name", "do not send `subscriber_email`"))
	// }
	// if len(m.SubscriberIDs) > 0 && m.SubscriberID != 0 {
	// 	return m, echo.NewHTTPError(http.StatusBadRequest,
	// 		app.i18n.Ts("globals.messages.invalidFields", "name", "do not send `subscriber_id`"))
	// }

	// if m.SubscriberEmail != "" {
	// 	m.SubscriberEmails = append(m.SubscriberEmails, m.SubscriberEmail)
	// }

	// if m.SubscriberID != 0 {
	// 	m.SubscriberIDs = append(m.SubscriberIDs, m.SubscriberID)
	// }

	if len(m.MemberIds) == 0 {
		return m, echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.invalidFields", "name", "send member ids."))
	}

	// for n, email := range m.SubscriberEmails {
	// 	if m.SubscriberEmail != "" {
	// 		em, err := app.importer.SanitizeEmail(email)
	// 		if err != nil {
	// 			return m, echo.NewHTTPError(http.StatusBadRequest, err.Error())
	// 		}
	// 		m.SubscriberEmails[n] = em
	// 	}
	// }

	// if m.FromEmail == "" {
	// 	m.FromEmail = app.constants.FromEmail
	// }

	if m.Messenger == "" {
		m.Messenger = emailMsgr
	} else if !app.manager.HasMessenger(m.Messenger) {
		return m, echo.NewHTTPError(http.StatusBadRequest, app.i18n.Ts("campaigns.fieldInvalidMessenger", "name", m.Messenger))
	}

	return m, nil
}

func validateSegmentMembership(exclusionSegments, inclusionSegments []string, memberId, memberType string, logger push.Logger, ctx context.Context) bool {

	if len(exclusionSegments) == 0 && len(inclusionSegments) == 0 {
		return true
	}

	req := models.SegmentEligibilityRequest{
		Members:           []string{memberId},
		MemberType:        memberType,
		InclusionSegments: inclusionSegments,
		ExclusionSegments: exclusionSegments,
	}

	res, err := external.CheckSegmentEligibility(config.ServerConfig.SegmentServiceUrl, req, logger, ctx)
	if err != nil {
		logger.Error().Msgf("error occured in segment eligibility api, %v", err)
		return false
	}
	if len(res.Result) > 0 {
		k, has := res.Result[memberId]

		logger.Info().Msgf("answer %v and %v and total res %v", k, has, req)
	}
	return true
}

func createAndSendMessengerLog(logger push.Logger, messengerType, memberName, memberType, target, status, remarks, referenceID, createdBy, reason string, templateId int) {
	logDetails := models.MessengerLog{
		RequestType:   "real_time_message",
		MessengerType: messengerType,
		MemberName:    memberName,
		MemberType:    memberType,
		Target:        target,
		Status:        status,
		Remarks:       remarks,
		ReferenceID:   referenceID,
		CreatedBy:     createdBy,
		TemplateId:    templateId,
	}
	app.manager.SendMessengerLogs(logDetails)
	logger.Info().Msgf("Txn: message will not be sent due to %s for subscriber(%s, %s)", reason, memberName, memberType)
}

func handleSendTestMessage(c echo.Context) error {

	var (
		app    = c.Get("app").(*App)
		m      models.TestTxnMessage
		logger = c.Get("logger").(push.Logger)
	)

	logger.Info().Msg("Got request for test txn api")

	if err := c.Bind(&m); err != nil {
		logger.Error().Msgf("got error %v", err)
		return err
	}

	request, _ := json.Marshal(m)

	logger.Info().Msgf("request body : " + string(request))

	ctx := tracer.WrapEchoContextLogger(c)

	if err := ProcessTestMsgRequest(m, app, logger, ctx); err != nil {
		return err
	}

	logger.Info().Msgf("Got OK response from txn api")
	return c.JSON(http.StatusOK, okResp{true})
}

func ProcessTestMsgRequest(m models.TestTxnMessage, app *App, logger push.Logger, ctx context.Context) error {

	templateId, _ := strconv.Atoi(string(m.TemplateID))

	if _, err := validateTxMessage(m.TxMessage, app); err != nil {
		logger.Error().Msgf("Error in test txn api %v", err)
		return err
	}
	// Get the cached tx template.
	tpl, err := app.manager.GetTpl(templateId)

	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.notFound", "name", fmt.Sprintf("template %d", templateId)))
	}

	var gt *models.GateWayDetails

	if app.manager.GetGatewayFlag(m.Messenger) {

		key := fmt.Sprintf("%s_default", m.Messenger)
		has := false
		var val interface{}

		if tpl.Constant != nil {
			val, has = tpl.Constant[m.Messenger]
		}

		if has {
			key = fmt.Sprintf("%s_%v", m.Messenger, val)
		}

		gt, err = app.manager.GetGateway(key)

		if err != nil {
			return err
		}

		if gt == nil {
			gt, err = app.manager.GetGateway(fmt.Sprintf("%s_default", m.Messenger))

			if err != nil {
				return err
			}
		}

		if gt != nil && gt.Method.String == "smtp" {
			m.GatewayId = fmt.Sprintf("%v", gt.ID)
		}
	}
	tid := ""
	//for email details
	containsED := false
	if m.Messenger == "email" {
		if len(m.MemberIds) != 0 {
			tid = m.MemberIds[0]
		}
		if len(m.EmailDetails.To) != 0 {
			m.MemberIds = []string{m.EmailDetails.To[0]}
			containsED = true
		}
	}

	var (
		num       = len(m.MemberIds)
		isMembers = true
	)
	if len(m.MemberIds) < 0 {
		num = len(m.MemberIds)
		isMembers = false
	}
	subs := make(map[string]interface{})

	notFound := []string{}
	for n := 0; n < num; n++ {
		var (
			subName string
		)
		if isMembers {
			subName = m.MemberIds[n]
		}
		var sub models.Subscriber
		sub.Name = subName
		subs["Target"] = subName
		sub.Type = m.MemberType
		if m.MemberType == "" && (m.DirectFcm == true || m.DirectFcm == "true") {
			sub.Type = "customer"
		} else if m.MemberType == "" && (m.DirectFcm == nil || m.DirectFcm == false || m.DirectFcm == "false") {
			sub.Type = "terminal"
		}

		var cacheData models.Cache
		if tpl.IsCachePresent && len(tid) != 0 {
			cacheData = external.GetCacheTerminalData(tid, ctx)
		} else if tpl.IsCachePresent {
			cacheData = external.GetCacheTerminalData(subName, ctx)
		}

		if m.Messenger == emailMsgr && !containsED {
			sub.Email = m.SubscriberEmails[n]
			sub.Type = "customer"
			subs["Target"] = m.SubscriberEmails[n]
		} else if (m.Messenger == smsMsgr || m.Messenger == whatsappMsgr) && m.MemberType != "customer" && m.MemberType != "" {
			cache := external.GetCacheTerminalData(subName, ctx)
			if cache.MobileNumber != "" {
				mobileNo := utils.Decryptdata(cache.MobileNumber, logger)
				sub.Name = mobileNo
				subs["Target"] = mobileNo
				sub.Type = "customer"
			} else if cache.TerminalId == "" {
				return fmt.Errorf("terminalId %v not found in cache and via api", subName)
			}
		} else if (m.Messenger == mqttMsgr) && m.MemberType != "customer" && m.MemberType != "" {
			imei := manager.GetIMEIData(ctx, logger, subName, m.MemberType)
			if len(imei) != 0 {
				sub.Name = imei[0]
				subs["Target"] = imei[0]
				sub.Type = "customer"
			} else {
				return fmt.Errorf("imei for terminalID %v not found in api", subName)
			}
		}
		camp := models.Campaign{
			Subject:      m.Subject,
			FromEmail:    m.FromEmail,
			Body:         m.CampaignBody,
			TemplateBody: m.CampaignBody,
			Messenger:    m.Messenger,
			AltBody:      m.AltBody,
			FcmKeyval:    m.FcmKeyval,
			ContentType:  m.ContentType,
		}
		if err := camp.CompileTemplate(app.manager.TemplateFuncs(&camp), false); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("templates.errorCompiling", "error", err.Error()))
		}
		start := time.Now()
		campaignMsg, err := app.manager.NewCampaignMessage(&camp, sub, ctx, "")
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest,
				app.i18n.Ts("templates.errorRendering", "error", err.Error()))
		}
		logger.Info().Msgf("created and rendered test message in %v", time.Since(start))

		msg := manager.Message{}
		msg.Subscriber = sub
		msg.Target = sub.Name
		if containsED {
			msg.To = m.EmailDetails.To
			msg.Cc = m.EmailDetails.Cc
			msg.Bcc = m.EmailDetails.Bcc
			msg.Target = m.EmailDetails.To[0]
			msg.Subscriber.Type = "customer"
		} else {
			msg.To = []string{sub.Email}
		}
		msg.From = m.FromEmail
		msg.Subject = campaignMsg.Subject()
		msg.ContentType = m.ContentType
		msg.Messenger = m.Messenger
		msg.Body = campaignMsg.Body()
		msg.AdditionalValues = m.AdditionalValues
		msg.Label = tpl.Name
		msg.Encrypted = m.Encrypted
		msg.S3Files = m.Files
		msg.DirectFcm = strings.Compare(fmt.Sprint(m.DirectFcm), "true") == 0
		msg.MessageType = tpl.MessageType
		msg.RequestBody = campaignMsg.RequestBody
		msg.RequestParams = campaignMsg.RequestParams
		msg.RequestHeaders = campaignMsg.RequestHeaders
		msg.Roles = m.Roles
		msg.Ctx = ctx
		msg.GatewayId = m.GatewayId
		msg.Data = campaignMsg.Data
		//Additional parameters for handling dedupe in processes
		msg.Cache = cacheData
		msg.ProcessId = m.ProcessId
		msg.DeDuplication = tpl.DeDuplication.Bool
		msg.DuplicationLevel = tpl.DuplicationLevel.String
		msg.ProcessDuration = tpl.ProcessDuration

		if gt != nil {
			msg.Url = gt.Url.String
			msg.Method = gt.Method.String
		}
		// Optional headers.
		if len(m.Headers) != 0 {
			msg.Headers = make(textproto.MIMEHeader, len(m.Headers))
			for _, set := range m.Headers {
				for hdr, val := range set {
					msg.Headers.Add(hdr, val)
				}
			}
		}

		if err := app.manager.PushMessage(msg); err != nil {
			logger.Error().Msgf("error sending message (%s): %v", msg.Subject, err)
			return err
		}
	}

	if len(notFound) > 0 {
		return echo.NewHTTPError(http.StatusBadRequest, strings.Join(notFound, "; "))
	}
	return nil
}
