/* Added by <PERSON><PERSON> for external calls */
package external

import (
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/knadh/listmonk/logger"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/xuri/excelize/v2"
	"gopkg.in/volatiletech/null.v6"

	"encoding/csv"
	"encoding/json"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"

	push "github.com/phuslu/log"
	"google.golang.org/api/option"
)

// Firebase
var (
	firebaseApp            = &firebaseClient{}
	userTerminalInfoApiUrl string
	skipSSLFlag            bool
	soundboxServiceApiUrl  string
)

// RedisClusterClient struct
type firebaseClient struct {
	a *firebase.App
	m *messaging.Client
}

func SetupFirebase(firebaseJsonPath string, skipSSL bool) (*firebase.App, context.Context, *messaging.Client) {
	ctx := context.Background()
	serviceAccountKeyFilePath, err := filepath.Abs(firebaseJsonPath)
	if err != nil {
		panic("Unable to load serviceAccountKeys.json file")
	}

	skipSSLFlag = skipSSL

	opt := option.WithCredentialsFile(serviceAccountKeyFilePath)
	if skipSSL {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: skipSSL,
		}
		http.DefaultTransport.(*http.Transport).TLSClientConfig = tlsConfig

	}
	// transport := http.DefaultTransport.(*http.Transport)

	// http.DefaultTransport = &outbound.CustomTransport{
	// 	T: transport,
	// }
	//Firebase admin SDK initialization
	app, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		panic("Firebase load error")
	}

	//Messaging client
	client, err := app.Messaging(ctx)
	if err != nil {
		logger.Log.Error().Msgf("Error setting up firebase %v", err)
		panic("Error initializing firebase client")
	}
	logger.Log.Info().Msg("Firebase connected successfully")
	firebaseApp.a = app
	firebaseApp.m = client
	return app, ctx, client
}

type NotificationResult struct {
	Token   string
	Success bool
	Error   string
}

func SendFCMNotification(registartionTokens []string, msgTitle string, msgBody string, image string, data map[string]string, notifLabel string, messageType string, logger push.Logger) ([]NotificationResult, error) {
	ctx := context.Background()

	var notification *messaging.Notification
	var apnsConfig *messaging.APNSConfig

	if messageType == "notification" {

		notification = &messaging.Notification{
			Title:    msgTitle,
			Body:     msgBody,
			ImageURL: image,
		}

		apnsConfig = &messaging.APNSConfig{

			Headers: map[string]string{
				"apns-prority": "10",
			},

			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert:          &messaging.ApsAlert{},
					MutableContent: true,
				},
			},
			FCMOptions: &messaging.APNSFCMOptions{
				AnalyticsLabel: notifLabel,
			},
		}

	} else {
		notification = nil
		apnsConfig = nil
		data["body"] = msgBody
		data["title"] = msgTitle
		data["imageUrl"] = image
		data["analytics_label"] = notifLabel
		if value, exists := data["NotificationType"]; exists && value == "inAppPopup" {
			apnsConfig = &messaging.APNSConfig{
				Payload: &messaging.APNSPayload{
					Aps: &messaging.Aps{
						ContentAvailable: true,
					},
				},
			}
		} else {
			apnsConfig = &messaging.APNSConfig{
				Payload: &messaging.APNSPayload{
					Aps: &messaging.Aps{
						Alert: &messaging.ApsAlert{
							Body:  msgBody,
							Title: msgTitle,
						},
						MutableContent:   true,
						ContentAvailable: true,
					},
				},
			}
		}

	}

	androidConfig := &messaging.AndroidConfig{
		Notification: &messaging.AndroidNotification{},
		FCMOptions: &messaging.AndroidFCMOptions{
			AnalyticsLabel: notifLabel,
		},
	}

	message := &messaging.MulticastMessage{
		Notification: notification,
		Data:         data,
		Android:      androidConfig,
		APNS:         apnsConfig,
		Tokens:       registartionTokens,
	}

	payload, _ := json.Marshal(message)

	logger.Info().Msgf("sending notification %v", string(payload))
	response, err := firebaseApp.m.SendEachForMulticast(ctx, message)
	if err != nil {
		logger.Error().Msgf("Error pushing notification to firebase: %v", err)
		// utils.UpdateCounter(campId+"_"+"listmonk"+"_failed_delivers", int64(len(registartionTokens)))
		return nil, err
	}
	// Create a slice to store the results for each token
	results := make([]NotificationResult, len(registartionTokens))
	// Iterate through each response and capture success/failure for each token
	for i, resp := range response.Responses {
		result := NotificationResult{
			Token:   registartionTokens[i],
			Success: resp.Success,
		}
		if !resp.Success {
			result.Error = resp.Error.Error() // Capture the error message if the notification failed
		}
		results[i] = result
	}
	logger.Info().Msgf("Response success count : %v", response.SuccessCount)
	logger.Info().Msgf("Response failure count : %v", response.FailureCount)
	// utils.UpdateCounter(campId+"_"+"listmonk"+"_"+"success_delivers", int64(response.SuccessCount))
	// utils.UpdateCounter(campId+"_"+"listmonk"+"_"+"failed_delivers", int64(response.FailureCount))
	return results, nil
}

func SendFCMNotificationImage(registartionTokens []string) error {
	ctx := context.Background()
	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: "New image",
			Body:  "Check out this cool image!",
		},
		Android: &messaging.AndroidConfig{
			Notification: &messaging.AndroidNotification{
				ImageURL: "https://hdfcbadgeimages.s3.ap-south-1.amazonaws.com/loan.png",
			},
		},
		Tokens: registartionTokens,
	}
	response, err := firebaseApp.m.SendMulticast(ctx, message)
	if err != nil {
		fmt.Println("Error pushing notofication to firebase", err)
		return err
	}
	fmt.Println("Response success count : ", response.SuccessCount)
	fmt.Println("Response failure count : ", response.FailureCount)
	return nil
}

func SendFCMNotificationTest(registartionTokens []string) error {
	ctx := context.Background()
	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: "New image",
			Body:  "Check out this cool image!",
		},
		Tokens: registartionTokens,
	}
	response, err := firebaseApp.m.SendMulticast(ctx, message)
	if err != nil {
		fmt.Println("Error pushing notofication to firebase", err)
		return err
	}
	fmt.Println("Response success count : ", response.SuccessCount)
	fmt.Println("Response failure count : ", response.FailureCount)
	return nil
}
func ConvertToJson(a []models.Atribs) string {

	j, err := json.Marshal(a)
	if err != nil {
		fmt.Printf("Error while converting list to json: %s", err.Error())
		return ""
	} else {
		return string(j)
	}
}

type Message struct {
	SegmentName       string   `json:"segmentName"`
	SegmentType       string   `json:"segmentType"`
	SegmentSource     string   `json:"segmentSource"`
	SegmentMemberType string   `json:"segmentMemberType"`
	UploadType        string   `json:"uploadType"`
	Members           []string `json:"v"`
	Status            string   `json:"status"`
	LoggedInUser      string   `json:"loggedInUser"`
}

func CreateSegment(members []string, segments []models.List, ctx context.Context) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	config := utils.GetConfigProperties()
	url := config.ServerConfig.SegmentServiceUrl + "SEG2/api/legacy/createSegment/v2"
	logger.Info().Msgf("URL:> %s", url)

	for _, item := range segments {
		if item.Description != "Listmonk" {
			continue
		}

		m := Message{item.Name, "static", "listmonk", "Terminal", "LIST_UPLOAD", members, "PUBLISHED", ""}
		jsonStr, err := json.Marshal(m)
		req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
		req.Header.Set("Content-Type", "application/json")

		client, err := tracer.GetHttpClient(config.ServerConfig.SkipSSLCheck)

		if err != nil {
			logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
			continue
		}

		req = tracer.GetRequestWithTraceContext(req, ctx)
		resp, err := client.Do(req)
		if err != nil {
			logger.Error().Msgf("got error resposne from the createSegment/v2 api, detailed error %v", err)
		}
		defer resp.Body.Close()

		logger.Info().Msgf("response Status: %v", resp.Status)
		logger.Info().Msgf("response Headers: %v", resp.Header)
		body, _ := io.ReadAll(resp.Body)
		logger.Info().Msgf("response Body: %v", string(body))
	}
}

func GetCacheTerminalData(memberId string, ctx context.Context) models.Cache {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	terminalObj := models.Cache{}
	err, terminal := utils.GetRedisData(memberId)
	if err != nil {
		logger.Error().Msgf("Error occured while fetching from redis %v", err)
	}
	if terminal != "" {
		err := json.Unmarshal([]byte(terminal), &terminalObj)
		if err != nil {
			logger.Error().Msgf("Error occured while unmarshalling json %v", err)
		}
	}
	if terminalObj.TerminalId == "" {
		cache, err := GetCacheTerminlFromApi(memberId, ctx)
		if err {
			logger.Error().Msgf("error: terminalId %v not found in cache or api ", memberId)
		} else {
			terminalObj = *cache
		}
	}
	return terminalObj
}

func GetRealTimeSegmentProperties(serverUrl string, name string, ctx context.Context) []string {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	url := serverUrl + "SEG2/api/composite-properties/realTime"
	logger.Info().Msgf("URL:> %s", url)
	r := models.RealTimeSegReq{name}
	jsonStr, err := json.Marshal(r)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return []string{}
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while calling api composite-properties/realTime %v", err)
		return []string{}
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %s", resp.Status)
	logger.Info().Msgf("response Headers: %v", resp.Header)
	body, _ := io.ReadAll(resp.Body)
	logger.Info().Msgf("response Body: %v", string(body))
	realTimeSegResp := []models.RealTimeSegResp{}
	err = json.Unmarshal((body), &realTimeSegResp)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return []string{}
	}
	response := make([]string, 0, len(realTimeSegResp))
	for _, r := range realTimeSegResp {
		response = append(response, r.Name)
	}
	return response
}

func GetRealTimeSegmentData(propertyName string, reqval string, serverUrl string, ctx context.Context) string {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	url := serverUrl + "SEG2/api/realTimeSegmentResults"
	logger.Info().Msgf("URL:> %s", url)
	r := models.Request{TerminalId: reqval}
	request := models.RealTimeSegDataReq{PropertyName: propertyName, Request: r}
	jsonStr, err := json.Marshal(request)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)
	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return ""
	}
	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while calling api realTimeSegmentResults %v", err)
		return ""
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %s", resp.Status)
	logger.Info().Msgf("response Headers: %v", resp.Header)
	body, _ := io.ReadAll(resp.Body)
	response := models.RealTimeSegDataResp{}
	err = json.Unmarshal((body), &response)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return ""
	}
	logger.Info().Msgf("response Body: %v", string(body))

	if len(response.Results.Result) > 0 {
		return response.Results.Result[0]
	} else {
		return ""
	}
}

func GetCacheTerminlFromApi(tid string, ctx context.Context) (*models.Cache, bool) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	r := models.TerminalInfoReq{
		Tids: []string{tid}}

	jsonStr, err := json.Marshal(r)

	if err != nil {
		logger.Error().Msgf("Error marshalling json: %v", err)
		return nil, true
	}

	logger.Info().Msgf("Fetching Tid from user-terminal-info Api: %v with request: %v", userTerminalInfoApiUrl, string(jsonStr))

	req, _ := http.NewRequest("POST", userTerminalInfoApiUrl, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return nil, true
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)

	resp, err := client.Do(req)

	if err != nil {
		logger.Error().Msgf("Error while getting results from user-terminal-info api: %v", err)
		return nil, true
	}
	defer resp.Body.Close()

	logger.Info().Msgf("Response from user-terminal-info: %v", resp.Status)
	if resp.StatusCode != 200 {
		logger.Error().Msg("Got non-Success response from user-terminal-info")
		return nil, true
	}
	body, err := io.ReadAll(resp.Body)

	if err != nil {
		logger.Error().Msgf("Error while reading response from user-terminal-info %v", err)
		return nil, true
	}

	logger.Info().Msgf("Response from user-terminal-info Body: %v", string(body))

	terminalResponse := models.TerminalResponse{}

	err = json.Unmarshal((body), &terminalResponse)
	if err != nil {
		logger.Error().Msgf("Error while unmarshalling response from user-terminal-info %v", err)
		return nil, true
	}

	if len(terminalResponse.TerminalsDetails) == 0 {
		logger.Info().Msgf("Cannot find details for terminal %v ", tid)
		return nil, true
	}

	cache, has := terminalResponse.TerminalsDetails[0][tid]

	if !has {
		logger.Info().Msgf("Cannot find details for terminal %v ", tid)
		return nil, true
	}

	return &cache, false
}

func SetUserTerminalInfoApiUrl(url, soundboxApiUrl string) {
	userTerminalInfoApiUrl = url
	soundboxServiceApiUrl = soundboxApiUrl
}

func DownloadPreSignedUrlFile(fileUrl string, localFilePath string, ctx context.Context) error {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	logger.Info().Msgf("Downloading presigned file from S3 %v", fileUrl)
	out, err := os.Create(localFilePath)
	if err != nil {
		logger.Error().Msgf("Error creating local file %v, detailed error: %v", localFilePath, err)
		return err
	}
	defer out.Close()

	// Get the data
	resp, err := http.Get(fileUrl)
	if err != nil {
		logger.Error().Msgf("Error downloading presigned file with url %v, %v", fileUrl, err)
		return err
	}
	defer resp.Body.Close()

	// Writer the body to file
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		logger.Error().Msgf("Error copying presigned file with url %v, %v", fileUrl, err)
		return err
	}

	return nil
}

func DownloadFromS3Bucket(bucket string, path string, campaignId string, accessKey string, secretKey string, region string, ctx context.Context) string {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	logger.Info().Msgf("Downloading file from S3 %v", path)
	fileName := "file_" + campaignId
	if strings.HasSuffix(path, ".xlsx") {
		fileName = fileName + ".xlsx"
	} else if strings.HasSuffix(path, ".csv") {
		fileName = fileName + ".csv"
	}

	file, err := os.Create(fileName)
	if err != nil {
		logger.Error().Msgf("error occured %v", err)
	}
	defer file.Close()
	var sess *session.Session
	if accessKey != "" {
		creds := credentials.NewStaticCredentials(accessKey, secretKey, "")
		sess, _ = session.NewSession(&aws.Config{Region: aws.String(region), Credentials: creds})
	} else {
		sess, _ = session.NewSession(&aws.Config{Region: aws.String(region)})
	}

	downloader := s3manager.NewDownloader(sess)
	numBytes, err := downloader.Download(file,
		&s3.GetObjectInput{
			Bucket: aws.String(bucket),
			Key:    aws.String(path),
		})
	if err != nil {
		logger.Error().Msgf("error occured %v", err)
	}

	logger.Info().Msgf("Downloaded fileName %v size: %v unit: %v", file.Name(), numBytes, "bytes")
	return fileName
}

func UploadToS3Bucket(bucket string, path string, file *os.File, accessKey string, secretKey string, region string, cType string, ctx context.Context) error {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	var sess *session.Session
	if accessKey != "" {
		creds := credentials.NewStaticCredentials(accessKey, secretKey, "")
		sess, _ = session.NewSession(&aws.Config{Region: aws.String(region), Credentials: creds})
	} else {
		sess, _ = session.NewSession(&aws.Config{Region: aws.String(region)})
	}
	uploader := s3manager.NewUploader(sess)

	// Upload the file to S3.
	result, err := uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(bucket),
		Key:         aws.String(path),
		Body:        file,
		ContentType: aws.String(cType),
	})
	if err != nil {
		logger.Error().Msgf("failed to upload file, %v", err)
		return err
	}
	logger.Info().Msgf("file uploaded to, %s\n", (result.Location))
	return nil
}

func ReadExcel(path string, ctx context.Context) map[string]map[string]string {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	var data map[string]map[string]string = make(map[string]map[string]string)
	var headers []string
	f, err := excelize.OpenFile(path)
	if err != nil {
		logger.Error().Msgf("error while open excel file %v", err)
		return nil
	}
	defer func() {
		// Close the spreadsheet.
		if err := f.Close(); err != nil {
			logger.Error().Msgf("error while closing file %v", err)
		}
	}()
	// Get the name of the first sheet
	firstSheetName := f.GetSheetName(0)
	logger.Info().Msgf("Sheet name in xlsx file %s for file %s", firstSheetName, path)
	// Get all the rows in the Sheet1.
	rows, err := f.GetRows(firstSheetName)
	if err != nil {
		logger.Error().Msgf("error unable to read data %v", err)
		return nil
	}
	for _, header := range rows[0] {
		logger.Info().Msgf("Header: %v", header)
		headers = append(headers, header)
	}
	logger.Info().Msg("=======")

	for _, row := range rows[1:] {
		data[row[0]] = make(map[string]string)
		for i, colCell := range row {
			logger.Info().Msgf("cols = %v", colCell)
			data[row[0]][headers[i]] = colCell
		}
	}

	logger.Info().Msgf("data: %v", data)
	return data
}

func ReadCsv(path string, ctx context.Context) map[string]map[string]string {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	var data map[string]map[string]string = make(map[string]map[string]string)
	csvIn, err := os.Open(path)
	if err != nil {
		logger.Error().Msgf("Error opening csv %v", err)
		return nil
	}
	r := csv.NewReader(csvIn)

	headers, err := r.Read()
	if err != nil {
		logger.Error().Msgf("Error reading csv %v", err)
		return nil
	}
	for {
		row, err := r.Read()
		if err != nil {
			if err == io.EOF {
				break
			}
		}
		var entry = make(map[string]string)
		for i, cell := range row {

			if err != nil {
				if err == io.EOF {
					break
				}
			}
			if i == 0 {
				data[cell] = entry
			}
			entry[headers[i]] = cell
		}
		logger.Info().Msgf("entry : %v", entry)
	}
	logger.Info().Msgf("data: %v", data)
	return data
}

func ReadCsvHeaders(path string, logger push.Logger) []string {
	csvIn, err := os.Open(path)
	if err != nil {
		logger.Error().Msgf("Error opening csv %v", err)
		return nil
	}
	r := csv.NewReader(csvIn)

	headers, err := r.Read()
	if err != nil {
		logger.Error().Msgf("Error reading csv %v", err)
		return nil
	}
	return headers
}

func GetSegmentMembers(serverUrl string, lastId string, segmentName string, segmentPageSize int, logger push.Logger, ctx context.Context) (models.SegmentMembershipResponse, error) {
	url := serverUrl + "SEG2/internal/api/legacy/v2/get-membership-by-segment-id"
	logger.Info().Msgf("URL:> %v", url)
	r := models.SegmentMembershipBase{
		SegmentName: segmentName,
		LastId:      lastId,
		PageSize:    segmentPageSize,
	}
	jsonStr, _ := json.Marshal(r)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return models.SegmentMembershipResponse{}, err
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while fetching members from segments api %v", err)
		return models.SegmentMembershipResponse{}, err
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %v", resp.Status)
	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		logger.Error().Msgf("response Body: %v", string(body))
		return models.SegmentMembershipResponse{}, fmt.Errorf("error while getting segment members for segmentName %v", segmentName)
	}

	realTimeSegResp := models.SegmentMembershipResponse{}
	err = json.Unmarshal((body), &realTimeSegResp)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return models.SegmentMembershipResponse{}, err
	}
	return realTimeSegResp, nil
}

func CreateReport(reportName string, campaignId string, eventTime string, ctx context.Context) (string, error) {
	config := utils.GetConfigProperties()
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	url := config.ServerConfig.SegmentServiceUrl + "SEG2/api/reportingResults"

	logger.Info().Msgf("URL:> %s", url)
	currentTime := time.Now()
	name := campaignId + "_" + reportName + "_" + currentTime.Format("2006-01-02 15:04:05")
	r := models.ReportApiRequest{
		FileType:         "xlsx",
		ReportName:       reportName,
		Name:             name,
		IsCampaignReport: null.BoolFrom(true),
	}
	jsonStr, _ := json.Marshal(r)
	logger.Info().Msgf("Body:> %v", string(jsonStr))
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return "", err
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)

	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while calling api %v", err)
		return "", err
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %v", resp.Status)
	body, _ := io.ReadAll(resp.Body)
	logger.Info().Msgf("Body:> %v", string(body))
	if resp.StatusCode != 200 {
		logger.Error().Msgf("response Body: %v", string(body))
		return "", errors.New("http response not success")
	}

	reportResults := models.ReportResults{}
	err = json.Unmarshal((body), &reportResults)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return "", errors.New("http response not valid json")
	}
	return reportResults.Results.RequestId, nil
}

/*
 * This will fetch the imei number related to each terminalId
 */
func GetIMEIForTerminal(request models.SoundboxRequest, ctx context.Context) (models.SoundboxResponse, error) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	logger.Info().Msgf("URL:> %s", soundboxServiceApiUrl)

	jsonStr, err := json.Marshal(request)
	req, err := http.NewRequest("POST", soundboxServiceApiUrl, bytes.NewBuffer(jsonStr))
	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return models.SoundboxResponse{}, err
	}
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return models.SoundboxResponse{}, err
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while calling api %s, %v", request, err)
		return models.SoundboxResponse{}, err
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %s", resp.Status)

	body, _ := io.ReadAll(resp.Body)

	logger.Info().Msgf("response Body: %v", string(body))
	soundboxResponse := models.SoundboxResponse{}

	err = json.Unmarshal((body), &soundboxResponse)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return soundboxResponse, err
	}

	return soundboxResponse, nil
}

func CheckSegmentEligibility(serverUrl string, segmentEligibilityRequest models.SegmentEligibilityRequest, logger push.Logger, ctx context.Context) (models.SegmentEligibilityResponse, error) {
	url := serverUrl + "SEG2/api/legacy/check-member-eligibility"
	logger.Info().Msgf("URL:> %v", url)
	jsonStr, _ := json.Marshal(segmentEligibilityRequest)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client, err := tracer.GetHttpClient(skipSSLFlag)

	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return models.SegmentEligibilityResponse{}, err
	}

	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while fetching segmentMemberEligibility from segments api %v", err)
		return models.SegmentEligibilityResponse{}, err
	}
	defer resp.Body.Close()

	logger.Info().Msgf("response Status: %v", resp.Status)
	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		logger.Error().Msgf("response Body: %v", string(body))
		return models.SegmentEligibilityResponse{}, fmt.Errorf("error while getting segmentMemberEligibility for %v", segmentEligibilityRequest)
	}

	segmentMembershipResponse := models.SegmentEligibilityResponse{}
	err = json.Unmarshal((body), &segmentMembershipResponse)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return models.SegmentEligibilityResponse{}, err
	}
	return segmentMembershipResponse, nil
}

// DeleteFileFromS3 deletes a file from the S3 bucket using the AWS SDK.
func DeleteFileFromS3(bucket string, objectKey string, accessKey string, secretKey string, region string, ctx context.Context) error {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	// Create an AWS session
	var sess *session.Session
	if accessKey != "" {
		creds := credentials.NewStaticCredentials(accessKey, secretKey, "")
		sess, _ = session.NewSession(&aws.Config{Region: aws.String(region), Credentials: creds})
	} else {
		sess, _ = session.NewSession(&aws.Config{Region: aws.String(region)})
	}

	// Create an S3 client
	svc := s3.New(sess)

	// Delete the object
	_, err := svc.DeleteObject(&s3.DeleteObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(objectKey),
	})
	if err != nil {
		logger.Error().Msgf("failed to delete file: %v", err)
		return err
	}

	// Confirm deletion
	err = svc.WaitUntilObjectNotExists(&s3.HeadObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(objectKey),
	})
	if err != nil {
		logger.Error().Msgf("error while waiting for object deletion: %v", err)
		return err
	}

	logger.Info().Msgf("file deleted from S3: %s", objectKey)
	return nil
}

func GetReportHeaders(reportName string, serverUrl string, ctx context.Context) models.ReportHeaderResponse {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	url := serverUrl + "/SEG2/api/get-report-headers"
	logger.Info().Msgf("URL:> %s", url)
	request := models.ReportApiRequest{ReportName: reportName}
	jsonStr, err := json.Marshal(request)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	response := models.ReportHeaderResponse{}

	client, err := tracer.GetHttpClient(skipSSLFlag)
	if err != nil {
		logger.Error().Msgf("error occured while preparing http client , detailed error : %v", err)
		return response
	}
	req = tracer.GetRequestWithTraceContext(req, ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.Error().Msgf("error occured while calling api get-report-headers %v", err)
		return response
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		logger.Error().Msgf("error: non Ok response from get-report-headers api:  %v", resp)
		return response
	}

	body, _ := io.ReadAll(resp.Body)

	err = json.Unmarshal((body), &response)
	if err != nil {
		logger.Error().Msgf("error occured while unmarshalling json %v", err)
		return response
	}
	logger.Info().Msgf("Successfuly proccesed get-report-headers api: response Body: %v", string(body))

	return response
}
