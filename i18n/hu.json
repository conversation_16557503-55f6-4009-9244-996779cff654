{"_.code": "hu", "_.name": "Hungary (hu)", "admin.errorMarshallingConfig": "Hiba a konfiguráció rendezésekor: {error}", "analytics.count": "Számláló", "analytics.fromDate": "<PERSON>", "analytics.invalidDates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `t<PERSON><PERSON>` vagy `ig` d<PERSON><PERSON>.", "analytics.isUnique": "The counts are unique per subscriber.", "analytics.links": "<PERSON><PERSON>", "analytics.nonUnique": "The counts are non-unique as individual subscriber tracking is turned off.", "analytics.title": "<PERSON><PERSON><PERSON><PERSON>", "analytics.toDate": "<PERSON> nek", "bounces.source": "<PERSON><PERSON><PERSON>", "bounces.unknownService": "Ismeretlen szolgáltatás.", "bounces.view": "Visszapattanások megtekintése", "campaigns.addAltText": "Alternatív egyszerű szöveges üzenet hozzáadása", "campaigns.archive": "Archive", "campaigns.archiveEnable": "Publish to public archive", "campaigns.archiveHelp": "Publish (running, paused, finished) the campaign message on the public archive.", "campaigns.archiveMeta": "Campaign metadata", "campaigns.archiveMetaHelp": "Dummy subscriber data to use in the public message including name, email, and any optional attributes used in the campaign message or template.", "campaigns.cantUpdate": "<PERSON>em lehet frissíteni a futó vagy a befejezett kampányt.", "campaigns.clicks": "Kat<PERSON>tás<PERSON>", "campaigns.confirmDelete": "<PERSON><PERSON><PERSON><PERSON> {name}", "campaigns.confirmSchedule": "Ez a kampány automatikusan elindul az ütemezett napon és időpontban. Ütemezés most?", "campaigns.confirmSwitchFormat": "A tartalom elveszítheti a formázást. Biztos folytatja?", "campaigns.content": "Tartalom", "campaigns.contentHelp": "Tartalom itt", "campaigns.continue": "Folytatás", "campaigns.copyOf": "Másolata a {name}", "campaigns.customHeadersHelp": "Array of custom headers to attach to outgoing messages. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Dátum és Idő", "campaigns.ended": "Befejezett", "campaigns.errorSendTest": "Hiba a teszt küldésekor: {error}", "campaigns.fieldInvalidBody": "Hiba a kampánytörzs összeállításakor: {error}", "campaigns.fieldInvalidFromEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `from_email`.", "campaigns.fieldInvalidListIDs": "Érvénytelen lista IDs.", "campaigns.fieldInvalidMessenger": "Ismeretlen üzenet küldő {name}.", "campaigns.fieldInvalidName": "A név hossza érvénytelen.", "campaigns.fieldInvalidSendAt": "A tervezett dátumnak a jövőben kell lennie.", "campaigns.fieldInvalidSubject": "A tárgy hossza érvénytelen.", "campaigns.formatHTML": "HTML formátum", "campaigns.fromAddress": "Címről", "campaigns.fromAddressPlaceholder": "A neved <<EMAIL>>", "campaigns.invalid": "<PERSON>rv<PERSON><PERSON><PERSON>", "campaigns.invalidCustomHeaders": "Invalid custom headers: {error}", "campaigns.markdown": "Csökkentés", "campaigns.needsSendAt": "A kampányhoz dátumot kell be<PERSON>.", "campaigns.newCampaign": "<PERSON><PERSON>", "campaigns.noKnownSubsToTest": "Nincsenek tesztelhető feliratkozók.", "campaigns.noOptinLists": "Nem találhatók feliratkozási listák a kampány létrehozásához.", "campaigns.noSubs": "Nincsenek feliratkozók a kiválasztott listákon a kampány létrehozásához.", "campaigns.noSubsToTest": "<PERSON><PERSON><PERSON>ek megcélzandó feliratkozók.", "campaigns.notFound": "A kampány nem található.", "campaigns.onlyActiveCancel": "Csak az aktív kampányok törölhetők.", "campaigns.onlyActivePause": "Csak az aktív kampányok szünetelhetők.", "campaigns.onlyDraftAsScheduled": "Csak kampányvázlatok ütemezhetők.", "campaigns.onlyPausedDraft": "Csak a szüneteltetett kampányok és piszkozatok indíthatók el.", "campaigns.onlyScheduledAsDraft": "Csak az ütemezett kampányok menthetők piszkozatként.", "campaigns.pause": "Szünet", "campaigns.plainText": "Egyszerű szöveg", "campaigns.preview": "Előnézet", "campaigns.progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.queryPlaceholder": "Név vagy t<PERSON>", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "<PERSON><PERSON> (Raw) HTML", "campaigns.removeAltText": "Alternatív egyszerű szöveges üzenet eltávolítása", "campaigns.richText": "Rich text", "campaigns.schedule": "Kampány ütemezése", "campaigns.scheduled": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.send": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.sendLater": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.sendTest": "Teszt üzenet küldése", "campaigns.sendTestHelp": "Egy cím beírása után nyomja meg az Enter bill<PERSON>űt több címzett hozzáadásához. A címeknek a meglévő előfizetőkhöz kell tartozniuk.", "campaigns.sendToLists": "Listák a küldéshez", "campaigns.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.start": "Indítsa el a kampányt", "campaigns.started": "\"{name}\" elindult", "campaigns.startedAt": "Elindult", "campaigns.stats": "Statisztika", "campaigns.status.cancelled": "Törölve", "campaigns.status.draft": "Piszkozat", "campaigns.status.finished": "Befejezett", "campaigns.status.paused": "Szüneteltetve", "campaigns.status.running": "<PERSON><PERSON><PERSON>", "campaigns.status.scheduled": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.statusChanged": "\"{name}\" van {status}", "campaigns.subject": "<PERSON><PERSON><PERSON>", "campaigns.testDisabled": "Enter password to test", "campaigns.testEmails": "E-mail", "campaigns.testSent": "Tesztüzenet elküldve", "campaigns.timestamps": "<PERSON>dőbélyegek", "campaigns.trackLink": "Nyomonkövetési link", "campaigns.views": "Nézetek", "dashboard.campaignViews": "Kampánynézetek", "dashboard.linkClicks": "Linkkattintások", "dashboard.messagesSent": "Üzenetek elküldve", "dashboard.orphanSubs": "<PERSON><PERSON><PERSON><PERSON>", "email.data.info": "Az Önről rögzített összes adat másolata JSON formátumú fájlként csatolva van. Szövegszerkesztőben megtekinthető.", "email.data.title": "<PERSON><PERSON> <PERSON><PERSON>", "email.optin.confirmSub": "Erősítse meg a feliratkozást", "email.optin.confirmSubHelp": "Erősítse meg feliratkozását az alábbi gombra kattint<PERSON>.", "email.optin.confirmSubInfo": "Ön felkerült a következő listákra:", "email.optin.confirmSubTitle": "Erősítse meg a feliratkozást", "email.optin.confirmSubWelcome": "Hello", "email.optin.privateList": "Privát lista", "email.status.campaignReason": "Ok", "email.status.campaignSent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email.status.campaignUpdateTitle": "Kampán<PERSON>", "email.status.importFile": "File", "email.status.importRecords": "Recordok", "email.status.importTitle": "Frissítés importálása", "email.status.status": "<PERSON><PERSON><PERSON><PERSON>", "email.unsub": "Leiratkozás", "email.unsubHelp": "<PERSON><PERSON> s<PERSON> email-eket kapni?", "email.viewInBrowser": "View in browser", "forms.formHTML": "HTML űrlap", "forms.formHTMLHelp": "A következő HTML használatával megjelenítheti az feliratkozási űrlapot egy külső weboldalon. Az űrlapnak tartalmaznia kell az e-mail mezőt és egy vagy több `l` (list UUID) mezőt. A név mező nem kötelező .", "forms.noPublicLists": "Nincsenek nyilvános listák az űrlapok létrehozásához.", "forms.publicLists": "Publikus lista", "forms.publicSubPage": "Nyilvános feliratkozási oldal", "forms.selectHelp": "Válassza ki az űrlaphoz hozzáadandó listákat.", "forms.title": "Űrlapok", "globals.buttons.add": "Hozzáadás", "globals.buttons.addNew": "<PERSON><PERSON>", "globals.buttons.back": "<PERSON><PERSON><PERSON>", "globals.buttons.cancel": "Törlés", "globals.buttons.clone": "Klonozás", "globals.buttons.close": "Kilépés", "globals.buttons.continue": "Folytatás", "globals.buttons.delete": "Törlés", "globals.buttons.deleteAll": "Összes Törlése", "globals.buttons.edit": "Szerkesztés", "globals.buttons.enabled": "E<PERSON><PERSON><PERSON>ezés", "globals.buttons.insert": "Insert", "globals.buttons.learnMore": "<PERSON><PERSON><PERSON> meg többet", "globals.buttons.more": "More", "globals.buttons.new": "<PERSON><PERSON>", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Eltávolítás", "globals.buttons.save": "Men<PERSON>s", "globals.buttons.saveChanges": "Válltozások mentése", "globals.days.0": "Vas<PERSON>rna<PERSON>", "globals.days.1": "Hétfő", "globals.days.2": "<PERSON><PERSON>", "globals.days.3": "Szerda", "globals.days.4": "Csütörtök", "globals.days.5": "Péntek", "globals.days.6": "Szombat", "globals.days.7": "Sat", "globals.fields.createdAt": "Létrehozva", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Név", "globals.fields.status": "<PERSON><PERSON><PERSON><PERSON>", "globals.fields.type": "<PERSON><PERSON><PERSON>", "globals.fields.updatedAt": "Frissítve", "globals.fields.uuid": "UUID", "globals.messages.confirm": "biztos vagy ebben?", "globals.messages.confirmDiscard": "Módosítások elvetése?", "globals.messages.created": "\"{name}\" k<PERSON><PERSON><PERSON><PERSON>tte", "globals.messages.deleted": "\"{name}\" t<PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.messages.deletedCount": "{name} ({num}) törölve", "globals.messages.done": "Done", "globals.messages.emptyState": "<PERSON><PERSON>i sincs itt", "globals.messages.errorCreating": "Hiba a létrehozásnál {name}: {error}", "globals.messages.errorDeleting": "Hiba a törléskor {name}: {error}", "globals.messages.errorFetching": "Hiba a lekérés során {name}: {error}", "globals.messages.errorInvalidIDs": "<PERSON><PERSON> vagy több azonosító érvénytelen: {error}", "globals.messages.errorUUID": "<PERSON>ba a generá<PERSON>ás során UUID: {error}", "globals.messages.errorUpdating": "Hiba a frissítés során {name}: {error}", "globals.messages.internalError": "Belső Szerverhiba", "globals.messages.invalidData": "Érvénytelen adat", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Érvénytelen ID(s)", "globals.messages.invalidUUID": "Érvénytelen UUID(s)", "globals.messages.missingFields": "<PERSON><PERSON><PERSON><PERSON><PERSON>(k): {name}", "globals.messages.notFound": "{name} nem ta<PERSON>", "globals.messages.passwordChange": "Adja meg a módosítani kívánt értéket", "globals.messages.updated": "\"{name}\" frissítve", "globals.months.1": "Jan", "globals.months.10": "Oct", "globals.months.11": "Nov", "globals.months.12": "Dec", "globals.months.2": "Feb", "globals.months.3": "Mar", "globals.months.4": "Apr", "globals.months.5": "May", "globals.months.6": "Jun", "globals.months.7": "Jul", "globals.months.8": "Aug", "globals.months.9": "Sep", "globals.states.off": "Off", "globals.terms.all": "All", "globals.terms.analytics": "<PERSON><PERSON><PERSON>", "globals.terms.bounce": "Visszapattanó | Visszapattanók", "globals.terms.bounces": "Visszapattanók", "globals.terms.campaign": "Kampány | Kampányok", "globals.terms.campaigns": "Kampányok", "globals.terms.dashboard": "Irányítópult", "globals.terms.day": "Day | Days", "globals.terms.hour": "Hour | Hours", "globals.terms.list": "Lista | Listák", "globals.terms.lists": "Listák", "globals.terms.media": "Media | Media", "globals.terms.messenger": "Messenger | Messengers", "globals.terms.messengers": "Messengers", "globals.terms.minute": "Minute | Minutes", "globals.terms.month": "Month | Months", "globals.terms.second": "Second | Seconds", "globals.terms.settings": "Beállítások", "globals.terms.subscriber": "Feliratkozó | Feliratkozók", "globals.terms.subscribers": "Feliratkozók", "globals.terms.subscriptions": "Subscription | Subscriptions", "globals.terms.tag": "Címke | Címkék", "globals.terms.tags": "Címkék", "globals.terms.template": "Sablon | Sablonok", "globals.terms.templates": "Sablonok", "globals.terms.tx": "Transactional | Transactional", "globals.terms.year": "Year | Years", "import.alreadyRunning": "<PERSON><PERSON>r fut az importálás. <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON>, v<PERSON><PERSON>, mi<PERSON><PERSON><PERSON> újra próbálkozna.", "import.blocklist": "Tiltólista", "import.csvDelim": "CSV határoló", "import.csvDelimHelp": "<PERSON>z alapértelmezett határoló a vessző.", "import.csvExample": "Példa nyers CSV", "import.csvFile": "CSV vagy ZIP file", "import.csvFileHelp": "<PERSON><PERSON>tson vagy húzza ide a CSV- vagy ZIP-fájlt", "import.errorCopyingFile": "<PERSON>ba a fájl másolásakor : {error}", "import.errorProcessingZIP": "Hiba a ZIP-fájl feldolgozása során : {error}", "import.errorStarting": "Hiba az importálás indításakor : {error}", "import.importDone": "<PERSON><PERSON><PERSON>", "import.importStarted": "Az importálás megkezdődöt", "import.instructions": "Utasítás", "import.instructionsHelp": "Töltsön fel egy CSV-fájlt vagy egy ZIP-fájlt egyetlen CSV-fájllal a tömeges importálásra feliratkozók számára. A CSV-fájlnak a következő fejlécekkel kell rendelkeznie a pontos oszlopnevekkel. attribútumoknak (nem kötelező) érvényes JSON-karakterláncnak kell lenniük kettős megtisztított idézőjelekkel.", "import.invalidDelim": "A határolónak egyetlen karakterből kell állnia.", "import.invalidFile": "Érvénytelen file: {error}", "import.invalidMode": "Érvénytelen mode", "import.invalidParams": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paramé<PERSON>: {error}", "import.invalidSubStatus": "Érvén<PERSON>len f<PERSON><PERSON>", "import.listSubHelp": "Feliratkozási listák.", "import.mode": "<PERSON><PERSON><PERSON>", "import.overwrite": "Á<PERSON><PERSON><PERSON>?", "import.overwriteHelp": "A meglévő előfizetők nevének, attribútumainak és előfizetési állapotának felülírása?", "import.recordsCount": "{num} / {total} rekordok", "import.stopImport": "Importá<PERSON><PERSON> le<PERSON>llí<PERSON>", "import.subscribe": "Iratkozz fel", "import.title": "Feliratkozók importálása", "import.upload": "Feltöltés", "lists.confirmDelete": "biztos vagy ebben? Ez nem törli a feliratkozókat.", "lists.confirmSub": "Feliratkozók megerősítése(s) a {name}", "lists.invalidName": "Érvénytelen név", "lists.newList": "<PERSON><PERSON> lista", "lists.optin": "Felirat<PERSON>z<PERSON>", "lists.optinHelp": "A feliratkozás email-t küld a feliratkozóknak amelyben megerősítést kér. A dupla feliratkozási listákon a kampányok csak a jóváhagyott feliratkozók kerülnek elküldésre.", "lists.optinTo": "f<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "lists.optins.double": "<PERSON><PERSON><PERSON>", "lists.optins.single": "<PERSON><PERSON><PERSON><PERSON>", "lists.sendCampaign": "Kampány k<PERSON>e", "lists.sendOptinCampaign": "Feliratkozási kampány küldése", "lists.type": "<PERSON><PERSON><PERSON>", "lists.typeHelp": "A nyilvános listákra mindenki felirat<PERSON>zhat, és nevük megjelenhet nyilvános oldalakon, például az előfizetés-kez<PERSON>ő oldalon.", "lists.types.private": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lists.types.public": "N<PERSON>lván<PERSON>", "logs.title": "Logok", "maintenance.help": "Some actions may take a while to complete depending on the amount of data.", "maintenance.maintenance.unconfirmedOptins": "Unconfirmed opt-in subscriptions", "maintenance.olderThan": "Older than", "maintenance.title": "Maintenance", "maintenance.unconfirmedSubs": "Unconfirmed subscriptions older than {name} days.", "media.errorReadingFile": "Hiba a fájl olvasásakor : {error}", "media.errorResizing": "Hiba a kép átméretezésekor : {error}", "media.errorSavingThumbnail": "Hiba az indexkép mentésekor : {error}", "media.errorUploading": "<PERSON>ba a fájl feltöltésekor : {error}", "media.invalidFile": "Érvénytelen fájl : {error}", "media.title": "Média", "media.unsupportedFileType": "<PERSON><PERSON> fájl tí<PERSON>  ({type})", "media.upload": "Feltöltés", "media.uploadHelp": "<PERSON><PERSON><PERSON><PERSON> vagy h<PERSON>n ide egy vagy több képet", "media.uploadImage": "<PERSON><PERSON><PERSON>", "menu.allCampaigns": "<PERSON><PERSON> ka<PERSON>y", "menu.allLists": "Minden lista", "menu.allSubscribers": "<PERSON><PERSON> f<PERSON>", "menu.dashboard": "Irányítópult", "menu.forms": "Űrlapok", "menu.import": "Importálás", "menu.logs": "Logok", "menu.maintenance": "Maintenance", "menu.media": "Média", "menu.newCampaign": "<PERSON>j k<PERSON>z<PERSON>", "menu.settings": "Beállítások", "public.archiveEmpty": "No archived messages yet.", "public.archiveTitle": "Mailing list archive", "public.blocklisted": "Permanently unsubscribed.", "public.campaignNotFound": "Az e-mail üzenet nem található.", "public.confirmOptinSubTitle": "Feliratkozás megerősítése", "public.confirmSub": "Feliratkozás megerősítése", "public.confirmSubInfo": "Ön felkerült a következő listákra:", "public.confirmSubTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "public.dataRemoved": "Feliratkozását és az összes kapcsolódó adatot eltávolítottuk.", "public.dataRemovedTitle": "<PERSON><PERSON> tö<PERSON>ö<PERSON>", "public.dataSent": "Adatait mellékletként e-mailben elküldtük.", "public.dataSentTitle": "Adatok e-mailben", "public.errorFetchingCampaign": "Hiba az e-mail üzenet lekérésekor.", "public.errorFetchingEmail": "Az e-mail üzenet nem található", "public.errorFetchingLists": "Hiba a listák lekérésekor. K<PERSON><PERSON><PERSON><PERSON><PERSON>, pr<PERSON><PERSON><PERSON><PERSON><PERSON>.", "public.errorProcessingRequest": "Hiba a kérelem feldolgozásakor. Kérjük, próbálja <PERSON>.", "public.errorTitle": "Hiba", "public.invalidFeature": "Ez a funkció nem elérhető.", "public.invalidLink": "A link érvénytelen", "public.managePrefs": "Manage preferences", "public.managePrefsUnsub": "Uncheck lists to unsubscribe from them.", "public.noListsAvailable": "<PERSON>ncsenek feliratkozható listák.", "public.noListsSelected": "Nincsenek érvényes listák az feliratkozókhoz.", "public.noSubInfo": "Nincsenek megerősítendő feliratkozások .", "public.noSubTitle": "<PERSON><PERSON><PERSON>", "public.notFoundTitle": "<PERSON><PERSON>", "public.prefsSaved": "Your preferences have been saved.", "public.privacyConfirmWipe": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy végleg törölni szeretné az összes feliratkozási adatot?", "public.privacyExport": "Export<PERSON><PERSON><PERSON> ad<PERSON>", "public.privacyExportHelp": "Az adatok másolatát e-mailben küldjük el.", "public.privacyTitle": "Adatvédelem és adatok", "public.privacyWipe": "Törölje az adatait", "public.privacyWipeHelp": "Véglegesen törölje az összes feliratkozást és a kapcsolódó adatokat az adatbázisból.", "public.sub": "Felirat<PERSON>z<PERSON>", "public.subConfirmed": "<PERSON><PERSON><PERSON> feli<PERSON>.", "public.subConfirmedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tt", "public.subName": "Név (optional)", "public.subNotFound": "Feliratkozás nem található.", "public.subOptinPending": "A feliratkozás megerősítése érdekében e-mailt küldtünk Önnek.", "public.subPrivateList": "Privát lista", "public.subTitle": "Felirat<PERSON>z<PERSON>", "public.unsub": "Leiratkozás", "public.unsubFull": "Leiratkozhat minden jövőbeni e-mailről is.", "public.unsubHelp": "Le szeretne iratkozni erről a levelezőlistáról?", "public.unsubTitle": "Leiratkozás", "public.unsubbedInfo": "<PERSON><PERSON><PERSON><PERSON>.", "public.unsubbedTitle": "Leiratkozott", "public.unsubscribeTitle": "Leiratkozás a levelezőlistáról", "settings.appearance.adminHelp": "Custom CSS to apply to the admin UI.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "Custom CSS", "settings.appearance.customJS": "Custom JavaScript", "settings.appearance.name": "Appearance", "settings.appearance.publicHelp": "Custom CSS and JavaScript to apply to the public pages.", "settings.appearance.publicName": "Public", "settings.bounces.action": "Action", "settings.bounces.blocklist": "Tiltólista", "settings.bounces.count": "Visszapattanások száma", "settings.bounces.countHelp": "Visszapattanások száma előfizetőnként", "settings.bounces.delete": "T<PERSON>r<PERSON><PERSON>", "settings.bounces.enable": "Visszapattanási feldolgozás engedélyezése", "settings.bounces.enableMailbox": "Visszapattanó postafiók engedélyezése", "settings.bounces.enableSES": "SES engedélyezése", "settings.bounces.enableSendgrid": "A SendGrid engedélyezése", "settings.bounces.enableWebhooks": "Visszapattanó webhook engedélyezése", "settings.bounces.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.bounces.folder": "Mappa", "settings.bounces.folderHelp": "A vizsgálandó IMAP mappa neve. Pl.: Inbox.", "settings.bounces.invalidScanInterval": "A visszapattanási szkennelés intervallumának legalább 1 percnek kell lennie.", "settings.bounces.name": "Visszapattanás", "settings.bounces.scanInterval": "Szkennelési intervallum", "settings.bounces.scanIntervalHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON> bel<PERSON> a v<PERSON>zapattanó postafiókot kell vizsgálni (s a másodperc, m a perc).", "settings.bounces.sendgridKey": "<PERSON><PERSON><PERSON>", "settings.bounces.type": "<PERSON><PERSON><PERSON>", "settings.bounces.username": "Felhasználó név", "settings.confirmRestart": "Győződjön meg arró<PERSON>, hogy a futó kampányok szünetelnek. Újrakezd ?", "settings.duplicateMessengerName": "Ismétlődő üzenetküldő név : {name}", "settings.errorEncoding": "Hiba a kódolási beállításoknál : {error}", "settings.errorNoSMTP": "Legalább egy SMTP blokkot engedélyezni kell ", "settings.general.adminNotifEmails": "Adminisztrátori értesítő e-mailek ", "settings.general.adminNotifEmailsHelp": "Azon e-mail címek vesszővel elválasztott listája, amelyekre az adminisztrátori értesítéseket kell küldeni, péld<PERSON>ul az importálási frissítésekről, a kampány befejezéséről, a sikertelenségről stb.", "settings.general.checkUpdates": "Frissítések keresése ", "settings.general.checkUpdatesHelp": "Rendszeresen ellenőrizze az új alkalmazáskiadásokat, és értesítéseket.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publish campaigns on which archiving is enabled on the public website.", "settings.general.enablePublicSubPage": "Nyilvános feliratkozási oldal engedélyezése ", "settings.general.enablePublicSubPageHelp": "Nyilvános feliratkozási oldal megjelenítése az összes nyilvános listával a feliratkozáshoz.", "settings.general.faviconURL": "Favicon URL", "settings.general.faviconURLHelp": "(Optional) a statikus favicon teljes URL-je, amely megjelen<PERSON> a felhasználó nézetén, például a leiratkozási oldalon.", "settings.general.fromEmail": "Alap<PERSON><PERSON><PERSON><PERSON>ett  `feladó` email", "settings.general.fromEmailHelp": "Alapértelmezett `felad<PERSON>` hogy megjelenjen a kampány kimenő e-mailjein. Ez kampányonként módosítható.", "settings.general.language": "Nyelv", "settings.general.logoURL": "Logo URL", "settings.general.logoURLHelp": "(Optional) a statikus logó teljes URL-je, amely megjelen<PERSON> a felhasználó nézetén, például a leiratkozási oldalon.", "settings.general.name": "General", "settings.general.rootURL": "Root URL", "settings.general.rootURLHelp": "Public URL of the installation (no trailing slash).", "settings.general.sendOptinConfirm": "Jelentkezési visszaigazolás küldése ", "settings.general.sendOptinConfirmHelp": "Feliratkozást megerősítő e-mail küldése, amikor az előfizetők a nyilvános űrlapon keresztül regisztr<PERSON>lnak, vagy amikor az adminisztrátor hozz<PERSON>adja ő<PERSON>.", "settings.general.siteName": "Site name", "settings.invalidMessengerName": "Érvénytelen üzenetküldő név.", "settings.mailserver.authProtocol": "<PERSON>th <PERSON><PERSON>", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "SMTP szerver címe.", "settings.mailserver.idleTimeout": "Tétlenségi idő<PERSON>úllépés", "settings.mailserver.idleTimeoutHelp": "Ideje várni az új tevékenységre a kap<PERSON>olaton, miel<PERSON>tt bezárná és eltávolítaná a készletből (s másodperc, m perc).", "settings.mailserver.maxConns": "<PERSON><PERSON>", "settings.mailserver.maxConnsHelp": "<PERSON><PERSON><PERSON> ka<PERSON>t a szerverrel.", "settings.mailserver.password": "Je<PERSON><PERSON><PERSON>", "settings.mailserver.passwordHelp": "<PERSON><PERSON><PERSON> be a módosításhoz", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "SMTP server's port.", "settings.mailserver.skipTLS": "Skip TLS verification", "settings.mailserver.skipTLSHelp": "Skip hostname check on the TLS certificate.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Enable STARTTLS.", "settings.mailserver.username": "Username", "settings.mailserver.waitTimeout": "<PERSON><PERSON><PERSON><PERSON>", "settings.mailserver.waitTimeoutHelp": "Ideje várni az új tevékenységre a kap<PERSON>olaton, miel<PERSON>tt bezárná és eltávolítaná a készletből (s másodperc, m perc).", "settings.media.provider": "S<PERSON>lgáltató ", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "Bucket path", "settings.media.s3.bucketPathHelp": "Path inside the bucket to upload files. Default is /", "settings.media.s3.bucketType": "Bucket type", "settings.media.s3.bucketTypePrivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.media.s3.bucketTypePublic": "N<PERSON>lván<PERSON>", "settings.media.s3.key": "AWS access key", "settings.media.s3.publicURL": "Custom public URL (optional)", "settings.media.s3.publicURLHelp": "Custom S3 domain to use for image links instead of the default S3 backend URL.", "settings.media.s3.region": "<PERSON><PERSON><PERSON><PERSON>", "settings.media.s3.secret": "AWS access secret", "settings.media.s3.uploadExpiry": "Feltöltés lejárata", "settings.media.s3.uploadExpiryHelp": "(Optional) Adja meg a TTL-t (másodpercben) a generált előre aláírt URL-hez. Csak privát gyűjtőhelyek<PERSON> von<PERSON>zik (s, m, h, d másodperc, perc, óra, nap).", "settings.media.s3.url": "S3 backend URL", "settings.media.s3.urlHelp": "Csak akkor <PERSON>, ha egyéni S3-kompatibilis hátteret használ, mint például a Minio.", "settings.media.title": "Médiafeltöltések", "settings.media.upload.path": "Feltöltési útvonal", "settings.media.upload.pathHelp": "Útvonal ahhoz a könyvtárhoz, ah<PERSON> a média feltöltődik.", "settings.media.upload.uri": "Feltöltési URI", "settings.media.upload.uriHelp": "Töltse fel a külvilág számára látható URI-t. Az upload_path cí<PERSON><PERSON> felt<PERSON>lt<PERSON>tt média nyilvánosan elérhető lesz például a {root_url} alatt , https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "<PERSON><PERSON> ", "settings.messengers.maxConnsHelp": "<PERSON><PERSON><PERSON> ka<PERSON>t a szerverrel .", "settings.messengers.messageSaved": "Beállítások elmentve. Alkalmazás újratöltése.", "settings.messengers.name": "Messengers", "settings.messengers.nameHelp": "eg: my-sms. Alphanumeric / dash.", "settings.messengers.password": "Je<PERSON><PERSON><PERSON>", "settings.messengers.retries": "Újrapróbálkozások", "settings.messengers.retriesHelp": "<PERSON>z újrapróbálkozások száma, ha az üzenet sikertelen.", "settings.messengers.skipTLSHelp": "A gazdagépnév ellenőrzésének kihagyása a TLS-tanúsítványon.", "settings.messengers.timeout": "Tétlenségi idő<PERSON>úllépés", "settings.messengers.timeoutHelp": "Ideje várni az új tevékenységre a kap<PERSON>olaton, miel<PERSON>tt bezárná és eltávolítaná a készletből (s másodperc, m perc).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "A visszaküldési szerver gyökér URL-je.", "settings.messengers.username": "Felhsználó név", "settings.needsRestart": "A beállítások megváltoztak. Szüntesse meg az összes futó ka<PERSON>, és indítsa újra az alkalmazást", "settings.performance.batchSize": "<PERSON><PERSON> méret", "settings.performance.batchSizeHelp": "Az adatbázisból egyetlen iteráció során le<PERSON>ívandó feliratkozók száma. Minden iteráció előfizetőket von ki az adatbázisból, üzeneteket küld nekik, majd továbblép a következő iterációra a következő köteg lehívásához. Ennek ideális esetben nagyobbnak kell lennie, mint a maximálisan elérhető átviteli sebesség (egyidejűség * üzenet_sebesség).", "settings.performance.concurrency": "Egyidejűség", "settings.performance.concurrencyHelp": "Maximum egyidejű dolgozó (szálak), amely egyidejűleg próbál meg üzeneteket küldeni.", "settings.performance.maxErrThreshold": "<PERSON><PERSON><PERSON>", "settings.performance.maxErrThresholdHelp": "A futó kampánynak eltűrhető hibák (pl. SMTP időtúllépések e-mailezés közben) száma, miel<PERSON>tt manuális vizsgálat vagy beavatkozás miatt szünetelne. Állítsa 0-ra, hogy soha ne szüneteljen.", "settings.performance.messageRate": "Üzenetek aránya ", "settings.performance.messageRateHelp": "A másodpercenként kiküldhető üzenetek maximális száma munkavállalónként egy másodperc alatt. Ha az egyidejűség = 10 és a message_rate = 10, akkor másodpercenként legfeljebb 10x10=100 üzenet kerülhet ki. Ezt a párhuzamossággal együtt úgy kell módosítani, hogy a nettó üzenetek másodpercenként ne haladják meg a cél üzenetszerverek sebességi határait, ha vannak ilyenek.", "settings.performance.name": "Teljesítmény", "settings.performance.slidingWindow": "Csúszóablak korlátozás engedélyezése", "settings.performance.slidingWindowDuration": "Időtar<PERSON>", "settings.performance.slidingWindowDurationHelp": "A tolóablak időtartama (m a perc, h az óra).", "settings.performance.slidingWindowHelp": "Korlátozza az adott időszakban kiküldött üzenetek számát. Ennek a korlátnak az elérésekor az üzeneteket a rendszer a küldéstől az időablak kiürüléséig visszatartja.", "settings.performance.slidingWindowRate": "<PERSON><PERSON>", "settings.performance.slidingWindowRateHelp": "Az ablak időtartamán belül elküldhető üzenetek maximális száma.", "settings.privacy.allowBlocklist": "Blokklistázás engedélyezése", "settings.privacy.allowBlocklistHelp": "Engedélyezi a feliratkozóknak, hogy leiratkozzanak az összes levelezőlistáról, és tiltólistán jelöljék meg magukat?", "settings.privacy.allowExport": "Exportálás engedélyezése", "settings.privacy.allowExportHelp": "Engedélyezze az előfizetőknek a róluk gyűjtött adatok exportálását?", "settings.privacy.allowPrefs": "Allow preference changes", "settings.privacy.allowPrefsHelp": "Allow subscribers to change preferences such as their names and multiple list subscriptions.", "settings.privacy.allowWipe": "Törlés engedélyezése", "settings.privacy.allowWipeHelp": "Lehetővé teszi az felirat<PERSON>z<PERSON>nak, hogy töröljék magukat az adatbázisból, beleértve az feliratkozásaikat és az összes többi adatot. A kampánynézeteket és a linkekre leadott kattintásokat szintén eltávolítjuk, miközben a megtekintések és kattintások száma megmarad (nem társított feliratkozókkal), így a statisztikák és az elemzések nem érintik.", "settings.privacy.domainBlocklist": "Domain tiltólista", "settings.privacy.domainBlocklistHelp": "Az ilyen domainekkel rendelkező e-mail címekre nem lehet feliratkozni. Soronként egy domaint adjon meg, pl.: somesite.com", "settings.privacy.individualSubTracking": "Egyéni feliratkozók követése", "settings.privacy.individualSubTrackingHelp": "Kövesse nyomon az feliratkozói szintű kampánymegtekintéseket és kattintásokat. <PERSON> le van tilt<PERSON>, a megtekintés és a kattintás követése továbbra is az egyes feliratkozókhoz való kapcsolódás nélkül folytatódik.", "settings.privacy.listUnsubHeader": "Tartalmazza a `List-Unsubscribe` fejlécet", "settings.privacy.listUnsubHeaderHelp": "Tartalmazzon leiratkozási fejléceket, amelyek le<PERSON>ővé teszik az e-mail kliensek számára, hogy a felhasználók egyetlen kattintással leiratkozhassanak.", "settings.privacy.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.restart": "Újraindítás", "settings.smtp.customHeaders": "<PERSON><PERSON><PERSON><PERSON>", "settings.smtp.customHeadersHelp": "Az e-mail fejlécek opcionális tömbje a szerverről küldött összes üzenetben.  eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.smtp.heloHost": "HELO hostname", "settings.smtp.heloHostHelp": "Választható. Egyes SMTP-kiszolgálók FQDN-t igényelnek a gazdagépnévben. Alapértelmezés szerint a HELLO-k a \"localhost\"-tal együtt járnak. Állítsa be, ha egyéni gazdagépnevet kíván használni.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Újrapróbálkozások", "settings.smtp.retriesHelp": "<PERSON>z újrapróbálkozások száma, ha az üzenet sikertelen.", "settings.smtp.sendTest": "Send e-mail", "settings.smtp.setCustomHeaders": "Egyéni fejlécek beállítása", "settings.smtp.testConnection": "Test connection", "settings.smtp.testEnterEmail": "Enter password to test", "settings.smtp.toEmail": "To e-mail", "settings.title": "Beállítások", "settings.updateAvailable": "<PERSON><PERSON> f<PERSON> {version} elérhető.", "subscribers.advancedQuery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribers.advancedQueryHelp": "Részleges SQL kifejezés az feliratkozói attribútumok lekérdezéséhez", "subscribers.attribs": "Attribútumok", "subscribers.attribsHelp": "Az attribútumok például JSON-leképezésként vannak definiálva:", "subscribers.blocklistedHelp": "A feketelistán szereplő feliratkozók soha nem kapnak e-mailt.", "subscribers.confirmBlocklist": "Tiltólista {num} f<PERSON>rat<PERSON><PERSON><PERSON>(k)?", "subscribers.confirmDelete": "<PERSON><PERSON><PERSON><PERSON> {num} f<PERSON><PERSON><PERSON><PERSON><PERSON>(k)?", "subscribers.confirmExport": "Exportálás {num} feliratkozó(k)?", "subscribers.domainBlocklisted": "Az e-mail domain feketelistán van.", "subscribers.downloadData": "Adatok letöltése", "subscribers.email": "E-mail", "subscribers.emailExists": "E-mail használatban van.", "subscribers.errorBlocklisting": "Hiba történt az feliratkozók letiltása során : {error}", "subscribers.errorNoIDs": "Nincsenek megadva azonosítók.", "subscribers.errorNoListsGiven": "<PERSON><PERSON><PERSON>ek listák megadva.", "subscribers.errorPreparingQuery": "Hiba az előfizetői lekérdezés előkészítésekor : {error}", "subscribers.errorSendingOptin": "Hiba történt a feliratkozási e-mail küldésekor.", "subscribers.export": "Exportálás", "subscribers.invalidAction": "Érvénytelen művel<PERSON>.", "subscribers.invalidEmail": "Érvénytelen email.", "subscribers.invalidJSON": "Érvénytelen JSON atributum.", "subscribers.invalidName": "Érvénytelen name.", "subscribers.listChangeApplied": "Listamódosítás alkalmazva.", "subscribers.lists": "Listák", "subscribers.listsHelp": "Azok a listák, amelyekről az feliratkozók maguk is leiratkoztak, nem távolíthatók el.", "subscribers.listsPlaceholder": "Feliratkozási listák", "subscribers.manageLists": "Listák k<PERSON>", "subscribers.markUnsubscribed": "Megjelö<PERSON>s <PERSON>", "subscribers.newSubscriber": "<PERSON><PERSON>", "subscribers.numSelected": "{num} feliratkozó(k) kiválasztva", "subscribers.optinSubject": "Erősítse meg az feliratkozást", "subscribers.preconfirm": "Feliratkozások megerősítése", "subscribers.preconfirmHelp": "Ne küldjön feliratkozási e-maileket, és ne j<PERSON>ölje meg az összes listára való feliratkozást 'feliratkozottként'.", "subscribers.query": "Lekérdezés", "subscribers.queryPlaceholder": "E-mail vagy név", "subscribers.reset": "Visszaállítás", "subscribers.selectAll": "Összes kijelölése {num}", "subscribers.sendOptinConfirm": "Send opt-in confirmation", "subscribers.sentOptinConfirm": "Opt-in confirmation sent", "subscribers.status.blocklisted": "Feketelistás", "subscribers.status.confirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tt", "subscribers.status.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribers.status.subscribed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribers.status.unconfirmed": "<PERSON><PERSON>", "subscribers.status.unsubscribed": "Leiratkozott", "subscribers.subscribersDeleted": "{num} f<PERSON>rat<PERSON><PERSON><PERSON>(k) törölve", "templates.cantDeleteDefault": "<PERSON>z alapértelmezett sablon nem törölhető", "templates.default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "templates.dummyName": "<PERSON><PERSON> ka<PERSON>", "templates.dummySubject": "Dummy ka<PERSON><PERSON> t<PERSON>", "templates.errorCompiling": "Hiba a sablon összeállításakor : {error}", "templates.errorRendering": "Hiba az üzenet megjelenítése közben : {error}", "templates.fieldInvalidName": "A név hossza érvénytelen.", "templates.makeDefault": "Alapértelmezett<PERSON>", "templates.newTemplate": "<PERSON><PERSON> sablon", "templates.placeholderHelp": "A {placeholder} helyőrzőnek pontosan egyszer kell megjelennie a sablonban.", "templates.preview": "Előnézet", "templates.rawHTML": "Raw HTML", "templates.subject": "Subject", "users.login": "Belépés", "users.logout": "Kijelentkezés"}