{"_.code": "en", "_.name": "English (en)", "admin.errorMarshallingConfig": "Error marshalling config: {error}", "analytics.count": "Count", "analytics.fromDate": "From", "analytics.invalidDates": "Invalid `from` or `to` dates.", "analytics.isUnique": "The counts are unique per subscriber.", "analytics.links": "Links", "analytics.nonUnique": "The counts are non-unique as individual subscriber tracking is turned off.", "analytics.title": "Analytics", "analytics.toDate": "To", "bounces.source": "Source", "bounces.unknownService": "Unknown service.", "bounces.view": "View bounces", "campaigns.addAltText": "Add alternate plain text message", "campaigns.archive": "Archive", "campaigns.archiveEnable": "Publish to public archive", "campaigns.archiveHelp": "Publish (running, paused, finished) the campaign message on the public archive.", "campaigns.archiveMeta": "Campaign metadata", "campaigns.archiveMetaHelp": "Dummy subscriber data to use in the public message including name, email, and any optional attributes used in the campaign message or template.", "campaigns.cantUpdate": "Cannot update a running or a finished campaign.", "campaigns.clicks": "<PERSON>licks", "campaigns.confirmDelete": "Delete {name}", "campaigns.confirmSchedule": "This campaign will start automatically at the scheduled date and time. Schedule now?", "campaigns.confirmSwitchFormat": "The content may lose formatting. Continue?", "campaigns.content": "Content", "campaigns.contentHelp": "Content here", "campaigns.continue": "Continue", "campaigns.copyOf": "Copy of {name}", "campaigns.customHeadersHelp": "Array of custom headers to attach to outgoing messages. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Date and time", "campaigns.ended": "Ended", "campaigns.errorSendTest": "Error sending test: {error}", "campaigns.fieldInvalidBody": "Error compiling campaign body: {error}", "campaigns.fieldInvalidFromEmail": "Invalid `from_email`.", "campaigns.fieldInvalidListIDs": "Invalid list IDs.", "campaigns.fieldInvalidMessenger": "Unknown messenger {name}.", "campaigns.fieldInvalidName": "Invalid length for name.", "campaigns.fieldInvalidSendAt": "Scheduled date should be in the future.", "campaigns.fieldInvalidSubject": "Invalid length for subject.", "campaigns.formatHTML": "Format HTML", "campaigns.fromAddress": "From address", "campaigns.fromAddressPlaceholder": "Your Name <<EMAIL>>", "campaigns.invalid": "Invalid campaign", "campaigns.invalidCustomHeaders": "Invalid custom headers: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Campaign needs a date to be scheduled.", "campaigns.newCampaign": "New campaign", "campaigns.noKnownSubsToTest": "No known subscribers to test.", "campaigns.noOptinLists": "No opt-in lists found to create campaign.", "campaigns.noSubs": "There are no subscribers in the selected lists to create the campaign.", "campaigns.noSubsToTest": "There are no subscribers to target.", "campaigns.notFound": "Campaign not found.", "campaigns.onlyActiveCancel": "Only active campaigns can be cancelled.", "campaigns.onlyActivePause": "Only active campaigns can be paused.", "campaigns.onlyDraftAsScheduled": "Only draft campaigns can be scheduled.", "campaigns.onlyPausedDraft": "Only paused campaigns and drafts can be started.", "campaigns.onlyScheduledAsDraft": "Only scheduled campaigns can be saved as drafts.", "campaigns.pause": "Pause", "campaigns.plainText": "Plain text", "campaigns.preview": "Preview", "campaigns.progress": "Progress", "campaigns.queryPlaceholder": "Name or subject", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "Raw HTML", "campaigns.removeAltText": "Remove alternate plain text message", "campaigns.richText": "Rich text", "campaigns.schedule": "Schedule campaign", "campaigns.scheduled": "Scheduled", "campaigns.send": "Send", "campaigns.sendLater": "Send later", "campaigns.sendTest": "Send test message", "campaigns.sendTestHelp": "Hit Enter after typing an address to add multiple recipients. The addresses must belong to existing subscribers.", "campaigns.sendToLists": "Lists to send to", "campaigns.sent": "<PERSON><PERSON>", "campaigns.start": "Start campaign", "campaigns.started": "\"{name}\" started", "campaigns.startedAt": "Started", "campaigns.stats": "Stats", "campaigns.status.cancelled": "Cancelled", "campaigns.status.draft": "Draft", "campaigns.status.finished": "Finished", "campaigns.status.paused": "Paused", "campaigns.status.running": "Running", "campaigns.status.scheduled": "Scheduled", "campaigns.statusChanged": "\"{name}\" is {status}", "campaigns.subject": "Subject", "campaigns.testEmails": "E-mails", "campaigns.testSent": "Test message sent", "campaigns.timestamps": "Timestamps", "campaigns.trackLink": "Track link", "campaigns.views": "Views", "dashboard.campaignViews": "Campaign views", "dashboard.linkClicks": "Link clicks", "dashboard.messagesSent": "Messages sent", "dashboard.orphanSubs": "Or<PERSON><PERSON>", "email.data.info": "A copy of all data recorded on you is attached as a file in JSON format. It can be viewed in a text editor.", "email.data.title": "Your data", "email.optin.confirmSub": "Confirm subscription", "email.optin.confirmSubHelp": "Confirm your subscription by clicking the below button.", "email.optin.confirmSubInfo": "You have been added to the following lists:", "email.optin.confirmSubTitle": "Confirm subscription", "email.optin.confirmSubWelcome": "Hi", "email.optin.privateList": "Private list", "email.status.campaignReason": "Reason", "email.status.campaignSent": "<PERSON><PERSON>", "email.status.campaignUpdateTitle": "Campaign update", "email.status.importFile": "File", "email.status.importRecords": "Records", "email.status.importTitle": "Import update", "email.status.status": "Status", "email.unsub": "Unsubscribe", "email.unsubHelp": "Don't want to receive these e-mails?", "email.viewInBrowser": "View in browser", "forms.formHTML": "Form HTML", "forms.formHTMLHelp": "Use the following HTML to show a subscription form on an external webpage. The form should have the email field and one or more `l` (list UUID) fields. The name field is optional.", "forms.noPublicLists": "There are no public lists to generate a forms.", "forms.publicLists": "Public lists", "forms.publicSubPage": "Public subscription page", "forms.selectHelp": "Select lists to add to the form.", "forms.title": "Forms", "category.title": "Catergories", "globals.buttons.add": "Add", "globals.buttons.addNew": "Add new", "globals.buttons.back": "Back", "globals.buttons.cancel": "Cancel", "globals.buttons.clone": "<PERSON><PERSON>", "globals.buttons.close": "Close", "globals.buttons.continue": "Continue", "globals.buttons.delete": "Delete", "globals.buttons.deleteAll": "Delete all", "globals.buttons.edit": "Edit", "globals.buttons.enabled": "Enabled", "globals.buttons.insert": "Insert", "globals.buttons.learnMore": "Learn more", "globals.buttons.more": "More", "globals.buttons.new": "New", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Remove", "globals.buttons.save": "Save", "globals.buttons.saveChanges": "Save changes", "globals.days.0": "Sun", "globals.days.1": "Sun", "globals.days.2": "Mon", "globals.days.3": "<PERSON><PERSON>", "globals.days.4": "Wed", "globals.days.5": "<PERSON>hu", "globals.days.6": "<PERSON><PERSON>", "globals.days.7": "Sat", "globals.fields.createdAt": "Created", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Name", "globals.fields.fcmImage": "Notofication Image", "globals.fields.cta": "CTA", "globals.fields.status": "Status", "globals.fields.type": "Type", "globals.fields.updatedAt": "Updated", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Are you sure?", "globals.messages.confirmDiscard": "Discard changes?", "globals.messages.created": "\"{name}\" created", "globals.messages.deleted": "\"{name}\" deleted", "globals.messages.deletedCount": "{name} ({num}) deleted", "globals.messages.done": "Done", "globals.messages.emptyState": "Nothing here", "globals.messages.errorCreating": "Error creating {name}: {error}", "globals.messages.segmentMismatch": "Exlcusion and Inclusion contains same segmentId: {id}", "globals.messages.errorDeleting": "Error deleting {name}: {error}", "globals.messages.errorFetching": "Error fetching {name}: {error}", "globals.messages.errorInvalidIDs": "One or more IDs are invalid: {error}", "globals.messages.errorUUID": "Error generating UUID: {error}", "globals.messages.errorUpdating": "Error updating {name}: {error}", "globals.messages.internalError": "Internal server error", "globals.messages.invalidData": "Invalid data", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Invalid ID(s)", "globals.messages.invalidUUID": "Invalid UUID(s)", "globals.messages.missingFields": "Missing field(s): {name}", "globals.messages.notFound": "{name} not found", "globals.messages.passwordChange": "Enter a value to change", "globals.messages.updated": "\"{name}\" updated", "globals.months.1": "Jan", "globals.months.10": "Oct", "globals.months.11": "Nov", "globals.months.12": "Dec", "globals.months.2": "Feb", "globals.months.3": "Mar", "globals.months.4": "Apr", "globals.months.5": "May", "globals.months.6": "Jun", "globals.months.7": "Jul", "globals.months.8": "Aug", "globals.months.9": "Sep", "globals.states.off": "Off", "globals.terms.all": "All", "globals.terms.analytics": "Analytics", "globals.terms.bounce": "Bounce | Bounces", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Campaign | Campaigns", "globals.terms.campaigns": "Campaigns", "globals.terms.dashboard": "Dashboard", "globals.terms.day": "Day | Days", "globals.terms.hour": "Hour | Hours", "globals.terms.list": "List | Lists", "globals.terms.lists": "Segments", "globals.terms.categories": "Categories", "globals.terms.deeplinks": "DeepLinks", "globals.terms.media": "Media | Media", "globals.terms.messenger": "Messenger | Messengers", "globals.terms.messengers": "Messengers", "globals.terms.fcmRoles": "FCM Roles", "globals.terms.minute": "Minute | Minutes", "globals.terms.month": "Month | Months", "globals.terms.second": "Second | Seconds", "globals.terms.settings": "Settings", "globals.terms.subscriber": "Subscriber | Subscribers", "globals.terms.subscribers": "Members", "globals.terms.subscriptions": "Subscription | Subscriptions", "globals.terms.tag": "Tag | Tags", "globals.terms.tags": "Tags", "globals.terms.template": "Template | Templates", "globals.terms.templates": "Templates", "globals.terms.tx": "Transactional | Transactional", "globals.terms.year": "Year | Years", "import.alreadyRunning": "An import is already running. Wait for it to finish or stop it before trying again.", "import.blocklist": "Blocklist", "import.csvDelim": "CSV delimiter", "import.csvDelimHelp": "Default delimiter is comma.", "import.csvExample": "Example raw CSV", "import.csvFile": "CSV or ZIP file", "import.csvFileHelp": "Click or drag a CSV or ZIP file here", "import.errorCopyingFile": "Error copying file: {error}", "import.errorProcessingZIP": "Error processing ZIP file: {error}", "import.errorStarting": "Error starting import: {error}", "import.importDone": "Done", "import.importStarted": "Import started", "import.instructions": "Instructions", "import.instructionsHelp": "Upload a CSV file or a ZIP file with a single CSV file in it to bulk import subscribers. The CSV file should have the following headers with the exact column names. attributes (optional) should be a valid JSON string with double escaped quotes.", "import.invalidDelim": "Delimiter should be a single character.", "import.invalidFile": "Invalid file: {error}", "import.invalidMode": "Invalid mode", "import.invalidParams": "Invalid params: {error}", "import.invalidSubStatus": "Invalid subscription status", "import.listSubHelp": "Lists to subscribe to.", "import.mode": "Mode", "import.overwrite": "Overwrite?", "import.overwriteHelp": "Overwrite memberId, attribs, subscription status of existing subscribers?", "import.recordsCount": "{num} / {total} records", "import.stopImport": "Stop import", "import.subscribe": "Subscribe", "import.title": "Import subscribers", "import.upload": "Upload", "lists.confirmDelete": "Are you sure? This does not delete subscribers.", "categories.confirmDelete": "Are you sure you want to delete category?", "lists.confirmSub": "Confirm subscription(s) to {name}", "lists.invalidName": "Invalid name", "lists.newList": "New list", "lists.optin": "Opt-in", "lists.optinHelp": "Double opt-in sends an e-mail to the subscriber asking for confirmation. On Double opt-in lists, campaigns are only sent to confirmed subscribers.", "lists.optinTo": "Opt-in to {name}", "lists.optins.double": "Double opt-in", "lists.optins.single": "Single opt-in", "lists.sendCampaign": "Send campaign", "lists.sendOptinCampaign": "Send opt-in campaign", "lists.type": "Type", "lists.typeHelp": "Public lists are open to the world to subscribe and their names may appear on public pages such as the subscription management page.", "lists.types.private": "Private", "lists.types.public": "Public", "logs.title": "Logs", "maintenance.help": "Some actions may take a while to complete depending on the amount of data.", "maintenance.maintenance.unconfirmedOptins": "Unconfirmed opt-in subscriptions", "maintenance.olderThan": "Older than", "maintenance.title": "Maintenance", "maintenance.unconfirmedSubs": "Unconfirmed subscriptions older than {name} days.", "media.errorReadingFile": "Error reading file: {error}", "media.errorResizing": "Error resizing image: {error}", "media.errorSavingThumbnail": "Error saving thumbnail: {error}", "media.errorUploading": "Error uploading file: {error}", "media.invalidFile": "Invalid file: {error}", "media.title": "Media", "media.unsupportedFileType": "Unsupported file type ({type})", "media.upload": "Upload", "media.uploadHelp": "Click or drag one or more images here", "fcm.uploadHelp": "Click or drag image here", "media.uploadImage": "Upload image", "menu.allCampaigns": "All campaigns", "menu.allLists": "All segments", "menu.allSubscribers": "All members", "menu.dashboard": "Dashboard", "menu.category": "Category", "menu.forms": "Forms", "menu.import": "Import", "menu.logs": "Logs", "menu.maintenance": "Maintenance", "menu.media": "Media", "menu.newCampaign": "Create new", "menu.settings": "Settings", "public.archiveEmpty": "No archived messages yet.", "public.archiveTitle": "Mailing list archive", "public.blocklisted": "Permanently unsubscribed.", "public.campaignNotFound": "The e-mail message was not found.", "public.confirmOptinSubTitle": "Confirm subscription", "public.confirmSub": "Confirm subscription", "public.confirmSubInfo": "You have been added to the following lists:", "public.confirmSubTitle": "Confirm", "public.dataRemoved": "Your subscriptions and all associated data has been removed.", "public.dataRemovedTitle": "Data removed", "public.dataSent": "Your data has been e-mailed to you as an attachment.", "public.dataSentTitle": "Data e-mailed", "public.errorFetchingCampaign": "Error fetching e-mail message.", "public.errorFetchingEmail": "E-mail message not found", "public.errorFetchingLists": "Error fetching lists. Please retry.", "public.errorProcessingRequest": "Error processing request. Please retry.", "public.errorTitle": "Error", "public.invalidFeature": "That feature is not available.", "public.invalidLink": "Invalid link", "public.managePrefs": "Manage preferences", "public.managePrefsUnsub": "Uncheck lists to unsubscribe from them.", "public.noListsAvailable": "No lists available to subscribe.", "public.noListsSelected": "No valid lists selected to subscribe.", "public.noSubInfo": "There are no subscriptions to confirm.", "public.noSubTitle": "No subscriptions", "public.notFoundTitle": "Not found", "public.prefsSaved": "Your preferences have been saved.", "public.privacyConfirmWipe": "Are you sure you want to delete all your subscription data permanently?", "public.privacyExport": "Export your data", "public.privacyExportHelp": "A copy of your data will be e-mailed to you.", "public.privacyTitle": "Privacy and data", "public.privacyWipe": "Wipe your data", "public.privacyWipeHelp": "Delete all your subscriptions and related data permanently.", "public.sub": "Subscribe", "public.subConfirmed": "Subscribed successfully.", "public.subConfirmedTitle": "Confirmed", "public.subName": "Name (optional)", "public.subNotFound": "Subscription not found.", "public.subOptinPending": "An e-mail has been sent to you to confirm your subscription(s).", "public.subPrivateList": "Private list", "public.subTitle": "Subscribe", "public.unsub": "Unsubscribe", "public.unsubFull": "Unsubscribe from all future e-mails.", "public.unsubHelp": "Do you want to unsubscribe from this mailing list?", "public.unsubTitle": "Unsubscribe", "public.unsubbedInfo": "You have unsubscribed successfully.", "public.unsubbedTitle": "Unsubscribed", "public.unsubscribeTitle": "Unsubscribe from mailing list", "settings.appearance.adminHelp": "Custom CSS to apply to the admin UI.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "Custom CSS", "settings.appearance.customJS": "Custom JavaScript", "settings.appearance.name": "Appearance", "settings.appearance.publicHelp": "Custom CSS and JavaScript to apply to the public pages.", "settings.appearance.publicName": "Public", "settings.bounces.action": "Action", "settings.bounces.blocklist": "Blocklist", "settings.bounces.count": "Bounce count", "settings.bounces.countHelp": "Number of bounces per subscriber", "settings.bounces.delete": "Delete", "settings.bounces.enable": "Enable bounce processing", "settings.bounces.enableMailbox": "Enable bounce mailbox", "settings.bounces.enableSES": "Enable SES", "settings.bounces.enableSendgrid": "Enable SendGrid", "settings.bounces.enableWebhooks": "Enable bounce webhooks", "settings.bounces.enabled": "Enabled", "settings.bounces.folder": "Folder", "settings.bounces.folderHelp": "Name of the IMAP folder to scan. Eg: Inbox.", "settings.bounces.invalidScanInterval": "Bounce scan interval should be minimum 1 minute.", "settings.bounces.name": "<PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Scan interval", "settings.bounces.scanIntervalHelp": "Interval at which the bounce mailbox should be scanned for bounces (s for second, m for minute).", "settings.bounces.sendgridKey": "SendGrid Key", "settings.bounces.type": "Type", "settings.bounces.username": "Username", "settings.confirmRestart": "Ensure running campaigns are paused. Restart?", "settings.duplicateMessengerName": "Duplicate messenger name: {name}", "settings.errorEncoding": "Error encoding settings: {error}", "settings.errorNoSMTP": "At least one SMTP block should be enabled", "settings.general.adminNotifEmails": "Admin notification e-mails", "settings.general.adminNotifEmailsHelp": "Comma separated list of e-mail addresses to which admin notifications such as import updates, campaign completion, failure etc. should be sent.", "settings.general.checkUpdates": "Check for updates", "settings.general.checkUpdatesHelp": "Periodically check for new app releases and notify.", "settings.general.enablePublicArchive": "Enable public mailing list archive", "settings.general.enablePublicArchiveHelp": "Publish campaigns on which archiving is enabled on the public website.", "settings.general.enablePublicSubPage": "Enable public subscription page", "settings.general.enablePublicSubPageHelp": "Show a public subscription page with all the public lists for people to subscribe.", "settings.general.faviconURL": "Favicon URL", "settings.general.faviconURLHelp": "(Optional) full URL to the static favicon to be displayed on user facing view such as the unsubscription page.", "settings.general.fromEmail": "Default `from` email", "settings.general.fromEmailHelp": "Default `from` e-mail to show on outgoing campaign e-mails. This can be changed per campaign.", "settings.general.language": "Language", "settings.general.logoURL": "Logo URL", "settings.general.logoURLHelp": "(Optional) full URL to the static logo to be displayed on user facing view such as the unsubscription page.", "settings.general.name": "General", "settings.general.rootURL": "Root URL", "settings.general.rootURLHelp": "Public URL of the installation (no trailing slash).", "settings.general.sendOptinConfirm": "Send opt-in confirmation", "settings.general.sendOptinConfirmHelp": "Send an opt-in confirmation e-mail when subscribers signup via the public form or when they are added by the admin.", "settings.general.siteName": "Site name", "settings.invalidMessengerName": "Invalid messenger name.", "settings.mailserver.authProtocol": "Auth protocol", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "SMTP server's host address.", "settings.mailserver.idleTimeout": "Idle timeout", "settings.mailserver.idleTimeoutHelp": "Time to wait for new activity on a connection before closing it and removing it from the pool (s for second, m for minute).", "settings.mailserver.maxConns": "Max. connections", "settings.mailserver.maxConnsHelp": "Maximum concurrent connections to the server.", "settings.mailserver.password": "Password", "settings.mailserver.passwordHelp": "Enter to change", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "SMTP server's port.", "settings.mailserver.skipTLS": "Skip TLS verification", "settings.mailserver.skipTLSHelp": "Skip hostname check on the TLS certificate.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "TLS/SSL encryption. STARTTLS is commonly used.", "settings.mailserver.username": "Username", "settings.mailserver.waitTimeout": "Wait timeout", "settings.mailserver.waitTimeoutHelp": "Time to wait for new activity on a connection before closing it and removing it from the pool (s for second, m for minute).", "settings.media.provider": "Provider", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "Bucket path", "settings.media.s3.bucketPathHelp": "Path inside the bucket to upload files. Default is /", "settings.media.s3.bucketType": "Bucket type", "settings.media.s3.bucketTypePrivate": "Private", "settings.media.s3.bucketTypePublic": "Public", "settings.media.s3.key": "AWS access key", "settings.media.s3.publicURL": "Custom public URL (optional)", "settings.media.s3.publicURLHelp": "Custom S3 domain to use for image links instead of the default S3 backend URL.", "settings.media.s3.region": "Region", "settings.media.s3.secret": "AWS access secret", "settings.media.s3.uploadExpiry": "Upload expiry", "settings.media.s3.uploadExpiryHelp": "(Optional) Specify TTL (in seconds) for the generated presigned URL. Only applicable for private buckets (s, m, h, d for seconds, minutes, hours, days).", "settings.media.s3.url": "S3 backend URL", "settings.media.s3.urlHelp": "Only change if using a custom S3 compatible backend like Minio.", "settings.media.title": "Media uploads", "settings.media.upload.path": "Upload path", "settings.media.upload.pathHelp": "Path to the directory where media will be uploaded.", "settings.media.upload.uri": "Upload URI", "settings.media.upload.uriHelp": "Upload URI that is visible to the outside world. The media uploaded to upload_path will be publicly accessible under {root_url}, for instance, https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "Max. connections", "settings.messengers.maxConnsHelp": "Maximum concurrent connections to the server.", "settings.messengers.messageSaved": "Settings saved. Reloading app ...", "settings.messengers.name": "Messengers", "settings.messengers.nameHelp": "eg: my-sms. Alphanumeric / dash.", "settings.messengers.password": "Password", "settings.messengers.retries": "Retries", "settings.messengers.retriesHelp": "Number of times to retry when a message fails.", "settings.messengers.skipTLSHelp": "Skip hostname check on the TLS certificate.", "settings.messengers.timeout": "Idle timeout", "settings.messengers.timeoutHelp": "Time to wait for new activity on a connection before closing it and removing it from the pool (s for second, m for minute).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Root URL of the Postback server.", "settings.messengers.username": "Username", "settings.needsRestart": "Settings changed. Pause all running campaigns and restart the app", "settings.performance.batchSize": "Batch size", "settings.performance.batchSizeHelp": "The number of subscribers to pull from the database in a single iteration. Each iteration pulls subscribers from the database, sends messages to them, and then moves on to the next iteration to pull the next batch. This should ideally be higher than the maximum achievable throughput (concurrency * message_rate).", "settings.performance.concurrency": "Concurrency", "settings.performance.concurrencyHelp": "Maximum concurrent worker (threads) that will attempt to send messages simultaneously.", "settings.performance.maxErrThreshold": "Maximum error threshold", "settings.performance.maxErrThresholdHelp": "The number of errors (eg: SMTP timeouts while e-mailing) a running campaign should tolerate before it is paused for manual investigation or intervention. Set to 0 to never pause.", "settings.performance.messageRate": "Message rate", "settings.performance.messageRateHelp": "Maximum number of messages to be sent out per second per worker in a second. If concurrency = 10 and message_rate = 10, then up to 10x10=100 messages may be pushed out every second. This, along with concurrency, should be tweaked to keep the net messages going out per second under the target message servers rate limits if any.", "settings.performance.name": "Performance", "settings.performance.slidingWindow": "Enable sliding window limit", "settings.performance.slidingWindowDuration": "Duration", "settings.performance.slidingWindowDurationHelp": "Duration of the sliding window period (m for minute, h for hour).", "settings.performance.slidingWindowHelp": "Limit the total number of messages that are sent out in given period. On reaching this limit, messages are be held from sending until the time window clears.", "settings.performance.slidingWindowRate": "Max. messages", "settings.performance.slidingWindowRateHelp": "Maximum number of messages to send within the window duration.", "settings.privacy.allowBlocklist": "Allow blocklisting", "settings.privacy.allowBlocklistHelp": "Allow subscribers to unsubscribe from all mailing lists and mark themselves as blocklisted?", "settings.privacy.allowExport": "Allow exporting", "settings.privacy.allowExportHelp": "Allow subscribers to export data collected on them?", "settings.privacy.allowPrefs": "Allow preference changes", "settings.privacy.allowPrefsHelp": "Allow subscribers to change preferences such as their names and multiple list subscriptions.", "settings.privacy.allowWipe": "Allow wiping", "settings.privacy.allowWipeHelp": "Allow subscribers to delete themselves including their subscriptions and all other data from the database. Campaign views and link clicks are also removed while views and click counts remain (with no subscriber associated to them) so that stats and analytics are not affected.", "settings.privacy.domainBlocklist": "Domain blocklist", "settings.privacy.domainBlocklistHelp": "E-mail addresses with these domains are disallowed from subscribing. Enter one domain per line, eg: somesite.com", "settings.privacy.individualSubTracking": "Individual subscriber tracking", "settings.privacy.individualSubTrackingHelp": "Track subscriber-level campaign views and clicks. When disabled, view and click tracking continue without being linked to individual subscribers.", "settings.privacy.listUnsubHeader": "Include `List-Unsubscribe` header", "settings.privacy.listUnsubHeaderHelp": "Include unsubscription headers that allow e-mail clients to allow users to unsubscribe in a single click.", "settings.privacy.name": "Privacy", "settings.restart": "<PERSON><PERSON>", "settings.smtp.customHeaders": "Custom headers", "settings.smtp.customHeadersHelp": "Optional array of e-mail headers to include in all messages sent from this server. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Enabled", "settings.smtp.heloHost": "HELO hostname", "settings.smtp.heloHostHelp": "Optional. Some SMTP servers require a FQDN in the hostname. By default, HELLOs go with `localhost`. Set this if a custom hostname should be used.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Retries", "settings.smtp.retriesHelp": "Number of times to retry when a message fails.", "settings.smtp.sendTest": "Send e-mail", "settings.smtp.setCustomHeaders": "Set custom headers", "settings.smtp.testConnection": "Test connection", "settings.smtp.testEnterEmail": "Enter password to test", "settings.smtp.toEmail": "To e-mail", "settings.title": "Settings", "settings.updateAvailable": "A new update {version} is available.", "subscribers.advancedQuery": "Advanced", "subscribers.advancedQueryHelp": "Partial SQL expression to query subscriber attributes", "subscribers.attribs": "Attributes", "subscribers.attribsHelp": "Attributes are defined as a JSON map, for example:", "subscribers.blocklistedHelp": "Blocklisted subscribers will never receive any e-mails.", "subscribers.confirmBlocklist": "Blocklist {num} subscriber(s)?", "subscribers.confirmDelete": "Delete {num} subscriber(s)?", "subscribers.confirmExport": "Export {num} subscriber(s)?", "subscribers.domainBlocklisted": "The e-mail domain is blocklisted.", "subscribers.downloadData": "Download data", "subscribers.email": "E-mail", "subscribers.emailExists": "E-mail already exists.", "subscribers.exists": "Subscriber already exists.", "subscribers.errorBlocklisting": "Error blocklisting subscribers: {error}", "subscribers.errorNoIDs": "No IDs given.", "subscribers.errorNoListsGiven": "No lists given.", "subscribers.errorPreparingQuery": "Error preparing subscriber query: {error}", "subscribers.errorSendingOptin": "Error sending opt-in e-mail.", "subscribers.export": "Export", "subscribers.invalidAction": "Invalid action.", "subscribers.invalidEmail": "Invalid email.", "subscribers.invalidJSON": "Invalid JSON in attributes.", "subscribers.invalidName": "Invalid name.", "subscribers.type": "Invalid type.", "subscribers.merchantType": "Merchant", "subscribers.terminalType": "Terminal", "subscribers.listChangeApplied": "List change applied.", "subscribers.lists": "Lists", "subscribers.listsHelp": "Lists from which subscribers have unsubscribed themselves cannot be removed.", "subscribers.listsPlaceholder": "Lists to subscribe to", "subscribers.manageLists": "Manage lists", "subscribers.markUnsubscribed": "Mark as unsubscribed", "subscribers.newSubscriber": "New subscriber", "subscribers.numSelected": "{num} subscriber(s) selected", "subscribers.optinSubject": "Confirm subscription", "subscribers.preconfirm": "Preconfirm subscriptions", "subscribers.preconfirmHelp": "Don't send opt-in e-mails and mark all list subscriptions as 'subscribed'.", "subscribers.query": "Query", "subscribers.queryPlaceholder": "E-mail or name", "subscribers.reset": "Reset", "subscribers.selectAll": "Select all {num}", "subscribers.sendOptinConfirm": "Send opt-in confirmation", "subscribers.sentOptinConfirm": "Opt-in confirmation sent", "subscribers.status.blocklisted": "Blocklisted", "subscribers.status.confirmed": "Confirmed", "subscribers.status.enabled": "Enabled", "subscribers.status.subscribed": "Subscribed", "subscribers.status.unconfirmed": "Unconfirmed", "subscribers.status.unsubscribed": "Unsubscribed", "subscribers.subscribersDeleted": "{num} subscriber(s) deleted", "templates.cantDeleteDefault": "Cannot delete non-existent or default template", "templates.default": "<PERSON><PERSON><PERSON>", "templates.dummyName": "Dummy campaign", "templates.dummySubject": "Dummy campaign subject", "templates.errorCompiling": "Error compiling template: {error}", "templates.errorRendering": "Error rendering message: {error}", "templates.fieldInvalidName": "Invalid length for name.", "templates.makeDefault": "Set default", "templates.newTemplate": "New template", "templates.placeholderHelp": "The placeholder {placeholder} should appear exactly once in the template.", "templates.preview": "Preview", "templates.rawHTML": "Raw HTML", "templates.subject": "Subject", "users.login": "<PERSON><PERSON>", "users.logout": "Logout", "login.email": "Enter email", "login.password": "Enter password"}