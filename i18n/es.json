{"_.code": "es", "_.name": "Español (es)", "admin.errorMarshallingConfig": "Error al ordenar la configuración: {error}", "analytics.count": "Número", "analytics.fromDate": "<PERSON><PERSON>", "analytics.invalidDates": "La fecha `desde` o `hasta` no es válida.", "analytics.isUnique": "Los totales son por subscriptores únicos.", "analytics.links": "Enlaces", "analytics.nonUnique": "Los totales no son por subscriptores únicos ya que el rastreo individual de subscriptores está desactivado.", "analytics.title": "Analíticas", "analytics.toDate": "<PERSON><PERSON>", "bounces.source": "Fuente", "bounces.unknownService": "<PERSON><PERSON><PERSON>.", "bounces.view": "Ver rebotes", "campaigns.addAltText": "Agregar mensaje en texto plano alternativo", "campaigns.archive": "Archivo", "campaigns.archiveEnable": "Hacer el archivo público", "campaigns.archiveHelp": "Publicar los mensajes de las campañas (en marcha, pausadas y terminadas) en el archivo público.", "campaigns.archiveMeta": "Metadata de la campaña", "campaigns.archiveMetaHelp": "Información de suscripción de ejemplo (por defecto) para ser usada en el mensaje público incluido nombre, correo electrónico, o cualquier valor accesible mediante atributos `{}` opcionales tanto en el mensaje de la campaña como en la plantilla.", "campaigns.cantUpdate": "No es posible actualizar una campaña iniciada o finalizada.", "campaigns.clicks": "C<PERSON>s", "campaigns.confirmDelete": "Eliminar {name}", "campaigns.confirmSchedule": "Esta campaña iniciará automáticamente en la fecha y hora establecida. ¿Agendar ahora?", "campaigns.confirmSwitchFormat": "Este contenido podría perder el formato. ¿Continuar?", "campaigns.content": "Contenido", "campaigns.contentHelp": "Contenido aquí", "campaigns.continue": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.copyOf": "<PERSON><PERSON> de {name}", "campaigns.customHeadersHelp": "Lista de encabezados adicionales a incluir en los mensajes salientes. ej: [{\"X-Custom\": \"valor\"}, {\"X-Custom2\": \"valor\"}]", "campaigns.dateAndTime": "<PERSON><PERSON> y hora", "campaigns.ended": "Finalizado", "campaigns.errorSendTest": "Error al enviar la prueba: {error}", "campaigns.fieldInvalidBody": "Error al compilar el cuerpo de la campaña: {error}", "campaigns.fieldInvalidFromEmail": "Correo de remitente inválido.", "campaigns.fieldInvalidListIDs": "IDs de lista inválidos", "campaigns.fieldInvalidMessenger": "Mensajero desconocido {name}.", "campaigns.fieldInvalidName": "Longitud de nombre inválida", "campaigns.fieldInvalidSendAt": "La hora agendada debe ser en el futuro.", "campaigns.fieldInvalidSubject": "Long<PERSON>ud de asunto inválida", "campaigns.formatHTML": "Formato HTML", "campaigns.fromAddress": "Dirección de remitente", "campaigns.fromAddressPlaceholder": "<PERSON> <<EMAIL>>", "campaigns.invalid": "Campaña inválida", "campaigns.invalidCustomHeaders": "Error en los encabezaos edicionales: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Una campaña necesita una fecha pra ser agendada.", "campaigns.newCampaign": "Nueva campaña", "campaigns.noKnownSubsToTest": "No hay ningún subscriptor para la prueba.", "campaigns.noOptinLists": "No se encontraron listas para crear la campaña", "campaigns.noSubs": "No hay subscriptores en la lista seleccionada para poder crear la campaña", "campaigns.noSubsToTest": "No hay subscriptores para la prueba.", "campaigns.notFound": "No se encontró la camapaña.", "campaigns.onlyActiveCancel": "Solo campañas activas pueden ser canceladas.", "campaigns.onlyActivePause": "Solo campañas activas pueden ser pausadas.", "campaigns.onlyDraftAsScheduled": "Solo campañas en borrador pueden ser agendadas.", "campaigns.onlyPausedDraft": "Solo campañas en borrador pueden ser comanzadas.", "campaigns.onlyScheduledAsDraft": "Solo campañas agendadas pueden ser guardadas como borrador.", "campaigns.pause": "Pausa", "campaigns.plainText": "Texto plano", "campaigns.preview": "Vista previa", "campaigns.progress": "Progreso", "campaigns.queryPlaceholder": "Nombre o asunto", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML de origen", "campaigns.removeAltText": "Eliminar mensaje en texto plano alternativo", "campaigns.richText": "Texto con formato", "campaigns.schedule": "Agendar camp<PERSON>", "campaigns.scheduled": "Agendada", "campaigns.send": "Enviar", "campaigns.sendLater": "Enviar más tarde", "campaigns.sendTest": "Enviar mensaje de <PERSON>", "campaigns.sendTestHelp": "Presionar `Enter` después de escribir una dirección para agregar múltiples destinatarios. Las direcciones deben corresponder a subscriptores existentes.", "campaigns.sendToLists": "Listas a las que enviar", "campaigns.sent": "Enviado", "campaigns.start": "<PERSON><PERSON><PERSON> camp<PERSON>", "campaigns.started": "\"{name}\" iniciada", "campaigns.startedAt": "Fecha de inicio", "campaigns.stats": "Estadísticas", "campaigns.status.cancelled": "Cancelada", "campaigns.status.draft": "<PERSON><PERSON><PERSON>", "campaigns.status.finished": "Finalizada", "campaigns.status.paused": "Pausada", "campaigns.status.running": "En progreso", "campaigns.status.scheduled": "Agendada", "campaigns.statusChanged": "\"{name}\" está {status}", "campaigns.subject": "<PERSON><PERSON><PERSON>", "campaigns.testDisabled": "Intoduce la contraseña (password) para probarla", "campaigns.testEmails": "Correos electrónicos de prueba", "campaigns.testSent": "Mensaje de prueba enviado", "campaigns.timestamps": "Marcas de <PERSON>po", "campaigns.trackLink": "Enlace de rastreo (Track link)", "campaigns.views": "Vistas", "dashboard.campaignViews": "Vista de campaña", "dashboard.linkClicks": "Enlaces cliqueados", "dashboard.messagesSent": "Mensajes enviados", "dashboard.orphanSubs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email.data.info": "Una copia de todos sus datos recopilados está adjunta en un archivo de formato JSON. Puede ser visto en un editor de textos.", "email.data.title": "<PERSON><PERSON> datos", "email.optin.confirmSub": "Confirmar la suscripción", "email.optin.confirmSubHelp": "Para confirmar su subscripción debe hacer clic en el siguiente botón.", "email.optin.confirmSubInfo": "Ud. ha sido agregado a las siguientes listas:", "email.optin.confirmSubTitle": "Confirmar la suscripción", "email.optin.confirmSubWelcome": "<PERSON><PERSON>", "email.optin.privateList": "Lista privada", "email.status.campaignReason": "Razón", "email.status.campaignSent": "Enviada", "email.status.campaignUpdateTitle": "Actualización de campaña", "email.status.importFile": "Archivo", "email.status.importRecords": "Registros", "email.status.importTitle": "Actualización importada", "email.status.status": "Estado", "email.unsub": "Des-subscribir", "email.unsubHelp": "¿No quiere recibir estos correos electrónicos?", "email.viewInBrowser": "Ver en el navegador", "forms.formHTML": "Formulario HTML", "forms.formHTMLHelp": "Use este código HTML para mostrar el formulario de subscripción en un sitio web. El formulario debe contener el campo `email` y uno o más campos `l` (UUID de lista). El campo `name` es opcional.", "forms.noPublicLists": "No hay listas públicas para generar formularios", "forms.publicLists": "Listas públicas", "forms.publicSubPage": "Página pública de subscripción", "forms.selectHelp": "Seleccione las listas para agregar al formulario.", "forms.title": "Formularios", "globals.buttons.add": "Agregar", "globals.buttons.addNew": "Agregar nuevo", "globals.buttons.back": "Regresar", "globals.buttons.cancel": "<PERSON><PERSON><PERSON>", "globals.buttons.clone": "Clonar", "globals.buttons.close": "<PERSON><PERSON><PERSON>", "globals.buttons.continue": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.delete": "Eliminar", "globals.buttons.deleteAll": "Eliminar todos", "globals.buttons.edit": "<PERSON><PERSON>", "globals.buttons.enabled": "Habilitar", "globals.buttons.insert": "Insertar", "globals.buttons.learnMore": "<PERSON><PERSON><PERSON> m<PERSON>", "globals.buttons.more": "Más", "globals.buttons.new": "Nuevo", "globals.buttons.ok": "Aceptar", "globals.buttons.remove": "Eliminar", "globals.buttons.save": "Guardar", "globals.buttons.saveChanges": "Guardar cambios", "globals.days.0": "Dom", "globals.days.1": "Dom", "globals.days.2": "<PERSON>n", "globals.days.3": "Mar", "globals.days.4": "<PERSON><PERSON>", "globals.days.5": "<PERSON><PERSON>", "globals.days.6": "Vie", "globals.days.7": "<PERSON><PERSON><PERSON>", "globals.fields.createdAt": "<PERSON><PERSON><PERSON>", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Nombre", "globals.fields.status": "Estado", "globals.fields.type": "Tipo", "globals.fields.updatedAt": "Actualizado", "globals.fields.uuid": "UUID", "globals.messages.confirm": "¿E<PERSON>á seguro?", "globals.messages.confirmDiscard": "¿Descartar cambios?", "globals.messages.created": "\"{name}\" creado", "globals.messages.deleted": "\"{name}\" eliminado", "globals.messages.deletedCount": "{name} ({num}) eliminado(s)", "globals.messages.done": "Done", "globals.messages.emptyState": "Vacío", "globals.messages.errorCreating": "Error creando {name}: {error}", "globals.messages.errorDeleting": "Error eliminando {name}: {error}", "globals.messages.errorFetching": "Error buscando {name}: {error}", "globals.messages.errorInvalidIDs": "Uno o más IDs ingresados son inválidos: {error}", "globals.messages.errorUUID": "Error generando UUID: {error}", "globals.messages.errorUpdating": "Error actualizando {name}: {error}", "globals.messages.internalError": "Error interno del servidor.", "globals.messages.invalidData": "<PERSON><PERSON>", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID inválido", "globals.messages.invalidUUID": "UUID inválido", "globals.messages.missingFields": "<PERSON><PERSON><PERSON>(s) faltantes: {name}", "globals.messages.notFound": "{name} no encontrado", "globals.messages.passwordChange": "Ingresar una contraseña para cambiar", "globals.messages.updated": "\"{name}\" actualizado", "globals.months.1": "<PERSON><PERSON>", "globals.months.10": "Octubre", "globals.months.11": "Noviembre", "globals.months.12": "Diciembre", "globals.months.2": "<PERSON><PERSON><PERSON>", "globals.months.3": "<PERSON><PERSON>", "globals.months.4": "Abril", "globals.months.5": "Mayo", "globals.months.6": "<PERSON><PERSON>", "globals.months.7": "<PERSON>", "globals.months.8": "Agosto", "globals.months.9": "<PERSON><PERSON><PERSON>", "globals.states.off": "<PERSON><PERSON><PERSON>", "globals.terms.all": "Todos", "globals.terms.analytics": "Analitica", "globals.terms.bounce": "Rebote | Rebotes", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Campaña | Campañas", "globals.terms.campaigns": "Campañas", "globals.terms.dashboard": "Panel", "globals.terms.day": "Día | Días", "globals.terms.hour": "Hora | Horas", "globals.terms.list": "Lista | Listas", "globals.terms.lists": "Listas", "globals.terms.media": "Multimedia | Multimedia", "globals.terms.messenger": "Mensajero | Mensajeros", "globals.terms.messengers": "Mensajeros", "globals.terms.minute": "Minuto | Minutos", "globals.terms.month": "Mes | Meses", "globals.terms.second": "Segundo | Segundos", "globals.terms.settings": "Configuraciones", "globals.terms.subscriber": "Subscriptor | Subscriptores", "globals.terms.subscribers": "Subscriptores", "globals.terms.subscriptions": "Subscription | Subscriptions", "globals.terms.tag": "Etiqueta | Etiquetas", "globals.terms.tags": "Etiqueta", "globals.terms.template": "Plantilla | Plantillas", "globals.terms.templates": "Plantillas", "globals.terms.tx": "Transactional | Transactional", "globals.terms.year": "Año | Años", "import.alreadyRunning": "Se está ejecutándo una importación. Espere a que termine o deténgala antes de intentar una nueva.", "import.blocklist": "Lista de bloqueados", "import.csvDelim": "Delimitador CSV", "import.csvDelimHelp": "El delimitador por defecto es la coma ','", "import.csvExample": "Ejemplo de CSV en crudo", "import.csvFile": "Archivo CSV o ZIP", "import.csvFileHelp": "Seleccione o arrastre un archivo CSV o ZIP aquí", "import.errorCopyingFile": "Error copiando archivo: {error}", "import.errorProcessingZIP": "Error procesando archivo ZIP: {error}", "import.errorStarting": "Error al iniciar la importación: {error}", "import.importDone": "Finalizado", "import.importStarted": "Importación iniciada", "import.instructions": "Instrucciones", "import.instructionsHelp": "Cargue un archivo CSV (o un archivo ZIP con un único archivo CSV) para importar múltiples subscriptores.", "import.invalidDelim": "El delimitador debe ser un carácter único.", "import.invalidFile": "Archivo inválido: {error}", "import.invalidMode": "<PERSON><PERSON>", "import.invalidParams": "Paramétros inválidos: {error}", "import.invalidSubStatus": "Estado de subscripción inválido", "import.listSubHelp": "Listas a subscribir", "import.mode": "Modo", "import.overwrite": "¿Sobrescribir?", "import.overwriteHelp": "¿Sobrescribir nombre y atributos de subscriptores existentes?", "import.recordsCount": "{num} de {total} registros", "import.stopImport": "Detener importación", "import.subscribe": "Subscribir", "import.title": "Importar subscriptores", "import.upload": "<PERSON><PERSON>", "lists.confirmDelete": "¿Está seguro? Esto no elimina subscriptores", "lists.confirmSub": "Subscripción confirmada a {name}", "lists.invalidName": "Nombre inválido", "lists.newList": "Nueva lista", "lists.optin": "Confirmar la inclusión (opt-in)", "lists.optinHelp": "Doble confirmación a la inscripción, envía un correo al subscriptor solicitando su confirmación. En las listas con la opción de confirmación doble, las campañas son enviadas solo a subscriptores ya confirmados.", "lists.optinTo": "Confirmar la <PERSON> en {name}", "lists.optins.double": "Confirmación doble", "lists.optins.single": "Confirmación simple", "lists.sendCampaign": "Enviar campaña", "lists.sendOptinCampaign": "Enviar campaña de confirmación", "lists.type": "Tipo", "lists.typeHelp": "Las listas públicas están abiertas al mundo y sus nombres pueden aparecen en páginas públicas tales como páginas de gestión de subscripciones.", "lists.types.private": "Privada", "lists.types.public": "Pública", "logs.title": "Registros", "maintenance.help": "Algunas acciones pueden tardar más tiempo dependiendo de la cantidad de datos a procesar.", "maintenance.maintenance.unconfirmedOptins": "Suscripciones opt-in no confirmadas", "maintenance.olderThan": "<PERSON>ás viejo que", "maintenance.title": "Mantenimiento", "maintenance.unconfirmedSubs": "Suscripciones no confirmadas anteriores a {name} días.", "media.errorReadingFile": "Error leyendo archivo: {error}", "media.errorResizing": "Error cambiando ta<PERSON> de imagen: {error}", "media.errorSavingThumbnail": "Error guardando miniatura: {error}", "media.errorUploading": "Error cargando archivo: {error}", "media.invalidFile": "Archivo inválido: {error}", "media.title": "Media", "media.unsupportedFileType": "Tipo de archivo no soportado ({type})", "media.upload": "<PERSON><PERSON>", "media.uploadHelp": "Seleccione o arrastre una o más imágenes aquí", "media.uploadImage": "<PERSON><PERSON> imagen", "menu.allCampaigns": "Todas las campañas", "menu.allLists": "Todas las listas", "menu.allSubscribers": "Todos los subscriptores", "menu.dashboard": "<PERSON><PERSON>", "menu.forms": "Formularios", "menu.import": "Importar", "menu.logs": "Registros (logs)", "menu.maintenance": "Maintenance", "menu.media": "Multimedia", "menu.newCampaign": "<PERSON><PERSON>r nueva", "menu.settings": "Configuraciones", "public.archiveEmpty": "No hay mensajes archivados todavía.", "public.archiveTitle": "Archivo de la lista de correo", "public.blocklisted": "Des-suscrita para siempre (bloqueada).", "public.campaignNotFound": "El mensaje de correo electrónico no fue encontrado", "public.confirmOptinSubTitle": "Confirmar subscripción", "public.confirmSub": "Confirmar subscripción", "public.confirmSubInfo": "Ud. ha sido agregado a las siguietnes listas:", "public.confirmSubTitle": "Confirmar", "public.dataRemoved": "Su subscripción y todos sus datos asociados han sido eliminados.", "public.dataRemovedTitle": "<PERSON><PERSON> eliminados", "public.dataSent": "Sus datos han sido enviados en un archivo adjunto a su correo electrónico.", "public.dataSentTitle": "Datos enviados por correo electrónico", "public.errorFetchingCampaign": "Error obteniendo el mensaje de correo electrónico", "public.errorFetchingEmail": "Mensaje de correo electrónico no encontrado", "public.errorFetchingLists": "Error obteniendo listas. Por favor intente nuevamente.", "public.errorProcessingRequest": "Error al procesar la petición. Por favor intente nuevamente.", "public.errorTitle": "Error", "public.invalidFeature": "Esta función no está disponible", "public.invalidLink": "<PERSON><PERSON>", "public.managePrefs": "Gestionar las preferencias", "public.managePrefsUnsub": "Desmarcar las listas para des-suscribirse.", "public.noListsAvailable": "No hay listas disponibles para subscribirse", "public.noListsSelected": "No se seleccionaron listas válidas a las cuales subscribirse", "public.noSubInfo": "No hay subscripciones para confirmar.", "public.noSubTitle": "No hay subscripciones", "public.notFoundTitle": "No encontrado", "public.prefsSaved": "Tus preferencias se han guardado.", "public.privacyConfirmWipe": "¿Está seguro que quiere eliminar todos sus datos de subscripción permanentemente?", "public.privacyExport": "Exportar sus datos", "public.privacyExportHelp": "Una copia de sus datos le será enviada por correo electrónico.", "public.privacyTitle": "Privacidad y datos personales", "public.privacyWipe": "Bo<PERSON>r sus datos", "public.privacyWipeHelp": "Borrar todas sus subscripciones y datos relacionados de la base de datos de forma permanente.", "public.sub": "Subscribirse", "public.subConfirmed": "Subscripción satisfactoria.", "public.subConfirmedTitle": "<PERSON><PERSON>rma<PERSON>", "public.subName": "Nombre (opcional)", "public.subNotFound": "Subscripción no encontrada", "public.subOptinPending": "Un correo electrónico le fue enviado para confirmar su(s) subcripcion(es)", "public.subPrivateList": "Lista privada", "public.subTitle": "Subscribirse", "public.unsub": "Des-Subscribirse", "public.unsubFull": "Adicionalemnte des-subscribirse de cualquer correo electrónico futuro.", "public.unsubHelp": "¿Desea des-subscribirse de esta lista de correo?", "public.unsubTitle": "Des-Subscribirse", "public.unsubbedInfo": "Ud. se ha des-subscrito de forma satisfactoria", "public.unsubbedTitle": "Des-subscrito.", "public.unsubscribeTitle": "Des-subscribirse de una lista de correo", "settings.appearance.adminHelp": "CSS adicional para aplicar en la interaz de administración.", "settings.appearance.adminName": "Administració", "settings.appearance.customCSS": "CSS adicional", "settings.appearance.customJS": "JavaScript adicional", "settings.appearance.name": "Apariencia", "settings.appearance.publicHelp": "CSS y JavaScript personalizado para aplicar en las páginas públicas.", "settings.appearance.publicName": "Publico", "settings.bounces.action": "Acción", "settings.bounces.blocklist": "Lista de bloqueo", "settings.bounces.count": "<PERSON><PERSON><PERSON>", "settings.bounces.countHelp": "Número de rebotes por subscripción", "settings.bounces.delete": "Eliminar", "settings.bounces.enable": "Activar el procesamiento de rebotes", "settings.bounces.enableMailbox": "Activar el buzón de rebotes", "settings.bounces.enableSES": "Activar SES", "settings.bounces.enableSendgrid": "Activar SendGrid", "settings.bounces.enableWebhooks": "Activar webhooks de rebotes", "settings.bounces.enabled": "Activado", "settings.bounces.folder": "Carpeta", "settings.bounces.folderHelp": "Nombre de la carpeta IMAP a escanear, por ejemplo: Entrada.", "settings.bounces.invalidScanInterval": "El intervalo mínimo de escanéo de los rebotes debería de ser 1 minuto.", "settings.bounces.name": "<PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Intervalo de escaneo", "settings.bounces.scanIntervalHelp": "Intervalo en el que el buzón de rebotes debería ser escaneado para encontrar nuevos rebotes (s para segundos, m para minutos).", "settings.bounces.sendgridKey": "Clave para SendGrid", "settings.bounces.type": "Tipo", "settings.bounces.username": "Nombre de usuario", "settings.confirmRestart": "Asegúrese de que las campañas ejecutándose están pausadas. ¿Reiniciar?", "settings.duplicateMessengerName": "Nombre de mensajero duplicado: {name}", "settings.errorEncoding": "Error codificando configuración: {error}", "settings.errorNoSMTP": "Al menos un bloque SMTP debe estar habilitado", "settings.general.adminNotifEmails": "Correos electrónicos para notificación de administradores", "settings.general.adminNotifEmailsHelp": "Lista de correos electrónicos separados por comas, a donde las notificaciones como actualizaciones de importación, campañas completadas, fallas, etc. deben ser enviadas.", "settings.general.checkUpdates": "Revisa las actualizaciones", "settings.general.checkUpdatesHelp": "Periódicamente buscar nuevas actualizaciones y notificarme.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publish campaigns on which archiving is enabled on the public website.", "settings.general.enablePublicSubPage": "Habilitar pagina publica de subscripción", "settings.general.enablePublicSubPageHelp": "Muestra una página con todas las listas públicas para subscribirse.", "settings.general.faviconURL": "URL del Favicon", "settings.general.faviconURLHelp": "(Opcional) URL completa del Favicon estático que debe mostrarse de cara a los usuarios en páginas como la página de des-subscripción", "settings.general.fromEmail": "Correo electrónico predeterminado del remitente", "settings.general.fromEmailHelp": "Correo electrónico del remitente para mostrar en campañas de correo salientes. Puede ser ajustado por cada campaña.", "settings.general.language": "Idioma", "settings.general.logoURL": "URL de logotipo", "settings.general.logoURLHelp": "(Opcional) URL completa de logotipo que a mostrse al usuario en páginas como la página de des-subscripción", "settings.general.name": "General", "settings.general.rootURL": "URL raíz", "settings.general.rootURLHelp": "URL pública de la instalación (sin incluir la barra final)", "settings.general.sendOptinConfirm": "Enviar confirmación de inscripción", "settings.general.sendOptinConfirmHelp": "Cuando haya una nueva subscripción mediante el formulario o la interfaz de administración, enviar un correo de confirmación al usuario.", "settings.general.siteName": "Nombre del sitio / web", "settings.invalidMessengerName": "Nombre inválido de mensajero.", "settings.mailserver.authProtocol": "Protocolo de autenticación", "settings.mailserver.host": "Host/Servidor", "settings.mailserver.hostHelp": "Dirección del servidor SMTP", "settings.mailserver.idleTimeout": "Tiempo máximo de inactividad", "settings.mailserver.idleTimeoutHelp": "Tiempo máximo de espara a nueva actividad en una conexión antes de cerrarla y retirarla del pool de conexiones (s para segundos, m para minutos).", "settings.mailserver.maxConns": "Conexiones máximas", "settings.mailserver.maxConnsHelp": "Número máximo de conexiones concurrentes hacia el servidor SMTP.", "settings.mailserver.password": "Contraseña", "settings.mailserver.passwordHelp": "Ingresar contraseña para cambiar", "settings.mailserver.port": "Puerto", "settings.mailserver.portHelp": "Puerto del servidor SMTP", "settings.mailserver.skipTLS": "Omitir verificación de TLS", "settings.mailserver.skipTLSHelp": "Omitir la verificación del nombre de servidor en un certificado TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Habilitar STARTTLS", "settings.mailserver.username": "Nombre de usuario", "settings.mailserver.waitTimeout": "Tiempo máximo de espera", "settings.mailserver.waitTimeoutHelp": "Tiempo máximo de espera de nueva actividad en una conexión antes de cerrarla y retirarla del pool de conexiones (s para segundos, m para minutos).", "settings.media.provider": "<PERSON><PERSON><PERSON><PERSON>", "settings.media.s3.bucket": "Bucket/contenedor", "settings.media.s3.bucketPath": "<PERSON><PERSON> de <PERSON>", "settings.media.s3.bucketPathHelp": "Ruta dentro del bucket/contenedor desde donde cargar los archivos. Por defecto es '/'", "settings.media.s3.bucketType": "Tipo de bucket/conteneor", "settings.media.s3.bucketTypePrivate": "Privado", "settings.media.s3.bucketTypePublic": "Público", "settings.media.s3.key": "Llave de acceso a S3 (access key)", "settings.media.s3.publicURL": "URL pública personalizada (opcional)", "settings.media.s3.publicURLHelp": "Dominio personalizado de S3 para usar en ennlaces de imágenes, en lugar de la URL predeterminada de S3.", "settings.media.s3.region": "Región", "settings.media.s3.secret": "Secreto de acceso a S3 (secret access key)", "settings.media.s3.uploadExpiry": "Expiración de la carga", "settings.media.s3.uploadExpiryHelp": "(Opcional) TTL específico (en segundos) para la URL pre firmada generada. Solo es aplicable para buckets/contenedores privados (s, m, h, d para segundos, minutos, horas, días)", "settings.media.s3.url": "URL de API de S3", "settings.media.s3.urlHelp": "Cambiar únicamente si se utiliza un servicio S3 personalizado (por ejemplo MinIO).", "settings.media.title": "Cargas multimedia", "settings.media.upload.path": "<PERSON>uta de carga", "settings.media.upload.pathHelp": "<PERSON>uta o prefijo donde los archivos seránn cargados.", "settings.media.upload.uri": "URI de carga", "settings.media.upload.uriHelp": "La URI de carga es visible hacia afuera. Los archivos cargados en el directorio de carga serán accesible públicamente bajo {root_url}, por ejemplo, https://listmonk.susitio.com/uploads", "settings.messengers.maxConns": "Conexiones máximas", "settings.messengers.maxConnsHelp": "Número máximo de conexiones al servidor", "settings.messengers.messageSaved": "Configuracion guardada. Recargando la aplicación.", "settings.messengers.name": "Mensajeros", "settings.messengers.nameHelp": "Ejemplo: my-sms. Alfanumérico / guión", "settings.messengers.password": "Contraseña", "settings.messengers.retries": "Reintentos", "settings.messengers.retriesHelp": "Número de reintentos cuando un mensaje falla", "settings.messengers.skipTLSHelp": "Omitir verificación del nombre de host en un certificado TLS", "settings.messengers.timeout": "Tiempo máximo por inactividad", "settings.messengers.timeoutHelp": "Tiempo máximo de espara a nueva actividad en una conexión antes de cerrarla y retirarla del pool de conexiones (s para segundos, m para minutos).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "URL raíz del servidor Postback", "settings.messengers.username": "Nombre de usuario", "settings.needsRestart": "Configuración cambiada. Pause todas las campañas y reinicie la aplicación.", "settings.performance.batchSize": "Tamaño del lote", "settings.performance.batchSizeHelp": "Número de subscriptores a extraer de la base de datos en cada iteración individul. Cada iteración extrae subscriptores de la base de datos, envía mensajes a ellos y luego avanza a la siguiente iteración para obtener el siguiente lote. Este número idealmente debería ser mayor que el máximo rendimiento alcanzable (concurrencia * tasa de envíos)", "settings.performance.concurrency": "Concurrencia", "settings.performance.concurrencyHelp": "Número máximo de hilos que intentarán enviar mensajes de forma simultánea.", "settings.performance.maxErrThreshold": "Umbral máximo de errores.", "settings.performance.maxErrThresholdHelp": "El número de errores (Por ejemplo: timeouts de SMTP mientras se envía correo) que una campaña en proceso debe tolerar antes de ser pausada para una invesitigación o intervención manual. 0 para no detenerse nunca.", "settings.performance.messageRate": "Tasa de <PERSON>", "settings.performance.messageRateHelp": "Número máximo de mensajes enviados por segundo por cada hilo. Si la concurrencia = 10 y la tasa de envíos = 10, entonces hasta 10x10=100 mensajes podrían ser sacados en cada segundo. Esto junto con la concurrencia deberían ser modificados para que el número de mensajes salientes no supere las tasas de envío de los servidores, si es que existen.", "settings.performance.name": "Rendimiento", "settings.performance.slidingWindow": "Habilitar límite de corrimiento de ventana", "settings.performance.slidingWindowDuration": "Duración", "settings.performance.slidingWindowDurationHelp": "Duración del periodo del corrimiento de ventana (m para minutos, h para horas).", "settings.performance.slidingWindowHelp": "Límite total de mensajes que son enviados en un periodo. Cuando se alcanza este límite, los mensajes son retenidos hasta que se libere la ventana de tiempo.", "settings.performance.slidingWindowRate": "Mensajes <PERSON>", "settings.performance.slidingWindowRateHelp": "Máximo número de mensajes a enviar dentro de la duración de la ventana.", "settings.privacy.allowBlocklist": "Permitir blocklisting", "settings.privacy.allowBlocklistHelp": "¿Permitir a los subscriptores des-subscribirse de todas las listas de correo y marcarlas como \"blocklisted\"?", "settings.privacy.allowExport": "Permitir exportar", "settings.privacy.allowExportHelp": "¿Permitir a los subscriptores exportar los datos recabados de ellos?", "settings.privacy.allowPrefs": "Permitir cambios en las preferencias", "settings.privacy.allowPrefsHelp": "Permitir a las cuentas suscritas realizar cambios como nombre o pertenencia a diferentes listas.", "settings.privacy.allowWipe": "<PERSON><PERSON><PERSON>", "settings.privacy.allowWipeHelp": "Permitir a los subscriptores eliminarse incluyendo sus subscripciones y todos sus datos de la base de datos. Las vistas de las campañas y los vínculos cliqueados también son eliminados mientras que las vistas y el conteo de clics se mantienen. (sin subscriptores asociados a ellos) de manera que las estadísticas y el análisis no se vea afectado.", "settings.privacy.domainBlocklist": "Listado de dominios bloqueados", "settings.privacy.domainBlocklistHelp": "Los correos electrónicos de estos dominios estan desabilitados para subscribirse. Introduzca un dominio por línea, por ejemplo: unsitio.com", "settings.privacy.individualSubTracking": "Seguimiento de subscriptor inválido.", "settings.privacy.individualSubTrackingHelp": "Seguir a nivel de subscriptor las vistas y clics en una campaña. Cuando está deshabilitado, el seguimiento de vistas y clics continua sin ser asociado con subscriptores individuales.", "settings.privacy.listUnsubHeader": "Incluir el encabezado para `des-subscribirse` de la lista", "settings.privacy.listUnsubHeaderHelp": "Incluye los encabezados de des-subscripción para habilitar a los clientes de correo para permitir a los usuarios des-subscribirse con un solo clic.", "settings.privacy.name": "Privacidad", "settings.restart": "Reiniciar", "settings.smtp.customHeaders": "Encabezados personalizados", "settings.smtp.customHeadersHelp": "Lista de encabezados opcionales a incluir en todos los mensajes enviados desde este servidor. Por ejemplo {{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Habilitado", "settings.smtp.heloHost": "HELO hostname", "settings.smtp.heloHostHelp": "Opcional. Algunos servidores SMTP requieren un FQDN en el nombre de host. Por defecto se usa 'localhost' cmo dato HELLO. Configurar aquí un nombre de host específico en caso se ser requerido.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Reintentos", "settings.smtp.retriesHelp": "Número de reintentos cuando un mensaje falla.", "settings.smtp.sendTest": "Enviar correo electrónico de prueba", "settings.smtp.setCustomHeaders": "Configurar encabezados personalizados.", "settings.smtp.testConnection": "Probar conexi<PERSON>", "settings.smtp.testEnterEmail": "Enter password to test", "settings.smtp.toEmail": "Correo electrónico del destinatario", "settings.title": "Configuraciones", "settings.updateAvailable": "Una actualización {version} está disponible.", "subscribers.advancedQuery": "<PERSON><PERSON><PERSON>", "subscribers.advancedQueryHelp": "Expresión SQL parcial para consultar los atributos de un subscriptor", "subscribers.attribs": "Atributos", "subscribers.attribsHelp": "Los atributos son definidos como un objeto JSON llave/valor, por ejemplo:", "subscribers.blocklistedHelp": "Subscriptores blocklisted nunca recibirán correos.", "subscribers.confirmBlocklist": "Blocklist {num} subscriptor(es)?", "subscribers.confirmDelete": "Eliminar {num} subscriptor(es)?", "subscribers.confirmExport": "Exportar {num} subscriptor(es)?", "subscribers.domainBlocklisted": "El dominio del correo electrónico está en la lista de bloqueos.", "subscribers.downloadData": "<PERSON><PERSON><PERSON>", "subscribers.email": "Correo electrónico", "subscribers.emailExists": "El correo electrónico ya existe.", "subscribers.errorBlocklisting": "Error blocklisting subscriptrores: {error}", "subscribers.errorNoIDs": "No se ingresaron IDs.", "subscribers.errorNoListsGiven": "No se ingresaron listas.", "subscribers.errorPreparingQuery": "Error preparando la consulta del subscriptor: {error}", "subscribers.errorSendingOptin": "Error en<PERSON>do correo opt-in ", "subscribers.export": "Exportar", "subscribers.invalidAction": "Accion inválida", "subscribers.invalidEmail": "Correo electrónico inválidoo", "subscribers.invalidJSON": "JSON inválido en atributos.", "subscribers.invalidName": "Nombre inválido.", "subscribers.listChangeApplied": "Cambio de lista aplicado.", "subscribers.lists": "Listas", "subscribers.listsHelp": "Listas desde donde los subscriptores se han des-subscrito no pueden ser eliminadas.", "subscribers.listsPlaceholder": "Lista a subscribir a", "subscribers.manageLists": "Administrar listas", "subscribers.markUnsubscribed": "Marcar como des-subscrito", "subscribers.newSubscriber": "Nuevo subscriptor", "subscribers.numSelected": "{num} subscriptores seleccionados", "subscribers.optinSubject": "Confirmar subscripción", "subscribers.preconfirm": "Pre-confirmar subscripción", "subscribers.preconfirmHelp": "No enviar correo de confirmación y marcar todas las subscripciones a las listas como 'subscritas'.", "subscribers.query": "Consulta", "subscribers.queryPlaceholder": "Correo electrónico o nombre", "subscribers.reset": "Restablecer", "subscribers.selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todos ({num})", "subscribers.sendOptinConfirm": "Enviar confirmación de subscripción voluntaria", "subscribers.sentOptinConfirm": "Se envió la confirmación de subscripción voluntaria", "subscribers.status.blocklisted": "Bloqueado", "subscribers.status.confirmed": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.status.enabled": "Habilitado", "subscribers.status.subscribed": "Subscrito", "subscribers.status.unconfirmed": "<PERSON>ar", "subscribers.status.unsubscribed": "Des-Subscrito", "subscribers.subscribersDeleted": "{num} subscriptor(es) borrados", "templates.cantDeleteDefault": "No se puede borrar la plantilla predeterminada", "templates.default": "predeterminada", "templates.dummyName": "Campaña de prueba", "templates.dummySubject": "Asunto de la campaña de prueba", "templates.errorCompiling": "Error compilando plantilla: {error}", "templates.errorRendering": "Error generando mensaje: {error}", "templates.fieldInvalidName": "Longitud de nombre inválida", "templates.makeDefault": "Establecer como plantilla predeterminada", "templates.newTemplate": "Nueva plantilla", "templates.placeholderHelp": "El marcador {placeholder} debe aparecer exactamente una vez en la plantilla.", "templates.preview": "Vista previa", "templates.rawHTML": "HTML de orige", "templates.subject": "<PERSON><PERSON><PERSON>", "users.login": "Ingresar", "users.logout": "Salir"}