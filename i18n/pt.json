{"_.code": "pt", "_.name": "Portuguese (pt)", "admin.errorMarshallingConfig": "Erro ao ler o config: {error}", "analytics.count": "Quantidade", "analytics.fromDate": "<PERSON><PERSON>", "analytics.invalidDates": "Datas `desde` e `até` inválidas.", "analytics.isUnique": "As quantidades são únicas por subscritor.", "analytics.links": "Endereços", "analytics.nonUnique": "As quantidades não são únicas dado que o rastreamento individual de cada subscritor está desligado.", "analytics.title": "Analítica", "analytics.toDate": "<PERSON><PERSON>", "bounces.source": "Fonte", "bounces.unknownService": "Serviço desconhecido.", "bounces.view": "Ver bounces", "campaigns.addAltText": "Adicionar mensagem alternativa em texto simples", "campaigns.archive": "Arquivo", "campaigns.archiveEnable": "Publicar para o arquivo público", "campaigns.archiveHelp": "Publicar (em execução, em pausa e terminadas) as mensagens da campanha no arquivo público.", "campaigns.archiveMeta": "<PERSON><PERSON><PERSON> da campanha", "campaigns.archiveMetaHelp": "Dados do subscritor modelo a usar em mensagens públicas, tais como nome, email e quais quer outros atributos opcionais usados na mensagem ou template da campanha.", "campaigns.cantUpdate": "Não é possível atualizar uma campanha em curso ou terminada.", "campaigns.clicks": "Cliques", "campaigns.confirmDelete": "Eliminar {name}", "campaigns.confirmSchedule": "A campanha irá começar automaticamente na data e hora agendadas. Agendar agora?", "campaigns.confirmSwitchFormat": "O conteúdo pode perder a formatação. Continuar?", "campaigns.content": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.contentHelp": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "campaigns.continue": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.copyOf": "Có<PERSON> de {name}", "campaigns.customHeadersHelp": "Lista de headers customizados para anexar às mensagens de saída, e.g.: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Dia e hora", "campaigns.ended": "Terminada", "campaigns.errorSendTest": "Erro ao enviar teste: {error}", "campaigns.fieldInvalidBody": "Erro ao compilar corpo da campanha: {error}", "campaigns.fieldInvalidFromEmail": "`from_email` inv<PERSON>lido.", "campaigns.fieldInvalidListIDs": "Lista de IDs inválida.", "campaigns.fieldInvalidMessenger": "<PERSON><PERSON><PERSON><PERSON> {name} desconhecido.", "campaigns.fieldInvalidName": "Tamanho de nome inválido.", "campaigns.fieldInvalidSendAt": "Data agendada deve ser no futuro.", "campaigns.fieldInvalidSubject": "<PERSON><PERSON><PERSON> de corpo inválido.", "campaigns.formatHTML": "Formatar HTML", "campaigns.fromAddress": "Endereço do Remetente", "campaigns.fromAddressPlaceholder": "<PERSON>u Nome <<EMAIL>>", "campaigns.invalid": "<PERSON><PERSON><PERSON>", "campaigns.invalidCustomHeaders": "Headers customizados inválidos: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "A campanha necessita de uma data para ser agendada.", "campaigns.newCampaign": "Nova campanha", "campaigns.noKnownSubsToTest": "Não existem subscritores para testar.", "campaigns.noOptinLists": "<PERSON>ão foram encontradas listas opt-in para criar a campanha.", "campaigns.noSubs": "Não existem subscritores nas listas selecionadas para criar a campanha.", "campaigns.noSubsToTest": "Não existem subscritores para usar.", "campaigns.notFound": "Campanha não encontrada.", "campaigns.onlyActiveCancel": "Apenas campanhas ativas podem ser canceladas.", "campaigns.onlyActivePause": "Apenas campanhas ativas podem ser pausadas.", "campaigns.onlyDraftAsScheduled": "Apenas rascunhos de campanhas podem ser agendadas.", "campaigns.onlyPausedDraft": "Apenas campanhas pausadas e rascunhos podem ser iniciadas.", "campaigns.onlyScheduledAsDraft": "Apenas campanhas <PERSON> podem ser guardadas como rascunhos.", "campaigns.pause": "Pausar", "campaigns.plainText": "Texto simples", "campaigns.preview": "Pré-visualizar", "campaigns.progress": "Progresso", "campaigns.queryPlaceholder": "Nome ou assunto", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML simples", "campaigns.removeAltText": "Remover mensagem alternativa em texto simples", "campaigns.richText": "Texto rico", "campaigns.schedule": "<PERSON><PERSON><PERSON> camp<PERSON>", "campaigns.scheduled": "Agendada", "campaigns.send": "Enviar", "campaigns.sendLater": "Enviar mais tarde", "campaigns.sendTest": "Enviar mensagem de teste", "campaigns.sendTestHelp": "Clica Enter após escrever o endereço de múltiplos destinatários. Os endereços devem pertencer a subscritores existentes.", "campaigns.sendToLists": "Listas a enviar para", "campaigns.sent": "Enviada", "campaigns.start": "<PERSON><PERSON><PERSON>", "campaigns.started": "\"{name}\" come<PERSON><PERSON>", "campaigns.startedAt": "<PERSON><PERSON><PERSON>", "campaigns.stats": "Estatísticas", "campaigns.status.cancelled": "Cancelada", "campaigns.status.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.status.finished": "Terminada", "campaigns.status.paused": "<PERSON>", "campaigns.status.running": "Em progresso", "campaigns.status.scheduled": "Agendada", "campaigns.statusChanged": "\"{name}\" está {status}", "campaigns.subject": "<PERSON><PERSON><PERSON>", "campaigns.testEmails": "E-mails", "campaigns.testSent": "Mensagem de teste enviada", "campaigns.timestamps": "Carimbo de hora", "campaigns.trackLink": "Link de rastreamento", "campaigns.views": "Visualizações", "dashboard.campaignViews": "Vista de campanhas", "dashboard.linkClicks": "Cliques nos links", "dashboard.messagesSent": "Mensagens enviadas", "dashboard.orphanSubs": "Órfãos", "email.data.info": "Uma cópia de todos os seus dados está em anexo em formato JSON. Pode ser visualizada num editor de texto.", "email.data.title": "Os seus dados", "email.optin.confirmSub": "Confirmar subscrição", "email.optin.confirmSubHelp": "Confirme a sua subscrição clicando no botão abaixo.", "email.optin.confirmSubInfo": "Foi adicionado às seguintes listas:", "email.optin.confirmSubTitle": "Confirmar subscrição", "email.optin.confirmSubWelcome": "O<PERSON><PERSON>", "email.optin.privateList": "Lista privada", "email.status.campaignReason": "Motivo", "email.status.campaignSent": "Enviada", "email.status.campaignUpdateTitle": "Atualização de campanha", "email.status.importFile": "<PERSON><PERSON><PERSON>", "email.status.importRecords": "Registos", "email.status.importTitle": "Importar atualização", "email.status.status": "Estado", "email.unsub": "Cancelar subscrição", "email.unsubHelp": "Não quer receber estes e-mails?", "email.viewInBrowser": "Ver no navegador", "forms.formHTML": "Formulário HTML", "forms.formHTMLHelp": "Usa o seguinte código HTML para mostrar um formulário de subscrição numa página externa. O formulário deve ter um campo de email e um ou mais campos `l` (UUID de listas). O campo de nome é opcional.", "forms.noPublicLists": "Não existem listas públicas para gerar um formulário.", "forms.publicLists": "Listas públicas", "forms.publicSubPage": "Página pública de subscrição", "forms.selectHelp": "Seleciona listas para adicionar ao formulário.", "forms.title": "Formulários", "globals.buttons.add": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.addNew": "Adicionar novo", "globals.buttons.back": "Voltar", "globals.buttons.cancel": "<PERSON><PERSON><PERSON>", "globals.buttons.clone": "Duplicar", "globals.buttons.close": "<PERSON><PERSON><PERSON>", "globals.buttons.continue": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.delete": "Eliminar", "globals.buttons.deleteAll": "Eliminar todos", "globals.buttons.edit": "<PERSON><PERSON>", "globals.buttons.enabled": "Ativo", "globals.buttons.insert": "Inserir", "globals.buttons.learnMore": "<PERSON>ber mais", "globals.buttons.more": "<PERSON><PERSON>", "globals.buttons.new": "Novo", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Remover", "globals.buttons.save": "Guardar", "globals.buttons.saveChanges": "Guardar alterações", "globals.days.0": "Dom", "globals.days.1": "Seg", "globals.days.2": "<PERSON><PERSON>", "globals.days.3": "<PERSON>ua", "globals.days.4": "<PERSON>ui", "globals.days.5": "Sex", "globals.days.6": "<PERSON><PERSON><PERSON>", "globals.days.7": "<PERSON><PERSON><PERSON>", "globals.fields.createdAt": "Criado a", "globals.fields.description": "Descrição", "globals.fields.id": "ID", "globals.fields.name": "Nome", "globals.fields.status": "Estado", "globals.fields.type": "Tipo", "globals.fields.updatedAt": "Atualizado a", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Tens a certeza?", "globals.messages.confirmDiscard": "Descartar alterações?", "globals.messages.created": "\"{name}\" criado", "globals.messages.deleted": "\"{name}\" eliminado", "globals.messages.deletedCount": "{name} ({num}) eliminado/a", "globals.messages.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.messages.emptyState": "Não há nada aqui", "globals.messages.errorCreating": "Erro ao criar {name}: {error}", "globals.messages.errorDeleting": "Erro ao eliminar {name}: {error}", "globals.messages.errorFetching": "Erro ao carregar {name}: {error}", "globals.messages.errorInvalidIDs": "Foram dados um ou mais IDs inválidos: {error}", "globals.messages.errorUUID": "Erro ao gerar UUID: {error}", "globals.messages.errorUpdating": "Erro ao atualizar {name}: {error}", "globals.messages.internalError": "Erro interno no servidor", "globals.messages.invalidData": "<PERSON><PERSON>", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID inválido", "globals.messages.invalidUUID": "UUID inválido", "globals.messages.missingFields": "Campo(s) em falta: {name}", "globals.messages.notFound": "{name} não encontrado", "globals.messages.passwordChange": "Insere um valor para alterar", "globals.messages.updated": "\"{name}\" atualizado", "globals.months.1": "Jan", "globals.months.10": "Out", "globals.months.11": "Nov", "globals.months.12": "<PERSON>z", "globals.months.2": "<PERSON>v", "globals.months.3": "Mar", "globals.months.4": "Abr", "globals.months.5": "<PERSON>", "globals.months.6": "Jun", "globals.months.7": "Jul", "globals.months.8": "Ago", "globals.months.9": "Set", "globals.states.off": "Des<PERSON><PERSON>", "globals.terms.all": "<PERSON><PERSON>(as)", "globals.terms.analytics": "Analítica", "globals.terms.bounce": "Bounce | Bounces", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Campanha | Campanhas", "globals.terms.campaigns": "<PERSON>an<PERSON>", "globals.terms.dashboard": "Dashboard", "globals.terms.day": "Dia | Dias", "globals.terms.hour": "Hora | Horas", "globals.terms.list": "Lista | Listas", "globals.terms.lists": "Listas", "globals.terms.media": "Mídia | Mídia", "globals.terms.messenger": "Mensageiro | Mensageiros", "globals.terms.messengers": "Mensageiros", "globals.terms.minute": "Minuto | Minutos", "globals.terms.month": "Mês | Meses", "globals.terms.second": "Segundo | Segundos", "globals.terms.settings": "Definições", "globals.terms.subscriber": "Subscritor | Subcritores", "globals.terms.subscribers": "Subscritores", "globals.terms.subscriptions": "Subscrição | Subscrições", "globals.terms.tag": "Etiqueta | Etiquetas", "globals.terms.tags": "Etiquetas", "globals.terms.template": "Modelo | Modelos", "globals.terms.templates": "<PERSON><PERSON>", "globals.terms.tx": "Transacional | Transacional", "globals.terms.year": "Ano | Anos", "import.alreadyRunning": "Uma importação já está em curso. Aguarda que termine ou cancela-a antes de tentares novamente.", "import.blocklist": "Lista de bloqueio", "import.csvDelim": "Delimitador CSV", "import.csvDelimHelp": "O delimitador padrão é uma vírgula.", "import.csvExample": "Exemplo CSV simples", "import.csvFile": "Ficheiro CSV ou ZIP", "import.csvFileHelp": "Clica ou arrasta um ficheiro CSV ou ZIP para aqui", "import.errorCopyingFile": "Erro ao copiar fi<PERSON>iro: {error}", "import.errorProcessingZIP": "Erro ao processar ficheiro ZIP: {error}", "import.errorStarting": "Erro ao começar importação: {error}", "import.importDone": "Terminado", "import.importStarted": "Importação iniciada", "import.instructions": "Instruções", "import.instructionsHelp": "Envia um ficheiro CSV ou ficheiro ZIP com um único CSV para importares subscritores em massa. O ficheiro CSV deve conter os seguintes cabeçalhos com os nomes de colunas exatos. attributes (opcional) deve ser uma string JSON válida, com aspas de escape duplo.", "import.invalidDelim": "O delimitador deve ser um caractere único.", "import.invalidFile": "<PERSON><PERSON><PERSON> inválido: {error}", "import.invalidMode": "<PERSON><PERSON>", "import.invalidParams": "Parâmetros inválidos: {error}", "import.invalidSubStatus": "Estado de subscrição inválido", "import.listSubHelp": "Listas a subscrever.", "import.mode": "Modo", "import.overwrite": "Sobrescrever?", "import.overwriteHelp": "Sobrescrever nome e atributos de subscritores existentes?", "import.recordsCount": "{num} / {total} registos", "import.stopImport": "Parar importação", "import.subscribe": "Subscrever", "import.title": "Importar subscritores", "import.upload": "Upload", "lists.confirmDelete": "Tens a certeza? Isto não elimina subscritores.", "lists.confirmSub": "Confirmar subscrição(ões) para {name}", "lists.invalidName": "Nome inválido", "lists.newList": "Nova lista", "lists.optin": "Adesão", "lists.optinHelp": "Double opt-in envia um email ao subscritor a pedir confirmação. Em listas double opt-in, as campanhas são apenas enviadas para subscritores confirmados.", "lists.optinTo": "Opt-in a {name}", "lists.optins.double": "<PERSON><PERSON><PERSON> dupla", "lists.optins.single": "Adesão única", "lists.sendCampaign": "<PERSON><PERSON><PERSON> campanha", "lists.sendOptinCampaign": "Enviada campanha opt-in", "lists.type": "Tipo", "lists.typeHelp": "Listas públicas estão abertas para toda a gente se subscrever e os seus nomes podem aparecer em páginas públicas, como a página de gestão de subscrições.", "lists.types.private": "Privado", "lists.types.public": "Público", "logs.title": "Logs (Histórico)", "maintenance.help": "Algumas ações podem demorar algum tempo, dependendo da quantidade de dados.", "maintenance.maintenance.unconfirmedOptins": "Adesão a subscrições não confirmadas", "maintenance.olderThan": "<PERSON>s antigo que", "maintenance.title": "Manutenção", "maintenance.unconfirmedSubs": "Subscrições não confirmadas há mais de {name} dias.", "media.errorReadingFile": "Erro ao ler ficheiro: {error}", "media.errorResizing": "Erro ao alterar tamanho da imagem: {error}", "media.errorSavingThumbnail": "Erro ao guardar miniatura: {error}", "media.errorUploading": "Erro ao enviar ficheiro: {error}", "media.invalidFile": "<PERSON><PERSON><PERSON> inválido: {error}", "media.title": "Mí<PERSON>", "media.unsupportedFileType": "Tipo de ficheiro não suportado ({type})", "media.upload": "Upload", "media.uploadHelp": "Clica ou arrasta uma ou mais imagens aqui", "media.uploadImage": "Enviar imagens", "menu.allCampaigns": "<PERSON><PERSON> as camp<PERSON><PERSON>", "menu.allLists": "<PERSON><PERSON> as listas", "menu.allSubscribers": "Todos os subscritores", "menu.dashboard": "Dashboard", "menu.forms": "Formulários", "menu.import": "Importar", "menu.logs": "Hist<PERSON><PERSON><PERSON>", "menu.maintenance": "Manutenção", "menu.media": "Mí<PERSON>", "menu.newCampaign": "Criar nova", "menu.settings": "Definições", "public.archiveEmpty": "Sem mensagens arquivadas.", "public.archiveTitle": "Arquivo da lista de e-mail", "public.blocklisted": "Subscrição cancelada permanentemente.", "public.campaignNotFound": "A mensagem de email não foi encontrada.", "public.confirmOptinSubTitle": "Confirmar subscrição", "public.confirmSub": "Confirmar subscrição", "public.confirmSubInfo": "Foi adicionado às seguintes listas:", "public.confirmSubTitle": "Confirmar", "public.dataRemoved": "As suas subscrições e todos os dados associados foram removidos.", "public.dataRemovedTitle": "Dados removidos", "public.dataSent": "Os seus dados foram-lhe enviados em anexo por email.", "public.dataSentTitle": "Dados enviados por email", "public.errorFetchingCampaign": "Error fetching e-mail message", "public.errorFetchingEmail": "Mensagem de email não encontrada", "public.errorFetchingLists": "Erro ao carregar listas. Por favor tente novamente.", "public.errorProcessingRequest": "Erro ao processar pedido. Por favor tente novamente.", "public.errorTitle": "Erro", "public.invalidFeature": "That feature is not available", "public.invalidLink": "<PERSON> in<PERSON>", "public.managePrefs": "<PERSON><PERSON><PERSON>", "public.managePrefsUnsub": "Desselecione listas para cancelar a subscrição à mesma.", "public.noListsAvailable": "Não existem listas disponíveis para subscrever.", "public.noListsSelected": "Não foram selecionadas listas válidas para subscrever.", "public.noSubInfo": "There are no subscriptions to confirm", "public.noSubTitle": "Sem subscrições", "public.notFoundTitle": "Não encontrado", "public.prefsSaved": "As suas preferências foram guardadas.", "public.privacyConfirmWipe": "Tem a certeza que deseja apagar permanentemente todos os seus dados de subscrições?", "public.privacyExport": "Exportar os seus dados", "public.privacyExportHelp": "Uma cópia dos seus dados ser-lhe-á enviada por email.", "public.privacyTitle": "Privacidade e dados", "public.privacyWipe": "<PERSON><PERSON><PERSON> os seus dados", "public.privacyWipeHelp": "Apagar permanentemente da base de dados todas as suas subscrições e dados relacionados.", "public.sub": "Subscrever", "public.subConfirmed": "Subscribed successfully", "public.subConfirmedTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.subName": "Nome (opcional)", "public.subNotFound": "Subscrição não encontrada.", "public.subOptinPending": "Foi-lhe enviado um email para confirmar a(s) sua(s) subscrição(ões)", "public.subPrivateList": "Lista privada", "public.subTitle": "Subscrever", "public.unsub": "Cancelar subscrição", "public.unsubFull": "Também cancelar subscrição de todos os emails futuros.", "public.unsubHelp": "Quer cancelar a subscrição desta lista de emails?", "public.unsubTitle": "Cancelar subscrição", "public.unsubbedInfo": "A sua subscrição foi cancelada com sucesso.", "public.unsubbedTitle": "Subscrição cancelada", "public.unsubscribeTitle": "Cancelar subscrição da lista de emails", "settings.appearance.adminHelp": "CSS customizado para aplicar à interface de administrador.", "settings.appearance.adminName": "Administrador", "settings.appearance.customCSS": "CSS customizado", "settings.appearance.customJS": "JavaScript customizado", "settings.appearance.name": "Aparência", "settings.appearance.publicHelp": "CSS e JavaScript customizados a aplicar às páginas públicas.", "settings.appearance.publicName": "Público", "settings.bounces.action": "Ação", "settings.bounces.blocklist": "Lista de Bloqueico", "settings.bounces.count": "Número de bounces", "settings.bounces.countHelp": "Número de bounces por subscritor", "settings.bounces.delete": "Eliminar", "settings.bounces.enable": "Ligar processamento de bounces", "settings.bounces.enableMailbox": "Ligar caixa de correio de bounces", "settings.bounces.enableSES": "Ligar SES", "settings.bounces.enableSendgrid": "Ligar SendGrid", "settings.bounces.enableWebhooks": "Ligar webhooks de bounces", "settings.bounces.enabled": "Ligado", "settings.bounces.folder": "Pasta", "settings.bounces.folderHelp": "Nome da pasta IMAP para procurar. E.g.: Inbox.", "settings.bounces.invalidScanInterval": "Intervalo de procura de bounces deve ser, no mínimo, 1 minuto.", "settings.bounces.name": "<PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Intervalo de procura", "settings.bounces.scanIntervalHelp": "Intervalo de procura de bounces na caixa de correio de bounces (s para segundos, m para minutos).", "settings.bounces.sendgridKey": "Chave do <PERSON>rid", "settings.bounces.type": "Tipo", "settings.bounces.username": "Nome de utilizador", "settings.confirmRestart": "Tenha a certeza que as campanhas em curso estão em pausa. Reiniciar?", "settings.duplicateMessengerName": "Nome duplicado do mensageiro: {name}", "settings.errorEncoding": "Erro de definições de codificação: {error}", "settings.errorNoSMTP": "Pelo menos um bloco SMTP deve estar ativo", "settings.general.adminNotifEmails": "Emails de notificação de administração", "settings.general.adminNotifEmailsHelp": "Lista separada por vírgulas dos endereços de email para os quais devem ser enviadas notificações de administração como updates importantes, conclusão de campanhas, falhas, etc.", "settings.general.checkUpdates": "<PERSON><PERSON>rar <PERSON>ual<PERSON>", "settings.general.checkUpdatesHelp": "Procurar e notificar periodicamente por novas versões da aplicação.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publicar campanhas em que o arquivo está ligado no site público.", "settings.general.enablePublicSubPage": "Ativar página de subscrição pública", "settings.general.enablePublicSubPageHelp": "Mostrar uma página de subscrição pública com todas as listas públicas para as pessoas se subscreverem.", "settings.general.faviconURL": "URL do Favicon", "settings.general.faviconURLHelp": "(Opcional) URL completo do favicon estático para ser mostrado nas janelas do utilizador, como a página de cancelamento de subscrição.", "settings.general.fromEmail": "Endereço `de` padrão", "settings.general.fromEmailHelp": "Email `de` padrão para usar em campanhas. Este pode ser alterado por campanha.", "settings.general.language": "Linguagem", "settings.general.logoURL": " Root URL", "settings.general.logoURLHelp": "(Opcional) URL completo do logotipo para ser mostrado nas janelas do utilizador, como a página de cancelamento de subscrição.", "settings.general.name": "G<PERSON>", "settings.general.rootURL": "URL base", "settings.general.rootURLHelp": "URL público da instalação (sem barra final).", "settings.general.sendOptinConfirm": "Enviar confirmação de adesão", "settings.general.sendOptinConfirmHelp": "When new subscribers signup or are added via the admin form, send an opt-in confirmation e-mail.", "settings.general.siteName": "Nome do site", "settings.invalidMessengerName": "Nome de mensageiro inválido.", "settings.mailserver.authProtocol": "Protocolo Autenticação", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "O endereço host do servidor SMTP", "settings.mailserver.idleTimeout": "Tempo limite de inatividade", "settings.mailserver.idleTimeoutHelp": "Tempo a esperar por nova atividade numa conexão antes de a fechar e removê-la da pool (s para segundo, m para minuto).", "settings.mailserver.maxConns": "<PERSON><PERSON> <PERSON>", "settings.mailserver.maxConnsHelp": "Número máximo de conexões simultâneas ao servidor SMTP.", "settings.mailserver.password": "Palavra-passe", "settings.mailserver.passwordHelp": "Escreve aqui para alterar", "settings.mailserver.port": "Porta", "settings.mailserver.portHelp": "Porta do servidor SMTP", "settings.mailserver.skipTLS": "Saltar verificação TLS", "settings.mailserver.skipTLSHelp": "Saltar verificação do hostname no certificado TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Ativar STARTTLS.", "settings.mailserver.username": "Nome de utilizador", "settings.mailserver.waitTimeout": "Tempo limite de espera", "settings.mailserver.waitTimeoutHelp": "Tempo a esperar por nova atividade numa conexão antes de a fechar e removê-la da pool (s para segundo, m para minuto).", "settings.media.provider": "Fornecedor", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "<PERSON>in<PERSON> do <PERSON>", "settings.media.s3.bucketPathHelp": "Caminho dentro do bucket para enviar ficheiros. Padrão é /", "settings.media.s3.bucketType": "Tipo de <PERSON>", "settings.media.s3.bucketTypePrivate": "Privado", "settings.media.s3.bucketTypePublic": "Público", "settings.media.s3.key": "Chave de acesso AWS", "settings.media.s3.publicURL": "URL público customizado (opcional)", "settings.media.s3.publicURLHelp": "Domínio S3 customizado a usar para links de imagens invés do URL do backend S3 pré-definido.", "settings.media.s3.region": "Região", "settings.media.s3.secret": "<PERSON><PERSON><PERSON> de acesso AWS", "settings.media.s3.uploadExpiry": "Validade do upload", "settings.media.s3.uploadExpiryHelp": "(Opcional) Especifica TTL (em segundos) para o URL pré-assinado gerado. Apenas aplicável a buckets privados (s, m, h, d para segundos, minutos, horas e dias).", "settings.media.s3.url": "URL do backend S3", "settings.media.s3.urlHelp": "Apenas alterar quando um backend customizado compatível com S3, como Minio, está em uso.", "settings.media.title": "Upload de mídia", "settings.media.upload.path": "<PERSON><PERSON><PERSON> de upload", "settings.media.upload.pathHelp": "<PERSON><PERSON><PERSON> para a pasta onde será enviada a mídia.", "settings.media.upload.uri": "URI de envio", "settings.media.upload.uriHelp": "URI de envio que é visível ao mundo exterior. Toda a mídia enviada para o upload_path será publicamente acessível em {root_url}/{}, por exemplo, https://listmonk.oteusite.com/uploads.", "settings.messengers.maxConns": "<PERSON><PERSON> <PERSON>", "settings.messengers.maxConnsHelp": "Número máximo de conexões simultâneas ao servidor.", "settings.messengers.messageSaved": "Definições guardadas. Recarregando aplicação ...", "settings.messengers.name": "Mensageiros", "settings.messengers.nameHelp": "eg: o-meu-sms. Alfanumérico / traço.", "settings.messengers.password": "Palavra-passe", "settings.messengers.retries": "Tentativas", "settings.messengers.retriesHelp": "Número de vezes para tentar novamente quando uma mensagem falha.", "settings.messengers.skipTLSHelp": "Saltar verificação do hostname no certificado TLS.", "settings.messengers.timeout": "Tempo limite de inatividade", "settings.messengers.timeoutHelp": "Tempo a esperar por nova atividade numa conexão antes de a fechar e removê-la da pool (s para segundo, m para minuto).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "URL base do servidor Postback.", "settings.messengers.username": "Nome de utilizador", "settings.needsRestart": "Definições alteradas. Pause todas as campanhas em curso e reinicie a aplicação", "settings.performance.batchSize": "Tamanho do lote", "settings.performance.batchSizeHelp": "O número de subscritores para ir buscar à base de dados numa só iteração. Cada iteração vai buscar subscritores à base de dados, envia-lhe mensagens, e depois segue para a nova iteração para ir buscar o lote seguinte. Isto deve idealmente ser maior do que a máxima taxa de transferência alcançável (simultaneidade * taxa de mensagens).", "settings.performance.concurrency": "Simultaneidade", "settings.performance.concurrencyHelp": "Número máximo de workers (threads) concurrentes que irão tentar enviar as mensagens simultaneamente.", "settings.performance.maxErrThreshold": "Limite máxi<PERSON> de er<PERSON>", "settings.performance.maxErrThresholdHelp": "O número de erros (eg: timeouts SMTP ao enviar um email) uma campanha em curso pode tolerar antes de ser colocada em pausa para investigação manual ou intervenção. Colocar a 0 para nunca pausar.", "settings.performance.messageRate": "Taxa de mensagens", "settings.performance.messageRateHelp": "Número máximo de mensagens para serem enviadas por segundo num worker. Se simultaneidade = 10 e taxa de mensagens = 10, então até 10x10=100 mensagens podem ser enviadas por segundo. Isto, junto com a simultaneidade, deve ser ajustado de forma a manter o número de mensagens a ser enviadas por segundo abaixo do limite máximo do servidor, se existir.", "settings.performance.name": "<PERSON><PERSON><PERSON><PERSON>", "settings.performance.slidingWindow": "Ativar o limite de janela", "settings.performance.slidingWindowDuration": "Duração", "settings.performance.slidingWindowDurationHelp": "Duração do periodo de limite de janela (m para minuto, h para hora).", "settings.performance.slidingWindowHelp": "Limitar o número total de mensagens que é enviado num determinado periodo. Ao alcançar este limite, as mensagens são impedidas de ser enviadas até ao fim da janela temporária.", "settings.performance.slidingWindowRate": "<PERSON><PERSON> men<PERSON>", "settings.performance.slidingWindowRateHelp": "Número máximo de mensagens para enviar na duração da janela.", "settings.privacy.allowBlocklist": "Permitir lista de bloqueio", "settings.privacy.allowBlocklistHelp": "Permitir ao subscritores cancelar a subscrição de todas as listas de emails e marcar-se como bloqueados?", "settings.privacy.allowExport": "Permitir <PERSON>", "settings.privacy.allowExportHelp": "Permitir aos subscritores exportar os dados coletados neles mesmos?", "settings.privacy.allowPrefs": "Permitir alterações de preferências", "settings.privacy.allowPrefsHelp": "Permitir que os subscritores alterem as suas preferências, como o seu nome e a sua subscrição às diversas listas.", "settings.privacy.allowWipe": "Permit<PERSON> elimina<PERSON> dados", "settings.privacy.allowWipeHelp": "Permitir aos subscritores eliminar todos os seus dados, incluindo as suas subscrições, da base de dados. Visualizações de campanhas e cliques em links também são removidos enquanto visualizações e contagem de clicks permanecem (sem nenhum subscritor associado) para que as estatísticas não sejam afetadas.", "settings.privacy.domainBlocklist": "Lista de domínios bloqueados", "settings.privacy.domainBlocklistHelp": "Endereços de email com estes domínios não podem efetuar subscrições. Insira um domínio por linha, e.g. somesite.com", "settings.privacy.individualSubTracking": "Tracking individual de subscritores", "settings.privacy.individualSubTrackingHelp": "Track visualizações e clicked ao nível do subscritor. <PERSON><PERSON><PERSON>, visualizações e track de clicks continuam, mas sem estarem associadas a nenhum subscritor.", "settings.privacy.listUnsubHeader": "Incluir header `List-Unsubscribe`", "settings.privacy.listUnsubHeaderHelp": "Incluir headers de cancelamento de subscrição que permite aos clientes de email permitir ao utilizadores cancelar a subscrição num único clique.", "settings.privacy.name": "Privacidade", "settings.restart": "Reiniciar", "settings.smtp.customHeaders": "Headers customizados", "settings.smtp.customHeadersHelp": "Array opcional de headers de email a incluir em todas as mensagens enviadas deste servidor. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Ativo", "settings.smtp.heloHost": "Hostname HELO", "settings.smtp.heloHostHelp": "Opcional. Alguns servidores SMTP necessitam de um FQDN no hostname. <PERSON><PERSON><PERSON>, HELLOs usam `localhost`. Coloca um hostname customizado se for necessario.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Tentativas", "settings.smtp.retriesHelp": "Número de vezes para tentar novamente quando uma mensagem falha.", "settings.smtp.sendTest": "Enviar e-mail", "settings.smtp.setCustomHeaders": "Colocar headers customizados", "settings.smtp.testConnection": "<PERSON><PERSON>", "settings.smtp.testEnterEmail": "Insira a palavra-passe para testar", "settings.smtp.toEmail": "E-mail do destinatário", "settings.title": "Definições", "settings.updateAvailable": "A nova versão {version} está disponível.", "subscribers.advancedQuery": "Avançado", "subscribers.advancedQueryHelp": "Expressão SQL parcial para consultar atributos de subscritores", "subscribers.attribs": "Atributos", "subscribers.attribsHelp": "Atributos estão definidos como uma mapa JSON, por exemplo:", "subscribers.blocklistedHelp": "Subscritores bloqueados nunca irão receber emails.", "subscribers.confirmBlocklist": "Adicionar {num} subscritor(es) à lista de bloqueio?", "subscribers.confirmDelete": "Eliminar {num} subscritor(es)?", "subscribers.confirmExport": "Exportar {num} subscritor(es)?", "subscribers.domainBlocklisted": "O domínio do e-mail está bloqueado.", "subscribers.downloadData": "<PERSON><PERSON><PERSON><PERSON> dados", "subscribers.email": "E-mail", "subscribers.emailExists": "E-mail já existe.", "subscribers.errorBlocklisting": "Erro ao bloquear subscritores: {error}", "subscribers.errorNoIDs": "<PERSON>ão foram dados IDs.", "subscribers.errorNoListsGiven": "Não foram dadas listas.", "subscribers.errorPreparingQuery": "Erro ao preparar query dos subscritores: {error}", "subscribers.errorSendingOptin": "Erro ao enviar email opt-in.", "subscribers.export": "Exportar", "subscribers.invalidAction": "Ação inválida.", "subscribers.invalidEmail": "<PERSON>ail in<PERSON>.", "subscribers.invalidJSON": "JSON inválido nos atributos.", "subscribers.invalidName": "Nome inválido.", "subscribers.listChangeApplied": "Alteração à lista aplicada.", "subscribers.lists": "Listas", "subscribers.listsHelp": "Listas nas quais o/a subscritor/a cancelou a sua subscrição não podem ser removidas.", "subscribers.listsPlaceholder": "Listas a subscrever", "subscribers.manageLists": "<PERSON><PERSON><PERSON> <PERSON>as", "subscribers.markUnsubscribed": "Marcar como não subscrito", "subscribers.newSubscriber": "Novo subscritor", "subscribers.numSelected": "{num} subscritor(es) selecionados", "subscribers.optinSubject": "Confirmar subscrição", "subscribers.preconfirm": "Pre-confirm subscriptions", "subscribers.preconfirmHelp": "Não enviar e-mails de adesão e marcar todas as subscrições a listas como 'subscrito'.", "subscribers.query": "Consulta", "subscribers.queryPlaceholder": "E-mail ou nome", "subscribers.reset": "Repor", "subscribers.selectAll": "Selecionar todos os {num}", "subscribers.sendOptinConfirm": "Enviar confirmação de adesão", "subscribers.sentOptinConfirm": "Confirmação de adesão enviada", "subscribers.status.blocklisted": "Bloqueados", "subscribers.status.confirmed": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.status.enabled": "Ativo", "subscribers.status.subscribed": "Subscrito", "subscribers.status.unconfirmed": "<PERSON>ão confirmado", "subscribers.status.unsubscribed": "Não subscrito", "subscribers.subscribersDeleted": "{num} subscritor(es) eliminados", "templates.cantDeleteDefault": "Não é possível eliminar o template padrão", "templates.default": "Padrão", "templates.dummyName": "Campanha <PERSON>", "templates.dummySubject": "<PERSON><PERSON><PERSON> da campanha fictícia", "templates.errorCompiling": "Erro ao compilar template: {error}", "templates.errorRendering": "Erro ao renderizar mensagem: {error}", "templates.fieldInvalidName": "<PERSON>an<PERSON> inválido para o nome.", "templates.makeDefault": "<PERSON>ar como padrão", "templates.newTemplate": "Novo template", "templates.placeholderHelp": "O placeholder {placeholder} deve aparecer exatamente uma vez no template.", "templates.preview": "Pré-visualização", "templates.rawHTML": "HTML Simples", "templates.subject": "<PERSON><PERSON><PERSON>", "users.login": "Entrar", "users.logout": "<PERSON><PERSON>"}