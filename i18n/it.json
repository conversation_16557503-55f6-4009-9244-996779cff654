{"_.code": "it", "_.name": "Italiano (it)", "admin.errorMarshallingConfig": "Errore durante la lettura della configurazione: {error}", "analytics.count": "Count", "analytics.fromDate": "Da", "analytics.invalidDates": "Invalid `from` or `to` dates.", "analytics.isUnique": "I totali sono per ogni singola suscrizione.", "analytics.links": "Links", "analytics.nonUnique": "I totali non sono per ogni singola suscrizione perchè le opzioni d'inseguimento individuali non sono statte attivate.", "analytics.title": "<PERSON><PERSON><PERSON>", "analytics.toDate": "a", "bounces.source": "<PERSON><PERSON><PERSON>", "bounces.unknownService": "<PERSON><PERSON><PERSON>.", "bounces.view": "Visualizza i rimbalzi", "campaigns.addAltText": "Aggiungere un messaggio sostitutivo in testo semplice", "campaigns.archive": "Archivio", "campaigns.archiveEnable": "Rendere pubblico l'archivio", "campaigns.archiveHelp": "Pubblicare i messagi delle campagne (avviate, pausate, finite) nel archivio pubblico.", "campaigns.archiveMeta": "Metadati della campagna", "campaigns.archiveMetaHelp": "Dati di essempio di suscrizioni per essere usate nei messagi publici, inclossi nome, posta elettronica, e qualche altro attributo ozionale `{}` che sia fatto servire nell messagio della campagna o pure della sagoma.", "campaigns.cantUpdate": "Impossibile aggiornare una campagna in corso o già effettuata.", "campaigns.clicks": "Clic", "campaigns.confirmDelete": "Cancellare {nome}", "campaigns.confirmSchedule": " Questa campagna inizierà automaticamente alla data e all'ora programmate. Programmare adesso?", "campaigns.confirmSwitchFormat": "Il contenuto può perdere la sua formattazione. Continuare?", "campaigns.content": "<PERSON><PERSON><PERSON>", "campaigns.contentHelp": "Contenuto qui", "campaigns.continue": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.copyOf": "<PERSON><PERSON> <PERSON> {name}", "campaigns.customHeadersHelp": "Lista di header personalizzati da allegare ai messaggi in uscita. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Data e ora", "campaigns.ended": "<PERSON><PERSON>", "campaigns.errorSendTest": "Errore durante il test di invio: {error}", "campaigns.fieldInvalidBody": "Errore durante la compilazione del contenuto della campagna: {error}", "campaigns.fieldInvalidFromEmail": "`Mittente` non valido.", "campaigns.fieldInvalidListIDs": "ID della lista non valido.", "campaigns.fieldInvalidMessenger": "Strumento di messaggeria sconosciuto {name}.", "campaigns.fieldInvalidName": "Lunghezza del nome non valida.", "campaigns.fieldInvalidSendAt": "La data programmata deve essere futura.", "campaigns.fieldInvalidSubject": "Lunghezza dell'oggetto non valida.", "campaigns.formatHTML": "Format HTML", "campaigns.fromAddress": "Mittente", "campaigns.fromAddressPlaceholder": "<PERSON>o nome <<EMAIL>>", "campaigns.invalid": "Campagna non valida", "campaigns.invalidCustomHeaders": "Header personal<PERSON><PERSON><PERSON> non validi: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "È necessaria una data per programmare la campagna.", "campaigns.newCampaign": "Nuova campagna", "campaigns.noKnownSubsToTest": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>to conos<PERSON>uto da testare.", "campaigns.noOptinLists": "Nessuna lista opt-in trovata per poter creare una campagna.", "campaigns.noSubs": "Non esiste alcun iscritto nelle liste selezionate per creare la campagna.", "campaigns.noSubsToTest": "Non c'è alcun iscritto a cui rivolgersi.", "campaigns.notFound": "Campagna introvabile.", "campaigns.onlyActiveCancel": "Solo le campagne attive possono essere annullate.", "campaigns.onlyActivePause": "Solo le campagne attive possono essere messe in pausa.", "campaigns.onlyDraftAsScheduled": "Solo le bozze delle campagne possono essere programmate.", "campaigns.onlyPausedDraft": "Solo le bozze e le campagne in pausa possono essere lanciate.", "campaigns.onlyScheduledAsDraft": "Solo le campagne pianificate possono essere registrate come bozze.", "campaigns.pause": "Pausa", "campaigns.plainText": "Testo semplice", "campaigns.preview": "Anteprima", "campaigns.progress": "Avanzamento", "campaigns.queryPlaceholder": "Nome o oggetto", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML semplice", "campaigns.removeAltText": "Cancellare il messaggio sostitutivo in testo semplice", "campaigns.richText": "Testo formattato", "campaigns.schedule": "Programmare la campagna", "campaigns.scheduled": "Programmata", "campaigns.send": "Inviare", "campaigns.sendLater": "Inviare più tardi", "campaigns.sendTest": "Inviare un messaggio di testo", "campaigns.sendTestHelp": "Per aggiungere più destinatari, premi Enter dopo aver aggiunto un indirizzo. Gli indirizzi devono appartenere a iscritti esistenti.", "campaigns.sendToLists": "Liste da inviare a", "campaigns.sent": "Inviato", "campaigns.start": "Lanciare la campagna", "campaigns.started": "\"{name}\" ha cominciato", "campaigns.startedAt": "Co<PERSON><PERSON><PERSON>", "campaigns.stats": "Statistiche", "campaigns.status.cancelled": "<PERSON><PERSON><PERSON>", "campaigns.status.draft": "<PERSON><PERSON>", "campaigns.status.finished": "<PERSON><PERSON>", "campaigns.status.paused": "In pausa", "campaigns.status.running": "In corso", "campaigns.status.scheduled": "Programmata", "campaigns.statusChanged": "\"{name}\" e {status}", "campaigns.subject": "<PERSON><PERSON><PERSON>", "campaigns.testDisabled": "Inserire la parola chiave (password) per collaudare", "campaigns.testEmails": "Emails di prova", "campaigns.testSent": "Messaggio di prova inviato", "campaigns.timestamps": "Marcatura temporale ", "campaigns.trackLink": "Link d'intracciamento", "campaigns.views": "Visualizzazioni", "dashboard.campaignViews": "Visualizzazioni della campagna", "dashboard.linkClicks": "Clic sui link", "dashboard.messagesSent": "Messaggi inviati", "dashboard.orphanSubs": "<PERSON><PERSON><PERSON>", "email.data.info": "È stato aggiunto un file JSON contenente l'insieme dei tuoi dati salvati. Può essere visualizzato in un editore di testo.", "email.data.title": "I tuoi dati", "email.optin.confirmSub": "Confermare l'iscrizione", "email.optin.confirmSubHelp": "Conferma la tua iscrizione cliccando sul pulsante qui sotto.", "email.optin.confirmSubInfo": "Sei stato aggiunto alle liste seguenti:", "email.optin.confirmSubTitle": "Confermare l'iscrizione", "email.optin.confirmSubWelcome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email.optin.privateList": "Lista privata", "email.status.campaignReason": "Ragione", "email.status.campaignSent": "Inviato", "email.status.campaignUpdateTitle": "Aggiornamento della campagna", "email.status.importFile": "Archivio", "email.status.importRecords": "<PERSON><PERSON><PERSON><PERSON>", "email.status.importTitle": "Importare l'aggiornamento", "email.status.status": "Stato", "email.unsub": "Cancella iscrizione", "email.unsubHelp": "Non desideri ricevere queste mail?", "email.viewInBrowser": "Visualizare nel navigatore", "forms.formHTML": "Formulario HTML", "forms.formHTMLHelp": "Fai servire questo codice HTML per visualizzare un formulario d'iscrizione su una pagina Web esterna.  Il formulario deve avere il campo `email` e uno o più campi `l` (liste UUID). Il campo nome è facoltativo.", "forms.noPublicLists": "Non ci sono liste pubbliche per generare un formulario.", "forms.publicLists": "Liste pubbliche", "forms.publicSubPage": "Pagina di iscrizione pubblica", "forms.selectHelp": "Seleziona le liste da aggiungere al formulario.", "forms.title": "Formulari", "globals.buttons.add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.buttons.addNew": "Aggiungi nuovo", "globals.buttons.back": "Indietro", "globals.buttons.cancel": "<PERSON><PERSON><PERSON>", "globals.buttons.clone": "Clona", "globals.buttons.close": "<PERSON><PERSON>", "globals.buttons.continue": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.delete": "Cancellare", "globals.buttons.deleteAll": "Cancellare tutto/e", "globals.buttons.edit": "Modifica", "globals.buttons.enabled": "At<PERSON><PERSON>a", "globals.buttons.insert": "Inserire", "globals.buttons.learnMore": "Per saperne di più", "globals.buttons.more": "Di piú", "globals.buttons.new": "Nuovo", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Cancellare", "globals.buttons.save": "<PERSON><PERSON><PERSON>", "globals.buttons.saveChanges": "<PERSON><PERSON><PERSON> le modifiche", "globals.days.0": "dom", "globals.days.1": "dom", "globals.days.2": "lun", "globals.days.3": "mar", "globals.days.4": "mer", "globals.days.5": "gio", "globals.days.6": "ven", "globals.days.7": "sab", "globals.fields.createdAt": "Creato il ", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Nome", "globals.fields.status": "Stato", "globals.fields.type": "Tipo", "globals.fields.updatedAt": "Aggiornato il", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Sei sicuro?", "globals.messages.confirmDiscard": "<PERSON><PERSON><PERSON> le modifiche?", "globals.messages.created": "\"{name}\" creato", "globals.messages.deleted": "\"{name}\" cancellato", "globals.messages.deletedCount": "{name} ({num}) cancellato", "globals.messages.done": "<PERSON><PERSON>", "globals.messages.emptyState": "<PERSON><PERSON> da visualizzare", "globals.messages.errorCreating": "Errore durante la creazione di {name}: {error}", "globals.messages.errorDeleting": "Errore durante la cancellazione di {name}: {error}", "globals.messages.errorFetching": "Errore durante il recupero di {name}: {error}", "globals.messages.errorInvalidIDs": "Una o più credenziali fornite non valide: {error}", "globals.messages.errorUUID": "Errore durante la generazione dell'UUID: {error}", "globals.messages.errorUpdating": "Errore durante l'aggiornamento di {name}: {error}", "globals.messages.internalError": "Errore interno nel server", "globals.messages.invalidData": "Dati non validi", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID non valido", "globals.messages.invalidUUID": "UUID non valido", "globals.messages.missingFields": "Valore/i mancante/i: {name}", "globals.messages.notFound": "{name} introvabile", "globals.messages.passwordChange": "Inserisci un valore da modificare", "globals.messages.updated": "\"{name}\" aggiornato", "globals.months.1": "Gen", "globals.months.10": "<PERSON><PERSON>", "globals.months.11": "Nov", "globals.months.12": "Dic", "globals.months.2": "Feb", "globals.months.3": "Mar", "globals.months.4": "Apr", "globals.months.5": "Mag", "globals.months.6": "<PERSON><PERSON>", "globals.months.7": "<PERSON>g", "globals.months.8": "Ago", "globals.months.9": "Set", "globals.states.off": "<PERSON><PERSON><PERSON>", "globals.terms.all": "Tutti/e", "globals.terms.analytics": "<PERSON><PERSON><PERSON>", "globals.terms.bounce": "Rimbalzo | Rimbalzi", "globals.terms.bounces": "<PERSON><PERSON><PERSON><PERSON>", "globals.terms.campaign": "Campagna | Campagne", "globals.terms.campaigns": "Campagne", "globals.terms.dashboard": "<PERSON><PERSON><PERSON>", "globals.terms.day": "Giorno | Giorni", "globals.terms.hour": "Hora | Hore", "globals.terms.list": "Lista | Liste", "globals.terms.lists": "Liste", "globals.terms.media": "Media | Media", "globals.terms.messenger": "Strumento di messaggeria | Strumenti di messaggeria", "globals.terms.messengers": "Strumento di messaggeria", "globals.terms.minute": "Minuto | Minuti", "globals.terms.month": "Mese | Mesi", "globals.terms.second": "Secondo | Secondi", "globals.terms.settings": "Impostazioni", "globals.terms.subscriber": "Iscritto | Iscritti", "globals.terms.subscribers": "<PERSON><PERSON><PERSON><PERSON>", "globals.terms.subscriptions": "Iscrizione | Iscrizioni", "globals.terms.tag": "Etichetta | Etichette", "globals.terms.tags": "<PERSON><PERSON><PERSON><PERSON>", "globals.terms.template": "Modello | Modelli", "globals.terms.templates": "<PERSON><PERSON>", "globals.terms.tx": "Transazionale | Transazionali", "globals.terms.year": "Anno | Anni", "import.alreadyRunning": "Un'importazione è già in corso. Aspetta che finisca o interrompila prima di riprovare.", "import.blocklist": "Lista degli indirizzi bloccati", "import.csvDelim": "Delimitatore CSV", "import.csvDelimHelp": "Il delimitatore predefinito è la virgola.", "import.csvExample": "Esempio di CSV semplice", "import.csvFile": "Archivio CSV o ZIP", "import.csvFileHelp": "Clicca o trascina qui un file CSV o ZIP", "import.errorCopyingFile": "Errore durante la copia del file: {error}", "import.errorProcessingZIP": "Errore durante il trattamento del file ZIP: {error}", "import.errorStarting": "Errore durante l'avvio dell'importazione: {error}", "import.importDone": "<PERSON><PERSON>", "import.importStarted": "L'importazione è inziata", "import.instructions": "Istruzioni", "import.instructionsHelp": "Carica un archivio CSV o ZIP contenente un solo CSV per importare iscritti in massa. Il file CSV deve avere le seguenti intestazioni con i nomi delle colonne esatti. Gli attributi (facoltativi) devono essere delle stringhe JSON valide tra virgolette doppie.", "import.invalidDelim": "Il delimitatore deve essere un singolo carattere.", "import.invalidFile": "Archivio non valido: {error}", "import.invalidMode": "Modalità non valida", "import.invalidParams": "Parametri non validi: {error}", "import.invalidSubStatus": "Status della/e iscrizione/i non valida/e", "import.listSubHelp": "Liste a cui iscriversi.", "import.mode": "Modalità", "import.overwrite": "Sovrascrivere?", "import.overwriteHelp": "Sostituire il nome e gli attributi degli iscritti esistenti?", "import.recordsCount": "{num} / {total} salvataggi", "import.stopImport": "Interrompere l'importazione", "import.subscribe": "<PERSON><PERSON><PERSON><PERSON>", "import.title": "<PERSON><PERSON><PERSON><PERSON>", "import.upload": "Caricare", "lists.confirmDelete": "Sei sicuro? Questo non cancella gli iscritti", "lists.confirmSub": "Confer<PERSON> gli iscritti di {name}", "lists.invalidName": "Nome errato", "lists.newList": "Nuova lista", "lists.optin": "Iscrizione", "lists.optinHelp": "Opt-in invio doppio di una mail a l'iscritto richiedendo la sua conferma. Per le liste opt-in doppio, le campagne sono inviate solo agli iscritti che hanno confermato.", "lists.optinTo": "<PERSON><PERSON><PERSON><PERSON> {name}", "lists.optins.double": "Opt-in doppio", "lists.optins.single": "Opt-in semplice", "lists.sendCampaign": "Inviare la campagna", "lists.sendOptinCampaign": "Inviare una campagna opt-in", "lists.type": "Tipo", "lists.typeHelp": "Le liste pubbliche sono libere d'accesso in abbonamento e i loro nomi sono visibili sulle pagine pubbliche come ad esempio la pagina della gestione degli abbonamenti.", "lists.types.private": "<PERSON><PERSON><PERSON><PERSON>", "lists.types.public": "Pubblico", "logs.title": "Logs", "maintenance.help": "Alqune azzione possono impiegare un po di tempo dovuto alla quantita di dati da processare.", "maintenance.maintenance.unconfirmedOptins": "<PERSON><PERSON><PERSON><PERSON><PERSON> `opt-in` da confermare", "maintenance.olderThan": "An<PERSON><PERSON><PERSON> da", "maintenance.title": "Manutenzione", "maintenance.unconfirmedSubs": "<PERSON><PERSON><PERSON><PERSON><PERSON> `opt-in` da confermare in attesa da piú di {name} gior<PERSON>.", "media.errorReadingFile": "Errore di lettura del file: {error}", "media.errorResizing": "Errore di ridimensionamento dell'immagine: {error}", "media.errorSavingThumbnail": "Errore durante il salvataggio dell'immagine: {error}", "media.errorUploading": "Errore durante il caricamento del file: {error}", "media.invalidFile": "File non valido: {error}", "media.title": "Media", "media.unsupportedFileType": "Tipo di file non supportato ({type})", "media.upload": "Caricare", "media.uploadHelp": "Seleziona o trascina qui una o più immagini", "media.uploadImage": "Caricare l'immagine", "menu.allCampaigns": "<PERSON><PERSON> le campagne", "menu.allLists": "<PERSON><PERSON> le liste", "menu.allSubscribers": "<PERSON><PERSON> gli <PERSON>ti", "menu.dashboard": "<PERSON><PERSON>", "menu.forms": "Formulari", "menu.import": "Importare", "menu.logs": "Logs", "menu.maintenance": "Manutenzione", "menu.media": "Media", "menu.newCampaign": "Creare nuovo", "menu.settings": "Impostazioni", "public.archiveEmpty": "Non ci sono ancora messagi achiviti.", "public.archiveTitle": "Archivio <PERSON> mailing-list", "public.blocklisted": "Cancellato permanentemente.", "public.campaignNotFound": "Newsletter impossibile da trovare.", "public.confirmOptinSubTitle": "Confermare l'iscrizione", "public.confirmSub": "Confermare l'iscrizione", "public.confirmSubInfo": "Sei stato aggiunto alle liste seguenti:", "public.confirmSubTitle": "<PERSON><PERSON><PERSON>", "public.dataRemoved": "I tuoi abbonamenti e tutti i dati associati sono stati cancellati.", "public.dataRemovedTitle": "<PERSON><PERSON>", "public.dataSent": "I tuoi dati ti sono stati trasmessi via mail.", "public.dataSentTitle": "<PERSON><PERSON> via mail", "public.errorFetchingCampaign": "Errore durante il recupero della mail.", "public.errorFetchingEmail": "Messaggio mail impossibile da trovare", "public.errorFetchingLists": "Errore durante il recupero delle liste. Per favore, rip<PERSON>a.", "public.errorProcessingRequest": "Errore durante la gestione della richiesta. Per <PERSON>, rip<PERSON><PERSON>.", "public.errorTitle": "Errore", "public.invalidFeature": "Questa funzione non è disponibile.", "public.invalidLink": "Link non valido", "public.managePrefs": "Gestiona l'impostazinoni", "public.managePrefsUnsub": "Non marcare per cancellare l'abonamento.", "public.noListsAvailable": "Nessuna lista disponibile per l'iscrizione.", "public.noListsSelected": "Nessuna lista valida selezionata per l'iscrizione.", "public.noSubInfo": "Non ci sono iscrizioni da confermare.", "public.noSubTitle": "Nessuna iscrizione", "public.notFoundTitle": "Non trovato", "public.prefsSaved": "Salvate le tue l'impostazioni.", "public.privacyConfirmWipe": "Sei sicuro di voler cancellare in modo permanente tutti i tuoi dati d'iscrizione?", "public.privacyExport": "Esporta i tuoi dati", "public.privacyExportHelp": "Una copia dei tuoi dati ti sarà trasmessa via mail.", "public.privacyTitle": "Privacy e dati", "public.privacyWipe": "Cancella i tuoi dati", "public.privacyWipeHelp": "Cancella in modo permanente tutte le tue iscrizioni e relativi dari dal database.", "public.sub": "<PERSON><PERSON><PERSON><PERSON>", "public.subConfirmed": "Iscrizione avvenuta con successo.", "public.subConfirmedTitle": "Confermato", "public.subName": "Nome (facoltativo)", "public.subNotFound": "Iscrizione impossibile da trovare.", "public.subOptinPending": "Una mail per confermare l'iscrizione è stata inviata alla tua casella di posta.", "public.subPrivateList": "Lista privata", "public.subTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.unsub": "Cancella iscrizione", "public.unsubFull": "Cancella iscrizione anche per tutte le mail future.", "public.unsubHelp": "Vuoi cancellare l'iscrizione da questa newsletter?", "public.unsubTitle": "Cancella iscrizione", "public.unsubbedInfo": "La cancellazione è avvenuta con successo.", "public.unsubbedTitle": "Iscrizione annullata", "public.unsubscribeTitle": "Cancella l'iscrizione dalla newsletter", "settings.appearance.adminHelp": "Custom CSS to apply to the admin UI.", "settings.appearance.adminName": "Aministrazione", "settings.appearance.customCSS": "CSS personalizato", "settings.appearance.customJS": "JavaScript personalizato", "settings.appearance.name": "Apparenza", "settings.appearance.publicHelp": "CSS e JavaScript personalizzati da applicare alle pagine pubbliche.", "settings.appearance.publicName": "Publico", "settings.bounces.action": "<PERSON><PERSON><PERSON>", "settings.bounces.blocklist": "Elenco bloccato", "settings.bounces.count": "Numero di rimblazi", "settings.bounces.countHelp": "Numero di rimbalzi per iscritto", "settings.bounces.delete": "Can<PERSON><PERSON>", "settings.bounces.enable": "Abilita il processamento dei rimbalzi", "settings.bounces.enableMailbox": "Abilita la cassella di posta per i rimbalzi", "settings.bounces.enableSES": "Attiva SES", "settings.bounces.enableSendgrid": "Attiva SendGrid", "settings.bounces.enableWebhooks": "Attiva bounce webhooks", "settings.bounces.enabled": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.folder": "<PERSON><PERSON><PERSON>", "settings.bounces.folderHelp": "Nome della cartella IMAP da analizzare. Ad esempio: Posta in arrivo.", "settings.bounces.invalidScanInterval": "L'intervallo di scansione dei rimbalzi deve essere di almeno 1 minuto.", "settings.bounces.name": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Intervallo di scansione", "settings.bounces.scanIntervalHelp": "Intervallo con cui la mailbox di rimbalzo deve essere scansionata per i rimbalzi (s per secondo, m per minuto).", "settings.bounces.sendgridKey": "<PERSON><PERSON>", "settings.bounces.type": "Tipo", "settings.bounces.username": "Nome utente", "settings.confirmRestart": "<PERSON>ic<PERSON><PERSON> che le campagne sono in pausa. Riavviare?", "settings.duplicateMessengerName": "Nome in messaggeria doppio: {name}", "settings.errorEncoding": "Errore durante la codifica dei parametri: {error}", "settings.errorNoSMTP": "<PERSON> attivare almeno un blocco SMTP", "settings.general.adminNotifEmails": "Mail di notifica amministratore", "settings.general.adminNotifEmailsHelp": "Lista indirizzi mail separati da virgole ai quali saranno inviate notifiche di amministrazione come gli aggiornamenti di importazione, la fine della campagna, eventuali problemi ecc.", "settings.general.checkUpdates": "Controlla le attualizazioni.", "settings.general.checkUpdatesHelp": "Rutinariamente controllare se ci sono nuove versioni dell'app e notificami.", "settings.general.enablePublicArchive": "Abilita la pagina publica di archivio delle mail", "settings.general.enablePublicArchiveHelp": "Rendere pubbliche le campagne in qui l'archivio pubblico nella pagina web è stato abilitato.", "settings.general.enablePublicSubPage": "Attiva la pagina di iscrizione pubblica", "settings.general.enablePublicSubPageHelp": "Visualizza una pagina di iscrizione pubblica con tutte le liste pubbliche a cui è possibile iscriversi.", "settings.general.faviconURL": "URL della favicon", "settings.general.faviconURLHelp": "(Facoltativo) URL completo della favicon statica visibile dall'utente, come sulla pagina per annullare l'iscrizione.", "settings.general.fromEmail": "Indirizzo mail `Mittente` predefinito", "settings.general.fromEmailHelp": "Indirizzo mail `Mittente` nelle mail delle campagne uscenti visibile in modo predefinito. Questo parametro è modificabile per ogni campagna.", "settings.general.language": "<PERSON><PERSON>", "settings.general.logoURL": "URL del logo", "settings.general.logoURLHelp": "(Facoltativo) URL completo del logo statico visibile dall'utente come sulla pagina per annullare l'iscrizione.", "settings.general.name": "Generale", "settings.general.rootURL": "Radice dell'URL", "settings.general.rootURLHelp": "URL pubblico dell'installazione (senza barra obliqua finale).", "settings.general.sendOptinConfirm": "Inviare la conferma di `opt-in`", "settings.general.sendOptinConfirmHelp": "Inviare una mail de conferma per ogni nuova iscrizione, lo stesso si è stata fatta dal formulario pubblico o da quello di amministrazione.", "settings.general.siteName": "Nome del sito", "settings.invalidMessengerName": "Nome di messaggeria non valido.", "settings.mailserver.authProtocol": "Protocollo di autenticazione", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "Indirizzo host del server SMTP.", "settings.mailserver.idleTimeout": "Periodo di inattività", "settings.mailserver.idleTimeoutHelp": "Tempo di attesa prima di una nuova attività sulla connessione prima della chiusura e cancellazione del pool (s per i secondi, m per i minuti).", "settings.mailserver.maxConns": "Nb. connessioni max.", "settings.mailserver.maxConnsHelp": "Numero massimo di connessioni simultanee al server SMTP.", "settings.mailserver.password": "Password", "settings.mailserver.passwordHelp": "Entra per modificare", "settings.mailserver.port": "Porto", "settings.mailserver.portHelp": "Porta del server SMTP.", "settings.mailserver.skipTLS": "Ignora controllo TLS", "settings.mailserver.skipTLSHelp": "Ignora la verifica del nome dell'host sul certificato TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Attiva STARTTLS.", "settings.mailserver.username": "Nome utente", "settings.mailserver.waitTimeout": "Tempo d'attesa", "settings.mailserver.waitTimeoutHelp": "Tempo di attesa per una nuova attività su una connessione prima che venga chiusa e rimossa dal pool (s per secondo, m per minuto).", "settings.media.provider": "Fornitore", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "Percorso del bucket", "settings.media.s3.bucketPathHelp": "Percorso all'interno del bucket per caricare i file. Il valore predefinito è /", "settings.media.s3.bucketType": "Tipo di <PERSON>", "settings.media.s3.bucketTypePrivate": "Privato", "settings.media.s3.bucketTypePublic": "Pubblico", "settings.media.s3.key": "Chiave d'accesso AWS", "settings.media.s3.publicURL": "Custom public URL (optional)", "settings.media.s3.publicURLHelp": "Dominio S3 personalizzato da usare per i link alle immagini, invece dell'URL predefinito del backend S3.", "settings.media.s3.region": "Regione", "settings.media.s3.secret": "Segreto d'acceso AWS", "settings.media.s3.uploadExpiry": "Caricamento scaduto", "settings.media.s3.uploadExpiryHelp": "(Facoltativo) Specifica il TTL (in secondi) per l'URL predefinito generato. Applicabile solo per i buckets privati (s, m, h, d per i secondi, minuti, ore e giorni).", "settings.media.s3.url": "S3 backend URL", "settings.media.s3.urlHelp": "Modifficare soltanto se stai utilizando uno backend compatibile con S3, ad essempio Minio.", "settings.media.title": "Caricamento dei media", "settings.media.upload.path": "Percorso del caricamento", "settings.media.upload.pathHelp": "Percorso verso il repertorio dove i media saranno caricati.", "settings.media.upload.uri": "URI del caricamento", "settings.media.upload.uriHelp": "URI del caricamento che sarà visibile dal mondo esterno. Il media caricato nel percorso del caricamento sarà accessibile pubblicamente sotto {root_url}, per esempio: https://listmonk.tuosito.com/uploads.", "settings.messengers.maxConns": "Nb. connessioni max.", "settings.messengers.maxConnsHelp": "Numero massimo di connessioni simultanee al server.", "settings.messengers.messageSaved": "Parametri salvati. Ricarica dell'applicazione...", "settings.messengers.name": "Strumento di messaggeria", "settings.messengers.nameHelp": "Per esempio: my-sms. Alfanumerico / trattino.", "settings.messengers.password": "Password", "settings.messengers.retries": "Tentativi", "settings.messengers.retriesHelp": "Numero di tentativi in caso di errore invio messaggio.", "settings.messengers.skipTLSHelp": "Ignora la verifica del nome dell'host sul certificato TLS.", "settings.messengers.timeout": "Periodo di inattività", "settings.messengers.timeoutHelp": "Tempo di attesa prima di una nuova attività sulla connessione prima della chiusura e cancellazione del pool (s per i secondi, m per i minuti).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Radice URL del server Postback.", "settings.messengers.username": "Nome utente", "settings.needsRestart": "Impostazione cambiata. Pausare tutte le campagne e riavviare l'applicazione", "settings.performance.batchSize": "Dimensione del lotto", "settings.performance.batchSizeHelp": "Numero di iscritti da estrarre dal database in una sola iterazione. Ogni iterazione estrae gli iscritti dal database, invia loro i messaggi, poi passa all'iterazione seguente per estrarre il lotto successivo. Idealmente questo valore dovrebbe essere superiore alla velocità massima possibile (Concorrenza x Frequenza del messaggio).", "settings.performance.concurrency": "Concorrenza", "settings.performance.concurrencyHelp": "Numero di worker (threads) concorrenti massimo che invieranno i messaggi contemporaneamente.", "settings.performance.maxErrThreshold": "Soglia massima di errore", "settings.performance.maxErrThresholdHelp": "Numero di errori (esempio: SMTP scaduto durante l'invio delle mail) che una campagna in corso può tollerare prima di essere sospesa per verifica o intervento manuale. Imposta sur 0 per non andare mai in pausa.", "settings.performance.messageRate": "Frequenza del messaggio", "settings.performance.messageRateHelp": "Numero massimo di messaggi a inviare per worker in un secondo. Se concorrente = 10 e frequenza del messaggio = 10, allora fino a 10x10 = 100 messaggi possono essere emessi ogni secondo. <PERSON><PERSON> parametro, come il parametro concorrente, dovrebbe essere modificato per mantenere i messaggi uscenti ogni secondo al di sotto del limite della velocità dei server dei messaggi destinatari.", "settings.performance.name": "Prestazione", "settings.performance.slidingWindow": "Attiva un limite tramite finestra scorrevole", "settings.performance.slidingWindowDuration": "<PERSON><PERSON>", "settings.performance.slidingWindowDurationHelp": "Durata del periodo della finestra scorrevole (m per minuto, h per ora).", "settings.performance.slidingWindowHelp": "Limita il numero totale di messaggi inviati durante un dato periodo. Una volta raggiunto questo limite, l'invio dei messaggi è sospeso fino a che la finestra di tempo sia passata.", "settings.performance.slidingWindowRate": "Num. max messaggi.", "settings.performance.slidingWindowRateHelp": "Numero massimo di messaggi da inviare nella durata della finestra.", "settings.privacy.allowBlocklist": "Autorizza la lista di blocco", "settings.privacy.allowBlocklistHelp": "Autorizza gli iscritti a cancellare l'iscrizione da tutte le newsletters e a segnalarsi come bloccati?", "settings.privacy.allowExport": "Autorizza l'esportazione", "settings.privacy.allowExportHelp": "Autorizzi gli iscritti a esportare i dati raccolti su di loro?", "settings.privacy.allowPrefs": "Allow preference changes", "settings.privacy.allowPrefsHelp": "Allow subscribers to change preferences such as their names and multiple list subscriptions.", "settings.privacy.allowWipe": "Autorizza la cancellazione", "settings.privacy.allowWipeHelp": "Autorizza gli iscritti a cancellare le loro iscrizioni e tutti gli altri dati dal database. Le visualizzazioni della campagna e i clic sui link verranno anch'essi cancellati, mentre i contatori globali delle visualizzazioni e del numero di clic restano invariati (nessun iscritto vi è associato) in modo che le statistiche non siano compromesse.", "settings.privacy.domainBlocklist": "Dominio della lista di blocco", "settings.privacy.domainBlocklistHelp": "Le casselle di posta di questi domini sono vietate dalla iscrizione. Inserire un dominiio per riga, ad essempio: pincopallino.com", "settings.privacy.individualSubTracking": "Follow-up individuale degli abbonati", "settings.privacy.individualSubTrackingHelp": "Monitora le visualizzazioni e i clic della campagna per iscritto. Quando è disabilitato, il follow-up delle visualizzazioni e dei clic, si effettua senza essere legato agli iscritti individuali.", "settings.privacy.listUnsubHeader": "Includere l'intestazione `List-Unsubscribe`", "settings.privacy.listUnsubHeaderHelp": "Includere intestazioni di annullamento dell'iscrizione che consentono agli utenti di annullare l'iscrizione con un clic dal proprio client di posta elettronica.", "settings.privacy.name": "Privacy", "settings.restart": "Riavviare", "settings.smtp.customHeaders": "Intestazioni personalizzate", "settings.smtp.customHeadersHelp": "Matrice facoltativa di intestazioni di posta elettronica da includere in tutti i messaggi inviati da questo server. Ad esempio: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "At<PERSON><PERSON>a", "settings.smtp.heloHost": "Nome host HELO", "settings.smtp.heloHostHelp": "Facoltativo. Alcuni server SMTP richiedono un nome di dominio completo nel nome host. Per impostazione predefinita, HELLOs viene fornito con `localhost`. Impostare questo parametro se deve essere utilizzato un nome host personalizzato.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Tentativi", "settings.smtp.retriesHelp": "Numero di tentativi in caso di errore invio messaggio.", "settings.smtp.sendTest": "Send e-mail", "settings.smtp.setCustomHeaders": "Definisci intestazioni personalizzate", "settings.smtp.testConnection": "Prova la connessione", "settings.smtp.testEnterEmail": "Inserice la password da testare", "settings.smtp.toEmail": "Cassella di posta di ricezzione", "settings.title": "Impostazioni", "settings.updateAvailable": "È a disposizione una nuova versione {version}.", "subscribers.advancedQuery": "Avanzate", "subscribers.advancedQueryHelp": "Espressione SQL parziale per interrogare gli attributi del sottoscrittore", "subscribers.attribs": "Attributi", "subscribers.attribsHelp": "Gli attributi sono definiti come una mappa JSON, ad esempio:", "subscribers.blocklistedHelp": "<PERSON>li abbonati bloccati non riceveranno mai e-mail.", "subscribers.confirmBlocklist": "Lista di blocco {num} iscritto(i)?", "subscribers.confirmDelete": "Elimina {num} is<PERSON>rit<PERSON><PERSON>(i)?", "subscribers.confirmExport": "Esporta {num} iscritto(i)?", "subscribers.domainBlocklisted": "Il nome di dominio della cassella di posta si trova nella llista di blocco.", "subscribers.downloadData": "Scarica i dati", "subscribers.email": "Email", "subscribers.emailExists": "Email già esistente.", "subscribers.errorBlocklisting": "Errore durante il blocco degli iscritti: {error}", "subscribers.errorNoIDs": "Nessun ID fornito.", "subscribers.errorNoListsGiven": "Nessuna lista fornita.", "subscribers.errorPreparingQuery": "Errore durante la preparazione della richiesta dell'iscritto: {error}", "subscribers.errorSendingOptin": "Errore durante l'invio dell'e-mail di attivazione.", "subscribers.export": "Esportazione", "subscribers.invalidAction": "Azione non valida.", "subscribers.invalidEmail": "E-mail non valida.", "subscribers.invalidJSON": "JSON non valido negli attributi.", "subscribers.invalidName": "Nome errato.", "subscribers.listChangeApplied": "Modifica della lista eseguita.", "subscribers.lists": "Liste", "subscribers.listsHelp": "Le liste i cui iscritti hanno annullato l'iscrizione non possono essere eliminate.", "subscribers.listsPlaceholder": "Liste a cui iscriversi", "subscribers.manageLists": "Gest<PERSON>ci liste", "subscribers.markUnsubscribed": "Segna come non iscritto", "subscribers.newSubscriber": "Nuovo iscritto", "subscribers.numSelected": "{num} is<PERSON>rit<PERSON>(i) selezionato(i)", "subscribers.optinSubject": "Confermare l'iscrizione", "subscribers.preconfirm": "Pre-confirm subscriptions", "subscribers.preconfirmHelp": "Non inviate e-mail di opt-in e classifica tutte le iscrizioni alle liste come 'sottoscritte'.", "subscribers.query": "<PERSON><PERSON>", "subscribers.queryPlaceholder": "Email o nome", "subscribers.reset": "R<PERSON><PERSON><PERSON>", "subscribers.selectAll": "Se<PERSON><PERSON>na tutto {num}", "subscribers.sendOptinConfirm": "Inviare la conferma dell'opt-in", "subscribers.sentOptinConfirm": "Conferma opt-in inviata", "subscribers.status.blocklisted": "Lista bloccata", "subscribers.status.confirmed": "Confermato", "subscribers.status.enabled": "At<PERSON><PERSON>a", "subscribers.status.subscribed": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.status.unconfirmed": "Non confermato", "subscribers.status.unsubscribed": "Iscrizione annullata", "subscribers.subscribersDeleted": "{num} iscritto(i) eliminato(i)", "templates.cantDeleteDefault": "Impossibile eliminare il modello predefinito", "templates.default": "Predefinito", "templates.dummyName": "Campagna di prova", "templates.dummySubject": "Oggetto della campagna di prova", "templates.errorCompiling": "Errore durante la compilazione del modello: {error}", "templates.errorRendering": "Messaggio di errore durante il rendering: {errore}", "templates.fieldInvalidName": "Lunghezza del nome non valida.", "templates.makeDefault": "Definisci per impostazione predefinita", "templates.newTemplate": "Nuovo modello", "templates.placeholderHelp": "Il segnaposto {placeholder} deve apparire esattamente una volta nel modello.", "templates.preview": "Anteprima", "templates.rawHTML": "HTML semplice", "templates.subject": "Oggeto", "users.login": "Accesso", "users.logout": "<PERSON><PERSON><PERSON>"}