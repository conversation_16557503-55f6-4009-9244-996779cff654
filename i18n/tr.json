{"_.code": "tr", "_.name": "Turkish (tr)", "admin.errorMarshallingConfig": "<PERSON><PERSON><PERSON> ile ilgili hata: {error}", "analytics.count": "<PERSON><PERSON>", "analytics.fromDate": "İtibaren", "analytics.invalidDates": "Geçersiz `ba<PERSON><PERSON><PERSON><PERSON>' veya `bitiş' tarihleri.", "analytics.isUnique": "<PERSON><PERSON><PERSON> her abone i<PERSON><PERSON>.", "analytics.links": "Bağlantılar", "analytics.nonUnique": "Bireysel abone takibi kapalı olduğu için sayılar ben<PERSON>.", "analytics.title": "<PERSON><PERSON><PERSON>", "analytics.toDate": "To", "bounces.source": "<PERSON><PERSON><PERSON>", "bounces.unknownService": "Bilinmeyen servis.", "bounces.view": "Sıçramaları görüntüleyin", "campaigns.addAltText": "Alternatif düz metin e<PERSON>in", "campaigns.archive": "Arşiv", "campaigns.archiveEnable": "Halka açık arşivde yayınlayın", "campaigns.archiveHelp": "Kampanya mesajını genel arşivde yayınlayın (çalışıyor, duraklatıldı, bitti).", "campaigns.archiveMeta": "Kampanya meta verisi", "campaigns.archiveMetaHelp": "Ad, e-posta ve kampanya mesajında veya şablonunda kullanılan tüm isteğe bağlı öznitelikler dahil olmak üzere genel mesajda kullanılacak kukla abone verileri.", "campaigns.cantUpdate": "Gönderilmekte olan veya gönderilmiş kampaynalar güncellenemez.", "campaigns.clicks": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.confirmDelete": "Sil {name}", "campaigns.confirmSchedule": "Bu kampanya belirtilen tarihte otomatik olarak ba<PERSON>lar. <PERSON><PERSON><PERSON> a<PERSON>?", "campaigns.confirmSwitchFormat": "İçerik düzenini yitirebilir. Devam et?", "campaigns.content": "İçerik", "campaigns.contentHelp": "İçerik buraya", "campaigns.continue": "<PERSON><PERSON> et", "campaigns.copyOf": "{name} - <PERSON><PERSON><PERSON><PERSON>", "campaigns.customHeadersHelp": "Giden iletilere eklenecek özel başlıkların dizisi. örn: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Ta<PERSON>h ve saat", "campaigns.ended": "<PERSON><PERSON>", "campaigns.errorSendTest": "Test gönderirken hata: {error}", "campaigns.fieldInvalidBody": "Kampanya gövdesini oluşturma hatası: {error}", "campaigns.fieldInvalidFromEmail": "<PERSON><PERSON><PERSON><PERSON> `from_email`.", "campaigns.fieldInvalidListIDs": "Yanlış liste ID'leri.", "campaigns.fieldInvalidMessenger": "Bilinmeyen mesajcı {name}.", "campaigns.fieldInvalidName": "İsim uzunluğu yanlış.", "campaigns.fieldInvalidSendAt": "Tanımlanan tarih gelecekte olmalı.", "campaigns.fieldInvalidSubject": "<PERSON><PERSON> uzunluğu yanlış verilmiş.", "campaigns.formatHTML": "Format HTML", "campaigns.fromAddress": "<PERSON><PERSON><PERSON>", "campaigns.fromAddressPlaceholder": "isminiz <<EMAIL>>", "campaigns.invalid": "Yanlış tanımlı kapmanya", "campaigns.invalidCustomHeaders": "Geçersiz özel başlıklar: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Kampanya i<PERSON><PERSON> bir tarih gere<PERSON>.", "campaigns.newCampaign": "<PERSON><PERSON>", "campaigns.noKnownSubsToTest": "Test i<PERSON>in bilinen üye yok.", "campaigns.noOptinLists": "Kampanya oluşturmak için katılım listesi bulunmuyor.", "campaigns.noSubs": "Seçilmiş listelerin içinde kampanya oluşturmak için üye bulunmuyor.", "campaigns.noSubsToTest": "Hedeflenen üye bulunmuyor.", "campaigns.notFound": "Kampanya bulunamadı.", "campaigns.onlyActiveCancel": "Sadece aktif <PERSON> iptal edilebilir.", "campaigns.onlyActivePause": "Sadece aktif ka<PERSON>alar duraklatılabilir.", "campaigns.onlyDraftAsScheduled": "Sadece taslak kampanyalar zamanlanabilir.", "campaigns.onlyPausedDraft": "Sadece <PERSON>lan ve taslak kampanyalar başlatılabilir.", "campaigns.onlyScheduledAsDraft": "Sadece başlatılmış kampanyalar taslak olarak kaydedilebilir.", "campaigns.pause": "<PERSON><PERSON><PERSON>", "campaigns.plainText": "Düz yazı", "campaigns.preview": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.progress": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.queryPlaceholder": "İsim veya konu", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "Ham HTML", "campaigns.removeAltText": "Alternatif düz yazıyı kaldır", "campaigns.richText": "<PERSON><PERSON> metin", "campaigns.schedule": "Kampanya'<PERSON><PERSON> z<PERSON>", "campaigns.scheduled": "Zamanlandı", "campaigns.send": "<PERSON><PERSON><PERSON>", "campaigns.sendLater": "<PERSON><PERSON>", "campaigns.sendTest": "Test mesajı gönder", "campaigns.sendTestHelp": "Birden fazla alıcı eklemek için adresi yazdıktan sonra enter tuşuna bas. Adresler mevcut üyelere ait olmalıdır.", "campaigns.sendToLists": "Gönderilecek listeler", "campaigns.sent": "<PERSON><PERSON><PERSON>", "campaigns.start": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "campaigns.started": "\"{name}\" ba<PERSON><PERSON><PERSON><PERSON>ı", "campaigns.startedAt": "Başlatıldı", "campaigns.stats": "Durum", "campaigns.status.cancelled": "İptal edildi", "campaigns.status.draft": "Taslak", "campaigns.status.finished": "Sonlandı", "campaigns.status.paused": "Duraklatıldı", "campaigns.status.running": "İlerliyor", "campaigns.status.scheduled": "Zamanlandı", "campaigns.statusChanged": "\"{name}\" durumu {status}", "campaigns.subject": "<PERSON><PERSON>", "campaigns.testDisabled": "Test etmek i<PERSON>in parola girin", "campaigns.testEmails": "E-postalar", "campaigns.testSent": "Test mesajı gönderildi", "campaigns.timestamps": "<PERSON><PERSON>", "campaigns.trackLink": "İzleme bağlantısı", "campaigns.views": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.campaignViews": "Kampanya görüntülenme Sayısı", "dashboard.linkClicks": "<PERSON><PERSON><PERSON> tı<PERSON>", "dashboard.messagesSent": "<PERSON><PERSON>", "dashboard.orphanSubs": "Sa<PERSON><PERSON>", "email.data.info": "Hakkınızda üretilmiş tüm veri JSON formatında bir dosya olarak eklendi. Bir meti düzenleyici ile görüntüleyebilirsiniz.", "email.data.title": "<PERSON>zin veriniz", "email.optin.confirmSub": "Üyeliği onaylayınız", "email.optin.confirmSubHelp": "Aşağıdaki düğmeyi tıklayarak Üyeliği onaylayınız.", "email.optin.confirmSubInfo": "B<PERSON>daki listelere eklendiniz:", "email.optin.confirmSubTitle": "Üyeliği doğrulayınız", "email.optin.confirmSubWelcome": "<PERSON><PERSON><PERSON><PERSON>", "email.optin.privateList": "<PERSON><PERSON><PERSON><PERSON> liste", "email.status.campaignReason": "Sebep", "email.status.campaignSent": "<PERSON><PERSON><PERSON>il<PERSON><PERSON>", "email.status.campaignUpdateTitle": "<PERSON><PERSON><PERSON><PERSON>", "email.status.importFile": "<PERSON><PERSON><PERSON>", "email.status.importRecords": "<PERSON><PERSON>tlar", "email.status.importTitle": "Güncellemeyi içe aktar", "email.status.status": "Durum", "email.unsub": "Üyeliği <PERSON>ı<PERSON>", "email.unsubHelp": "Bu e-posta'ları almak istemiyorum", "email.viewInBrowser": "View in browser", "forms.formHTML": "HTML Formu", "forms.formHTMLHelp": "Harici bir web sayfasında bir abonelik formu göstermek için aşağıdaki HTML'yi kullanın. Formda e-posta alanı ve bir veya daha fazla `l` (liste UUID) alanı bulunmalıdır. `<PERSON><PERSON><PERSON>` alanı isteğe bağlıdır.", "forms.noPublicLists": "Form'a ihtiyaç duyulan erişime açık listeler yok.", "forms.publicLists": "Erişime açık listeler", "forms.publicSubPage": "Erişime açık üyelik sayfası", "forms.selectHelp": "Form içine eklenecek listeleri seç.", "forms.title": "Formlar", "globals.buttons.add": "<PERSON><PERSON>", "globals.buttons.addNew": "<PERSON><PERSON> yeni", "globals.buttons.back": "<PERSON><PERSON>", "globals.buttons.cancel": "İptal", "globals.buttons.clone": "<PERSON><PERSON><PERSON>", "globals.buttons.close": "Ka<PERSON><PERSON>", "globals.buttons.continue": "<PERSON><PERSON> et", "globals.buttons.delete": "Sil", "globals.buttons.deleteAll": "Tamamını sil", "globals.buttons.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.buttons.enabled": "Etkinleş<PERSON><PERSON>", "globals.buttons.insert": "<PERSON><PERSON>", "globals.buttons.learnMore": "<PERSON>ha fazla <PERSON>", "globals.buttons.more": "<PERSON><PERSON> fazla", "globals.buttons.new": "<PERSON><PERSON>", "globals.buttons.ok": "Ok", "globals.buttons.remove": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.save": "<PERSON><PERSON>", "globals.buttons.saveChanges": "<PERSON><PERSON>", "globals.days.0": "Paz", "globals.days.1": "Pzt", "globals.days.2": "Sal", "globals.days.3": "Çar", "globals.days.4": "Per", "globals.days.5": "Cum", "globals.days.6": "Cmt", "globals.days.7": "Sat", "globals.fields.createdAt": "<PERSON><PERSON><PERSON><PERSON>", "globals.fields.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "globals.fields.id": "ID", "globals.fields.name": "İsim", "globals.fields.status": "Durum", "globals.fields.type": "Tip", "globals.fields.updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Emin misiniz?", "globals.messages.confirmDiscard": "Değişiklikleri yoksay?", "globals.messages.created": "\"{name}\" <PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.messages.deleted": "\"{name}\" silme", "globals.messages.deletedCount": "{name} ({num}) silindi", "globals.messages.done": "Tamamlandı", "globals.messages.emptyState": "Burası Boş", "globals.messages.errorCreating": "<PERSON><PERSON> o<PERSON>şturma {name}: {error}", "globals.messages.errorDeleting": "<PERSON><PERSON> silme {name}: {error}", "globals.messages.errorFetching": "<PERSON><PERSON> çağırı<PERSON><PERSON> {name}: {error}", "globals.messages.errorInvalidIDs": "Bir yada daha fazla geçersiz ID: {error}", "globals.messages.errorUUID": "Hata oluştururken UUID: {error}", "globals.messages.errorUpdating": "<PERSON><PERSON> {name}: {error}", "globals.messages.internalError": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "globals.messages.invalidData": "Geçersiz veri", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Yanlış ID", "globals.messages.invalidUUID": "Yanlış UUID", "globals.messages.missingFields": "<PERSON><PERSON><PERSON>(lar): {name}", "globals.messages.notFound": "{name} bulunamadı", "globals.messages.passwordChange": "Değiştirmek için de<PERSON> gir", "globals.messages.updated": "\"{name}\" g<PERSON><PERSON><PERSON><PERSON>", "globals.months.1": "Oca", "globals.months.10": "<PERSON><PERSON>", "globals.months.11": "<PERSON><PERSON>", "globals.months.12": "Ara", "globals.months.2": "<PERSON><PERSON>", "globals.months.3": "Mar", "globals.months.4": "<PERSON><PERSON>", "globals.months.5": "May", "globals.months.6": "Haz", "globals.months.7": "Tem", "globals.months.8": "<PERSON><PERSON><PERSON>", "globals.months.9": "<PERSON><PERSON>", "globals.states.off": "Off", "globals.terms.all": "All", "globals.terms.analytics": "<PERSON><PERSON><PERSON>", "globals.terms.bounce": "Bounce | Bounces", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Kampanya | Kampanyalar", "globals.terms.campaigns": "Kampanyalar", "globals.terms.dashboard": "<PERSON><PERSON><PERSON><PERSON>i", "globals.terms.day": "Gün | Günler", "globals.terms.hour": "Saat | Saatler", "globals.terms.list": "Liste | Listeler", "globals.terms.lists": "<PERSON><PERSON><PERSON>", "globals.terms.media": "Medya | Medya", "globals.terms.messenger": "Kuryeler | Kuryeler", "globals.terms.messengers": "<PERSON><PERSON><PERSON><PERSON>", "globals.terms.minute": "Dakika | Dakikalar", "globals.terms.month": "<PERSON><PERSON> | <PERSON><PERSON><PERSON>", "globals.terms.second": "Saniye | Saniyeler", "globals.terms.settings": "<PERSON><PERSON><PERSON>", "globals.terms.subscriber": "Üye | Üyeler", "globals.terms.subscribers": "<PERSON><PERSON><PERSON>", "globals.terms.subscriptions": "Abonelik | Abonelikler", "globals.terms.tag": "Etiket | Etiket(ler)", "globals.terms.tags": "Etiket(ler)", "globals.terms.template": "Taslak | Taslaklar", "globals.terms.templates": "Taslaklar", "globals.terms.tx": "İşlem | İşlem", "globals.terms.year": "Yıl | Yıllar", "import.alreadyRunning": "Bir içe aktarım halen sürüyor. Yeniden denemek için durdurun veya yeniden denemek için be<PERSON>.", "import.blocklist": "<PERSON><PERSON><PERSON>", "import.csvDelim": "CSV ayıracı", "import.csvDelimHelp": "Varsayılan ayıraç virgüldür.", "import.csvExample": "Örnek ham CSV dosyası", "import.csvFile": "CSV veya ZIP dosyası", "import.csvFileHelp": "Buraya CSV veya Zip dosyası bırak veya tıkla", "import.errorCopyingFile": "<PERSON><PERSON>, dosya k<PERSON>: {error}", "import.errorProcessingZIP": "<PERSON><PERSON>, zip dosyası işleme: {error}", "import.errorStarting": "<PERSON><PERSON>, i<PERSON>eri aktarım ba<PERSON>lama: {error}", "import.importDone": "<PERSON><PERSON>", "import.importStarted": "İçeri aktarım başladı", "import.instructions": "Kullanım talimatı", "import.instructionsHelp": "Toplu üyeleri yükleyebilmek için bir CSV dosyası veya CSV dosyası içeren bir ZIP dosyası yükleyiniz. CSV dosyasının aynen buradaki isimlere sahip başlıklara sahip olması gerekir. attributes (seçime bağlı) verisi çift tırnak ile verilerin tanımlandığı gerçerli bir JSON olmalıdır.", "import.invalidDelim": "Ayıraç tek bir karakter olmalı.", "import.invalidFile": "Hatalı dosya: {error}", "import.invalidMode": "Hatalı mod", "import.invalidParams": "Hatalı parametre: {error}", "import.invalidSubStatus": "Geçersiz abonelik durumu", "import.listSubHelp": "Üye olunacak listeler.", "import.mode": "Mod", "import.overwrite": "Üzerine yaz?", "import.overwriteHelp": "İsim ve attribs parametrelerini var olan üyelerin üzerine yaz?", "import.recordsCount": "{num} / {total} kayıt", "import.stopImport": "İçeri aktarmayı durdur", "import.subscribe": "Üye ol", "import.title": "Üyeleri içeri aktar", "import.upload": "<PERSON><PERSON><PERSON>", "lists.confirmDelete": "Emin misiniz? Bu işlem üyeleri silmeyecek.", "lists.confirmSub": "{name} <PERSON><PERSON><PERSON>(leri) <PERSON>ğ<PERSON>la", "lists.invalidName": "<PERSON><PERSON><PERSON><PERSON> isim", "lists.newList": "<PERSON><PERSON> liste", "lists.optin": "Katılım", "lists.optinHelp": "Çifte katılım üyelerin doğrulanması için e-posta gönderir. Çifte katılım listelerde, kampanyalar sadece doğrulanan üyelere gönderilir.", "lists.optinTo": "{name} i<PERSON><PERSON>", "lists.optins.double": "Çifte katılım", "lists.optins.single": "Tek katılım", "lists.sendCampaign": "Kampanyayı gönder", "lists.sendOptinCampaign": "katılım kampanyasını gönder", "lists.type": "Tip", "lists.typeHelp": "Erişime açık listelere her yerden erişilebilirdir ve üye olunabilir. Ayrıca üyelik yönetim sayfaları internet üzerinden erişime açık yerlerdir.", "lists.types.private": "<PERSON><PERSON><PERSON><PERSON>", "lists.types.public": "Erişime açık", "logs.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maintenance.help": "Veri miktarına bağlı olarak bazı eylemlerin tamamlanması biraz zaman alabilir.", "maintenance.maintenance.unconfirmedOptins": "Onaylanmamış katılım abonelikleri", "maintenance.olderThan": "<PERSON><PERSON> eski", "maintenance.title": "Bakım", "maintenance.unconfirmedSubs": "{name} günden daha eski onaylanmamış abonelikler.", "media.errorReadingFile": "Dosyayı okurken hata oluştu: {error}", "media.errorResizing": "<PERSON>sim ye<PERSON>den boyutlandırılırken hata oluştu: {error}", "media.errorSavingThumbnail": "Küçük resmi kaydederken hata oluştu: {error}", "media.errorUploading": "<PERSON><PERSON><PERSON> hata o<PERSON>: {error}", "media.invalidFile": "Hatalı dosya: {error}", "media.title": "<PERSON><PERSON><PERSON>", "media.unsupportedFileType": "Desteklenmeyen dosya tipi ({type})", "media.upload": "<PERSON><PERSON><PERSON><PERSON>", "media.uploadHelp": "Bir veya daha fazla resmi buraya bırak veya tıkla", "media.uploadImage": "<PERSON><PERSON><PERSON>", "menu.allCampaigns": "<PERSON><PERSON><PERSON>", "menu.allLists": "<PERSON><PERSON><PERSON>", "menu.allSubscribers": "<PERSON><PERSON><PERSON>", "menu.dashboard": "Y<PERSON><PERSON><PERSON>i", "menu.forms": "Formlar", "menu.import": "İçeri aktar", "menu.logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.maintenance": "Bakım", "menu.media": "<PERSON><PERSON><PERSON>", "menu.newCampaign": "<PERSON><PERSON>", "menu.settings": "<PERSON><PERSON><PERSON>", "public.archiveEmpty": "Henüz arşivlenmiş mesaj yok.", "public.archiveTitle": "Posta listesi arşivi", "public.blocklisted": "Abonelikten kalıcı olarak çıkıldı.", "public.campaignNotFound": "E-posta mesajı bulunamadı.", "public.confirmOptinSubTitle": "Üyeliği doğrula", "public.confirmSub": "Üyeliği doğrula", "public.confirmSubInfo": "Buradaki listeler içerisine eklendiniz:", "public.confirmSubTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.dataRemoved": "Tüm üyelikleriniz ve size ait olan tüm veriler silinmiştir.", "public.dataRemovedTitle": "<PERSON><PERSON>", "public.dataSent": "Size ait olan bilgiler size e-posta olarak gönderilmiştir.", "public.dataSentTitle": "Veri e-posta olarak g<PERSON>il<PERSON>.", "public.errorFetchingCampaign": "<PERSON><PERSON>, e-posta getirilirken.", "public.errorFetchingEmail": "E-posta mesajı bulunamadı", "public.errorFetchingLists": "Listeleri getirme hatası. Lütfen tekrarla.", "public.errorProcessingRequest": "İstek işleme hatası. Lütfen tekrarla.", "public.errorTitle": "<PERSON><PERSON>", "public.invalidFeature": "Bu özellik geçerli değil.", "public.invalidLink": "Geçersiz link", "public.managePrefs": "Manage preferences", "public.managePrefsUnsub": "Abonelikten çıkmak için listelerin işaretini kaldırın.", "public.noListsAvailable": "Eklenecek liste yok.", "public.noListsSelected": "Bağlanılacak geçerli bir liste seçilmedi.", "public.noSubInfo": "Doğrulanacak üyelik bulunmuyor.", "public.noSubTitle": "Üyelik yok", "public.notFoundTitle": "Bulunamadı", "public.prefsSaved": "Tercihleriniz kaydedilmiştir.", "public.privacyConfirmWipe": "Tüm üyelik verilerinizin kalıcı olarak silinmesini istediğinize eminmisiniz?", "public.privacyExport": "Verinizi dışarı aktarın", "public.privacyExportHelp": "Size ait verilerin bir kopyası size e-posta ile gönderilecektir.", "public.privacyTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.privacyWipe": "<PERSON><PERSON><PERSON> tamamen temizle", "public.privacyWipeHelp": "Tüm üyeliklerinizi ve ilişkili verilerinizi veritabanından silin.", "public.sub": "Üyelik", "public.subConfirmed": "Başarıyla üye olundu.", "public.subConfirmedTitle": "Doğrulanmıştır", "public.subName": "İsim (opsiyonel)", "public.subNotFound": "Üyelik bulunamadı.", "public.subOptinPending": "Üyelik doğrulaması için bir e-posta gönderilmiştir.", "public.subPrivateList": "<PERSON><PERSON><PERSON><PERSON> liste", "public.subTitle": "Üye ol", "public.unsub": "Üyelikten ayrıl", "public.unsubFull": "Gelecekte gelecek tüm e-postalar dahil üyeliği sonlandır.", "public.unsubHelp": "Bu e-posta listesinden ayrılmayı istermisiniz?", "public.unsubTitle": "Üyelikten ayrıl", "public.unsubbedInfo": "Başarı ile üyeliğinizi bitirdiniz.", "public.unsubbedTitle": "Üyelik bitirildi.", "public.unsubscribeTitle": "e-posta listesi üyeliğini bitir", "settings.appearance.adminHelp": "Yönetici arayüzüne uygulanacak özel CSS.", "settings.appearance.adminName": "Yönetici", "settings.appearance.customCSS": "Özel CSS", "settings.appearance.customJS": "Özel JavaScript", "settings.appearance.name": "G<PERSON>rü<PERSON><PERSON><PERSON>", "settings.appearance.publicHelp": "Genel sayfalara uygulanacak özel CSS ve JavaScript.", "settings.appearance.publicName": "Halka açık", "settings.bounces.action": "<PERSON><PERSON><PERSON>", "settings.bounces.blocklist": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.count": "Sıçrama sayısı", "settings.bounces.countHelp": "<PERSON><PERSON> ba<PERSON>ına geri dö<PERSON>ı", "settings.bounces.delete": "Sil", "settings.bounces.enable": "Sıçrama işlemeyi etkinleştirin", "settings.bounces.enableMailbox": "<PERSON><PERSON>önen posta kutusunu etkinleştirin", "settings.bounces.enableSES": "SES'i etkinleştirin", "settings.bounces.enableSendgrid": "SendG<PERSON>'i etkinleştirin", "settings.bounces.enableWebhooks": "Sıçrama web kancalarını etkinleştirin", "settings.bounces.enabled": "Etkinleştir", "settings.bounces.folder": "<PERSON><PERSON>", "settings.bounces.folderHelp": "Taranacak IMAP klasörünün adı. Örn: <PERSON><PERSON><PERSON>.", "settings.bounces.invalidScanInterval": "Sıçrama tarama aralığı en az 1 dakika olmalıdır.", "settings.bounces.name": "Sıçramalar", "settings.bounces.scanInterval": "<PERSON><PERSON>ığ<PERSON>", "settings.bounces.scanIntervalHelp": "Sıçrama posta kutusunun sıçramalar için taranması gereken aralık (saniye için s, dakika için m).", "settings.bounces.sendgridKey": "Send<PERSON><PERSON>", "settings.bounces.type": "Tip", "settings.bounces.username": "Kullanıcı adı", "settings.confirmRestart": "Çalışan kampanyaların durak<PERSON>ıldığından emin ol. <PERSON><PERSON> ba<PERSON>?", "settings.duplicateMessengerName": "Çoklanmış messenger ismi: {name}", "settings.errorEncoding": "Hat<PERSON>ı kodlama ayarları: {error}", "settings.errorNoSMTP": "En azından bir SMTP bloğu etkin olmalı", "settings.general.adminNotifEmails": "Yönetici e-posta bildirimleri", "settings.general.adminNotifEmailsHelp": "İçe aktarma gü<PERSON>eri, ka<PERSON><PERSON><PERSON>, başarısızlık gibi yönetici bildirimlerinin gönderilmesi gereken e-posta adreslerinin virgülle ayrılmış listesi.", "settings.general.checkUpdates": "Güncellemeleri kontrol edin", "settings.general.checkUpdatesHelp": "<PERSON>ni uygulama sürümlerini periyodik olarak kontrol edin ve bilgilendirin.", "settings.general.enablePublicArchive": "Genel posta listesi arşiv sayfasını etkinleştirin", "settings.general.enablePublicArchiveHelp": "Arşivlemenin etkinleştirildiği kampanyaları kamuya açık web sitesinde yayınlayın.", "settings.general.enablePublicSubPage": "Erişime açık üyelik sayfasını etkinleştir", "settings.general.enablePublicSubPageHelp": "Kişilerin abone olması için tüm genel listeleri içeren genel bir abonelik sayfası gösterin.", "settings.general.faviconURL": "Favicon URL'si", "settings.general.faviconURLHelp": "(İsteğe bağlı) abonelik iptal sayfası gibi kullanıcıya bakan görünümde görüntülenecek statik faviconun tam URL'si.", "settings.general.fromEmail": "Varsayılan `gelen` e-postası", "settings.general.fromEmailHelp": "Vars<PERSON><PERSON><PERSON> `gelen` e-postası, tüm gönderilen kampanyalarda gösterilecek. Her kampanya i<PERSON><PERSON>iştirilebilir.", "settings.general.language": "Dil", "settings.general.logoURL": "Logo URL'i", "settings.general.logoURLHelp": "(İsteğe bağlı) abonelik iptal sayfası gibi kullanıcıya bakan görünümde görüntülenecek statik logonun tam URL'si.", "settings.general.name": "<PERSON><PERSON>", "settings.general.rootURL": "Kök URL'i", "settings.general.rootURLHelp": "<PERSON><PERSON><PERSON>un genel URL'si (b<PERSON><PERSON><PERSON> ç<PERSON>i yok).", "settings.general.sendOptinConfirm": "Katılım onayı gö<PERSON>in", "settings.general.sendOptinConfirmHelp": "Yeni aboneler kaydolduğunda veya yönetici formu aracılığıyla eklendiğinde, bir katılım onay e-postası gönderin.", "settings.general.siteName": "Site adı", "settings.invalidMessengerName": "Geçersiz kurye adı.", "settings.mailserver.authProtocol": "Protokol", "settings.mailserver.host": "İstemci", "settings.mailserver.hostHelp": "SMTP sunucusu adresi.", "settings.mailserver.idleTimeout": "<PERSON><PERSON><PERSON> ka<PERSON>", "settings.mailserver.idleTimeoutHelp": "B<PERSON> bağlantıdaki yeni et<PERSON>liği kapatmadan ve havuzdan kaldırmadan önce bekleme süresi (s saniye, m dakika).", "settings.mailserver.maxConns": "<PERSON><PERSON><PERSON>um bağlantı sayısı", "settings.mailserver.maxConnsHelp": "SMTP sunucusuna aynı anda gönderilecek çoklu istek sayısı.", "settings.mailserver.password": "Pa<PERSON><PERSON>", "settings.mailserver.passwordHelp": "Değiştirmek için giri<PERSON>z", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "SMTP sunucusu port numarası.", "settings.mailserver.skipTLS": "TLS doğrulamasını atla", "settings.mailserver.skipTLSHelp": "TLS sertifikaları için sunucu adı doğrulamayı atla.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "STARTTLS tanımla.", "settings.mailserver.username": "Kullanıcı adı", "settings.mailserver.waitTimeout": "Bekleme süresi aşımı", "settings.mailserver.waitTimeoutHelp": "Bir bağlantıdaki yeni et<PERSON>liği kapatmadan ve havuzdan kaldırmadan önce bekleme süresi (saniye için s, dakika için m). ", "settings.media.provider": "Sağlayıcı", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "Bucket yolu", "settings.media.s3.bucketPathHelp": "Dosyaları yüklemek için paketin içindeki yol. Varsayılan /", "settings.media.s3.bucketType": "Bucket tipi", "settings.media.s3.bucketTypePrivate": "<PERSON><PERSON>", "settings.media.s3.bucketTypePublic": "Erişime açık", "settings.media.s3.key": "AWS eri<PERSON><PERSON>", "settings.media.s3.publicURL": "Özel genel URL (isteğe bağlı)", "settings.media.s3.publicURLHelp": "Varsayılan S3 arka uç URL'si yerine resim bağlantıları için kullanılacak özel S3 etki alanı.", "settings.media.s3.region": "<PERSON><PERSON><PERSON>", "settings.media.s3.secret": "AWS eri<PERSON><PERSON>(secret)", "settings.media.s3.uploadExpiry": "<PERSON><PERSON><PERSON><PERSON> sona erme", "settings.media.s3.uploadExpiryHelp": "(İsteğe bağlı) Oluşturulan önceden imzalanmış URL için TTL'yi (saniye cinsinden) belirtin. Yalnızca özel paketler için geçerlidir (saniye, dakika, saat, gün için s, m, h, d).", "settings.media.s3.url": "S3 arka uç URL'si", "settings.media.s3.urlHelp": "Yalnızca Minio gibi özel bir S3 uyumlu arka uç kullanıyorsanız değiştirin.", "settings.media.title": "<PERSON><PERSON><PERSON>", "settings.media.upload.path": "<PERSON><PERSON><PERSON><PERSON> yolu", "settings.media.upload.pathHelp": "<PERSON><PERSON><PERSON><PERSON>n y<PERSON>ği dizinin yolu.", "settings.media.upload.uri": "Yüklwmw URI si", "settings.media.upload.uriHelp": "Dış dünya tarafından görülebilen URI'yi yükleyin. Upload_path'e yüklenen medyaya {root_url} altından herkese açık erişime sahip olacak, örne<PERSON>in https://www.siteniz.com/uploads.", "settings.messengers.maxConns": "<PERSON><PERSON><PERSON><PERSON> bağlantı", "settings.messengers.maxConnsHelp": "<PERSON><PERSON><PERSON><PERSON> maksimum çoklu bağlantı.", "settings.messengers.messageSaved": "<PERSON><PERSON><PERSON> ka<PERSON>. Uygulama yeniden yükleniyor ...", "settings.messengers.name": "<PERSON><PERSON><PERSON><PERSON>", "settings.messengers.nameHelp": "örn.: my-sms. Alfanumerik / bölü.", "settings.messengers.password": "Pa<PERSON><PERSON>", "settings.messengers.retries": "Tekrarlama", "settings.messengers.retriesHelp": "Bir mesaj başarısız olduğunda yeniden deneme sayısı.", "settings.messengers.skipTLSHelp": "TLS sertifikasında ana bilgisayar adı kontrolünü atlayın.", "settings.messengers.timeout": "Boşta zaman aşımı", "settings.messengers.timeoutHelp": "B<PERSON> bağlantıdaki yeni et<PERSON>liği kapatmadan ve havuzdan kaldırmadan önce bekleme süresi (s saniye, m dakika).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Postback sunusucu için kök URL.", "settings.messengers.username": "Kullanıcı adı", "settings.needsRestart": "Ayarlar değişti. Çalışan tüm kampanyaları durdur ve uygulamayı yeniden başlat.", "settings.performance.batchSize": "Batch büyüklüğü", "settings.performance.batchSizeHelp": "Veritabanından tek bir yinelemede çekilecek abone sayısı. Her yineleme, aboneleri veritabanından çeker, onlara mesajlar gönderir ve ardından bir sonraki grubu çekmek için bir sonraki yinelemeye geçer. <PERSON><PERSON>, ideal olarak elde edilebilecek maksimum iş hacminden (eşzamanlılık * ileti_ hızı) daha yüksek olmalıdır.", "settings.performance.concurrency": "Çoklu bağlantı", "settings.performance.concurrencyHelp": "<PERSON><PERSON>ı anda ileti göndermeyi deneyecek maksimum eşzamanlı worker (thread) sayısı.", "settings.performance.maxErrThreshold": "<PERSON><PERSON><PERSON><PERSON> hata eşiği", "settings.performance.maxErrThresholdHelp": "The number of errors (eg: SMTP timeouts while e-mailing) a running campaign should tolerate before it is paused for manual investigation or intervention. Set to 0 to never pause.", "settings.performance.messageRate": "<PERSON>j <PERSON>", "settings.performance.messageRateHelp": "Çal<PERSON>şan başına saniyede bir saniyede gönderilecek maksimum mesaj sayısı. Concurrency = 10 ve message_rate = 10 ise, her saniye 10x10 = 100'e kadar mesaj gönderilebilir. Bu, eşzam<PERSON>l<PERSON><PERSON><PERSON><PERSON> ile birlikte, net mesajların saniyede dışarı çıkmasını hedef mesaj sunucularının hız limitlerinin altında tutmak için ince ayar yapılmalıdır.", "settings.performance.name": "Performans", "settings.performance.slidingWindow": "<PERSON><PERSON> pencere sınırını etkinleştir", "settings.performance.slidingWindowDuration": "<PERSON><PERSON><PERSON>", "settings.performance.slidingWindowDurationHelp": "<PERSON><PERSON> pencere periyo<PERSON><PERSON>n s<PERSON> (dakika i<PERSON>in m, saat i<PERSON>in h).", "settings.performance.slidingWindowHelp": "Belirli bir süre içinde gönderilen toplam ileti sayısını sınırlayın. Bu sınıra ulaşıldığında, mesajların gönderimi zaman penceresi temizlenene kadar bekletilir.", "settings.performance.slidingWindowRate": "Maksimum. mesaj", "settings.performance.slidingWindowRateHelp": "Pencere süresi içinde gönderilecek maksimum mesaj sayısı.", "settings.privacy.allowBlocklist": "Liste bloklama izini ver", "settings.privacy.allowBlocklistHelp": "Abonelerin tüm posta listelerinden çıkmalarına ve kendilerini engellenmiş olarak işaretlemelerine izin verin?", "settings.privacy.allowExport": "Dışa aktarım için izin ver", "settings.privacy.allowExportHelp": "Abonelerin üzerlerinde toplanan verileri dışa aktarmalarına izin verin?", "settings.privacy.allowPrefs": "<PERSON><PERSON>ih <PERSON>şikliklerine izin verin", "settings.privacy.allowPrefsHelp": "Abonelerin adları ve çoklu liste abonelikleri gibi tercihlerini değiştirmelerine izin verin.", "settings.privacy.allowWipe": "<PERSON><PERSON><PERSON> i<PERSON> izin ver", "settings.privacy.allowWipeHelp": "<PERSON><PERSON><PERSON><PERSON>, abonelikleri ve veritabanındaki diğer tüm veriler dahil olmak üzere kendilerini silmesine izin verin. Kampanya görüntülemeleri ve bağlantı tıklamaları da, g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve tıklama sayıları kalır (bunlarla ilişkilendirilmiş abone olmadan), böylece istatistikler ve analizler etkilenmez.", "settings.privacy.domainBlocklist": "<PERSON> adı engelleme listesi", "settings.privacy.domainBlocklistHelp": "<PERSON>u alan adlarına sahip e-posta adreslerinin abone olmasına izin verilmez. Her satıra bir alan adı girin, örneğin: somesite.com", "settings.privacy.individualSubTracking": "Bireysel üye takibi", "settings.privacy.individualSubTrackingHelp": "Abone düzeyinde kampanya görüntülemelerini ve tıklamalarını izleyin. Devre dışı bırakıldığında, birey<PERSON> abonelere bağlanmadan görüntüleme ve tıklama izleme devam eder.", "settings.privacy.listUnsubHeader": " `List-Unsubscribe` Başlık bilgisini ekle", "settings.privacy.listUnsubHeaderHelp": "E-posta istemcilerinin kullanıcıların tek bir tıklamayla abonelikten çıkmalarına olanak tanıyan abonelik iptal başlıklarını ekleyin.", "settings.privacy.name": "Gizlilik", "settings.restart": "<PERSON><PERSON><PERSON> b<PERSON>", "settings.smtp.customHeaders": "Özel başlık bilgisi", "settings.smtp.customHeadersHelp": "Bu sunucudan gönderilen tüm iletilere eklenecek isteğe bağlı e-posta başlıkları dizisi. Örnek: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Etkinleş<PERSON><PERSON>", "settings.smtp.heloHost": "HELO İstemci adı", "settings.smtp.heloHostHelp": "Opsiyonel. Bazı SMTP sunucuları istemci adı olarak FQDN isterler. Varsayılan olarak, 'localhost' üzerine HELLO gönderilecektir. Farklı bir sunucu adı kullanılacaksa tanımlayın lütfen.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Tekrarlama", "settings.smtp.retriesHelp": "<PERSON><PERSON> hata verdiğinde tekrar deneme sayısı.", "settings.smtp.sendTest": "E-posta g<PERSON>nder", "settings.smtp.setCustomHeaders": "<PERSON>zel ba<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "settings.smtp.testConnection": "Bağlantıyı test et", "settings.smtp.testEnterEmail": "Test etmek için parolayı girin", "settings.smtp.toEmail": "Gönderilecek e-posta", "settings.title": "<PERSON><PERSON><PERSON>", "settings.updateAvailable": "<PERSON><PERSON> bir güncel sürüm {version} mevcuttur.", "subscribers.advancedQuery": "İleri düzey", "subscribers.advancedQueryHelp": "Üye attributes verisini görüntülemek için SQL verisi", "subscribers.attribs": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.attribsHelp": "Nitelikler verisi JSON map olarak tanımlı, örnek olarak:", "subscribers.blocklistedHelp": "Erişime engelli ü<PERSON>ler hiçbir zaman e-posta alamayacak.", "subscribers.confirmBlocklist": "<PERSON><PERSON><PERSON><PERSON> engelli {num} üye(leri)?", "subscribers.confirmDelete": "Sil {num} üye(leri)?", "subscribers.confirmExport": "<PERSON>ışa aktar {num} üye(leri)?", "subscribers.domainBlocklisted": "E-posta alan adı engelli listesinde.", "subscribers.downloadData": "<PERSON><PERSON>yi indir", "subscribers.email": "E-posta", "subscribers.emailExists": "E-posta zaten mevcut.", "subscribers.errorBlocklisting": "<PERSON><PERSON>, er<PERSON><PERSON><PERSON> engelli üyeleri gösterme: {error}", "subscribers.errorNoIDs": "Herhangi bir ID verilmedi.", "subscribers.errorNoListsGiven": "Liste tanımı yapılmamış.", "subscribers.errorPreparingQuery": "<PERSON>ye sorgusu hazırl<PERSON>en hata oluştu: {error}", "subscribers.errorSendingOptin": "Katılım e-postası gönderirken hata oluştu.", "subscribers.export": "Dışarı aktar", "subscribers.invalidAction": "Gerçersiz aksiyon.", "subscribers.invalidEmail": "Geçersiz e-posta.", "subscribers.invalidJSON": "Nitelik tanımı içinde geçersiz JSON.", "subscribers.invalidName": "<PERSON><PERSON><PERSON> isim.", "subscribers.listChangeApplied": "Liste değişikliği uygulandı.", "subscribers.lists": "<PERSON><PERSON><PERSON>", "subscribers.listsHelp": "Üyelerin kendilerini sildikleri listeler silinemez.", "subscribers.listsPlaceholder": "Üye olunacak liste", "subscribers.manageLists": "<PERSON><PERSON><PERSON>", "subscribers.markUnsubscribed": "Üyelikten ayrılmış olarak işaretle", "subscribers.newSubscriber": "<PERSON><PERSON>", "subscribers.numSelected": "{num} üye(ler) seçildi", "subscribers.optinSubject": "Üyeliği doğrula", "subscribers.preconfirm": "Abonelikleri önceden onaylama", "subscribers.preconfirmHelp": "Katılım e-postaları göndermeyin ve tüm liste aboneliklerini 'abone olundu' olarak işaretleyin.", "subscribers.query": "Sorg<PERSON>", "subscribers.queryPlaceholder": "E-posta veya isim", "subscribers.reset": "Sıfırla", "subscribers.selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seç {num}", "subscribers.sendOptinConfirm": "Katılım onayı gö<PERSON>in", "subscribers.sentOptinConfirm": "Katılım onayı gönderildi", "subscribers.status.blocklisted": "Engellenmiş", "subscribers.status.confirmed": "Doğrulanmış", "subscribers.status.enabled": "Etkinleş<PERSON><PERSON>", "subscribers.status.subscribed": "<PERSON><PERSON> o<PERSON>", "subscribers.status.unconfirmed": "Onaylanmadı", "subscribers.status.unsubscribed": "Üyeliği sonlandı", "subscribers.subscribersDeleted": "{num} tane <PERSON><PERSON>(ler) silindi", "templates.cantDeleteDefault": "Varsayılan taslak silinemez", "templates.default": "Varsayılan", "templates.dummyName": "Boş kampanya", "templates.dummySubject": "Boş kampanya konusu", "templates.errorCompiling": "<PERSON><PERSON>, taslak oluşturulurken: {error}", "templates.errorRendering": "Mesajı oluşturma hatası: {error}", "templates.fieldInvalidName": "İsim için yanlış uzunluk.", "templates.makeDefault": "Varsayılan <PERSON>", "templates.newTemplate": "<PERSON><PERSON>", "templates.placeholderHelp": "<PERSON>r tutucu {placeholder} taslak içinde sadece bir kere olmalıdır.", "templates.preview": "<PERSON><PERSON><PERSON><PERSON>", "templates.rawHTML": "Ham HTML", "templates.subject": "Subject", "users.login": "<PERSON><PERSON><PERSON>", "users.logout": "Çıkış"}