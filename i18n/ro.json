{"_.code": "ro", "_.name": "Română (ro)", "admin.errorMarshallingConfig": "E<PERSON>re la setarea configurației: {eroare}", "analytics.count": "<PERSON><PERSON><PERSON><PERSON>", "analytics.fromDate": "De <PERSON>", "analytics.invalidDates": "Invalid `de la` sau `la` dată.", "analytics.isUnique": "The counts are unique per subscriber.", "analytics.links": "<PERSON><PERSON>", "analytics.nonUnique": "The counts are non-unique as individual subscriber tracking is turned off.", "analytics.title": "<PERSON><PERSON><PERSON>", "analytics.toDate": "La", "bounces.source": "Sursa", "bounces.unknownService": "<PERSON><PERSON><PERSON>.", "bounces.view": "Viz<PERSON><PERSON><PERSON>[ respingeri", "campaigns.addAltText": "Adaug[ un text simplu alternativ", "campaigns.archive": "Archive", "campaigns.archiveEnable": "Publish to public archive", "campaigns.archiveHelp": "Publish (running, paused, finished) the campaign message on the public archive.", "campaigns.archiveMeta": "Campaign metadata", "campaigns.archiveMetaHelp": "Dummy subscriber data to use in the public message including name, email, and any optional attributes used in the campaign message or template.", "campaigns.cantUpdate": "Nu se poate actualiza o campaniedifuzată sau terminată", "campaigns.clicks": "<PERSON><PERSON><PERSON>", "campaigns.confirmDelete": "Sterge {nume}", "campaigns.confirmSchedule": "Această campanie va începe la data și ora programată. Programezi acum?", "campaigns.confirmSwitchFormat": "Conținutul poate pierde formatarea. Continui?", "campaigns.content": "Continut", "campaigns.contentHelp": "Conținut aici", "campaigns.continue": "Con<PERSON><PERSON><PERSON>", "campaigns.copyOf": "Copie a {nume}", "campaigns.customHeadersHelp": "Array of custom headers to attach to outgoing messages. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Dată și oră", "campaigns.ended": "Terminat", "campaigns.errorSendTest": "Eroare trimitere test: {erore}", "campaigns.fieldInvalidBody": "<PERSON><PERSON>re la copmilarea corpului campaniei: {eroere}", "campaigns.fieldInvalidFromEmail": "`from_email` invalid.", "campaigns.fieldInvalidListIDs": "Invalid list IDs.", "campaigns.fieldInvalidMessenger": "Messenger necunoscut {nume}.", "campaigns.fieldInvalidName": "Lungime nevalidă pentru nume", "campaigns.fieldInvalidSendAt": "Data programată ar trebui să fie în viitor.", "campaigns.fieldInvalidSubject": "Lungime nevalida pentru subiect.", "campaigns.formatHTML": "Format HTML", "campaigns.fromAddress": "De la adresa", "campaigns.fromAddressPlaceholder": "Numele tau <<EMAIL>>", "campaigns.invalid": "<PERSON><PERSON><PERSON>", "campaigns.invalidCustomHeaders": "Invalid custom headers: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Campania are nevoie de o dată pentru a fi programată", "campaigns.newCampaign": "Campanie no<PERSON>", "campaigns.noKnownSubsToTest": "Nu există abonați cunoscuți pentru a testa.", "campaigns.noOptinLists": "Nu s-au găsit liste de înscriere pentru a crea campania.", "campaigns.noSubs": "Nu exista abonați în listele selectate pentru a crea campania.", "campaigns.noSubsToTest": "Nu există abonași pe care să îi vizați.", "campaigns.notFound": "Campania nu a fost găsită.", "campaigns.onlyActiveCancel": "Doar campaniile active pot fi anulate.", "campaigns.onlyActivePause": "Doar campaniile active pot fi întrerupte.", "campaigns.onlyDraftAsScheduled": "Doar campaniile schițe pot fi programate.", "campaigns.onlyPausedDraft": "Se pot începe doar campaniile și schițele întrerupte.", "campaigns.onlyScheduledAsDraft": "Numai campaniile programate pot fi salvate ca schițe", "campaigns.pause": "Pauză", "campaigns.plainText": "Text simplu", "campaigns.preview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.progress": "Progres", "campaigns.queryPlaceholder": "Numele sau subiectul", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML brut", "campaigns.removeAltText": "Eliminați un mesaj text alternativ", "campaigns.richText": "Text îmbogățit", "campaigns.schedule": "<PERSON><PERSON>za campanie", "campaigns.scheduled": "Programat", "campaigns.send": "<PERSON><PERSON><PERSON>", "campaigns.sendLater": "Trimite mai târziu", "campaigns.sendTest": "Trimite mesaj de test", "campaigns.sendTestHelp": "Apăsați Enter după ce ați introdus o adresă pentru a adăuga mai mulți destinatari. Adresele trebuie să aparțină abonaților existenți.", "campaigns.sendToLists": "Liste de trimis", "campaigns.sent": "Trimise", "campaigns.start": "Pornește campania", "campaigns.started": "\"{nume}\" început", "campaigns.startedAt": "Început", "campaigns.stats": "Statistici", "campaigns.status.cancelled": "<PERSON><PERSON><PERSON>", "campaigns.status.draft": "Schiță", "campaigns.status.finished": "Terminat", "campaigns.status.paused": "Întrerupt", "campaigns.status.running": "Rulează", "campaigns.status.scheduled": "Programat", "campaigns.statusChanged": "\"{nume}\" este {stare}", "campaigns.subject": "Subiect", "campaigns.testDisabled": "Enter password to test", "campaigns.testEmails": "<PERSON><PERSON><PERSON>", "campaigns.testSent": "Mesaju de test a fost trimis", "campaigns.timestamps": "<PERSON><PERSON><PERSON>im<PERSON>", "campaigns.trackLink": "Track link", "campaigns.views": "Vizualiz<PERSON><PERSON>", "dashboard.campaignViews": "Vizualizări ale campaniei", "dashboard.linkClicks": "Clickuri pe link", "dashboard.messagesSent": "<PERSON><PERSON>", "dashboard.orphanSubs": "<PERSON><PERSON><PERSON>", "email.data.info": "O copie a tuturor datelor înregistrate pe dvs. este atașată ca fișier în format JSON. Poate fi vizualizat într-un editor de text.", "email.data.title": "Datele tale", "email.optin.confirmSub": "Confirmă abonarea", "email.optin.confirmSubHelp": "Confirmă abonamentul făcând click pe butonul de mai jos.", "email.optin.confirmSubInfo": "Ai fost adăugat la urmatoarele liste:", "email.optin.confirmSubTitle": "Confirmă abonarea", "email.optin.confirmSubWelcome": "Salut", "email.optin.privateList": "Lista privata", "email.status.campaignReason": "Motiv", "email.status.campaignSent": "Trimite", "email.status.campaignUpdateTitle": "Actual<PERSON><PERSON> campanie", "email.status.importFile": "Fisier", "email.status.importRecords": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email.status.importTitle": "Actualizare import", "email.status.status": "Stare", "email.unsub": "<PERSON><PERSON><PERSON><PERSON>", "email.unsubHelp": "Nu dorești să primești aceste emailuri?", "email.viewInBrowser": "View in browser", "forms.formHTML": "Formular HTML", "forms.formHTMLHelp": "Utilizați următorul HTML pentru a afișa un formular de abonament pe o pagină web externă. Formularul trebuie să aibă câmpul de e-mail și unul sau mai multe câmpuri `l` (listă UUID). Câmpul de nume este opțional.", "forms.noPublicLists": "Nu există liste publice pentru a genera un formular.", "forms.publicLists": "Liste publice", "forms.publicSubPage": "Pagina publică de abonament", "forms.selectHelp": "Selecteaza listele de adăugat la formular.", "forms.title": "Formulare", "globals.buttons.add": "Adaugă", "globals.buttons.addNew": "Adaugă nou", "globals.buttons.back": "Back", "globals.buttons.cancel": "Anulează", "globals.buttons.clone": "Clonează", "globals.buttons.close": "<PERSON><PERSON><PERSON>", "globals.buttons.continue": "Continua", "globals.buttons.delete": "Șterge", "globals.buttons.deleteAll": "St<PERSON>ge tot", "globals.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.enabled": "Activat", "globals.buttons.insert": "Insert", "globals.buttons.learnMore": "Află mai multe", "globals.buttons.more": "More", "globals.buttons.new": "Nou", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Elimină", "globals.buttons.save": "Salvează", "globals.buttons.saveChanges": "Salvează schimbările", "globals.days.0": "<PERSON>", "globals.days.1": "<PERSON>", "globals.days.2": "Ma", "globals.days.3": "<PERSON>", "globals.days.4": "<PERSON>", "globals.days.5": "Vi", "globals.days.6": "Sa", "globals.days.7": "Sat", "globals.fields.createdAt": "<PERSON><PERSON><PERSON>", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Nume", "globals.fields.status": "Stare", "globals.fields.type": "Tip", "globals.fields.updatedAt": "Actualizat", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Ești sigur/ă?", "globals.messages.confirmDiscard": "Renunți la modificări?", "globals.messages.created": "\"{nume}\" creat", "globals.messages.deleted": "\"{nume}\" șters", "globals.messages.deletedCount": "{nume} ({număr}) ș<PERSON>", "globals.messages.done": "Done", "globals.messages.emptyState": "Nimic aici", "globals.messages.errorCreating": "Eroare creare {nume}: {eroare}", "globals.messages.errorDeleting": "<PERSON><PERSON><PERSON>e {nume}: {eroare}", "globals.messages.errorFetching": "<PERSON><PERSON>re preluare {nume}: {eroare}", "globals.messages.errorInvalidIDs": "Unul sau mai multe ID-uri nu sunt valide: {eroare}", "globals.messages.errorUUID": "Eroare generare UUID: {eroare}", "globals.messages.errorUpdating": "Eroare actualizare {nume}: {eroare}", "globals.messages.internalError": "Eroare server intern", "globals.messages.invalidData": "Date invalide", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID(uri) invalide", "globals.messages.invalidUUID": "UUID(uri) invalide", "globals.messages.missingFields": "<PERSON><PERSON>: {nume}", "globals.messages.notFound": "{nume} nu a fost găsit", "globals.messages.passwordChange": "Introdu o valoare de modificat", "globals.messages.updated": "\"{nume}\" actualizat", "globals.months.1": "<PERSON>", "globals.months.10": "Oct", "globals.months.11": "<PERSON>i", "globals.months.12": "Dec", "globals.months.2": "Feb", "globals.months.3": "Mar", "globals.months.4": "Apr", "globals.months.5": "<PERSON>", "globals.months.6": "<PERSON><PERSON>", "globals.months.7": "<PERSON><PERSON>", "globals.months.8": "Aug", "globals.months.9": "Sep", "globals.states.off": "Off", "globals.terms.all": "All", "globals.terms.analytics": "Analitice", "globals.terms.bounce": "Respins | Respinse", "globals.terms.bounces": "Respinse", "globals.terms.campaign": "Campanie | Campanii", "globals.terms.campaigns": "Campanii", "globals.terms.dashboard": "Dashboard", "globals.terms.day": "Day | Days", "globals.terms.hour": "Hour | Hours", "globals.terms.list": "Listă | Liste", "globals.terms.lists": "Liste", "globals.terms.media": "Media | Media", "globals.terms.messenger": "Messenger | Messengers", "globals.terms.messengers": "Messengers", "globals.terms.minute": "Minute | Minutes", "globals.terms.month": "Month | Months", "globals.terms.second": "Second | Seconds", "globals.terms.settings": "<PERSON><PERSON><PERSON>", "globals.terms.subscriber": "Abonat | Abonați", "globals.terms.subscribers": "Abona<PERSON><PERSON>", "globals.terms.subscriptions": "Subscription | Subscriptions", "globals.terms.tag": "Etichetă | Etichete", "globals.terms.tags": "Etichete", "globals.terms.template": "Șablon | Șabloane", "globals.terms.templates": "Șabloane", "globals.terms.tx": "Transactional | Transactional", "globals.terms.year": "Year | Years", "import.alreadyRunning": "Un import rulează deja. Așteptă să se termine sau oprește-l înainte de a încerca din nou.", "import.blocklist": "Lista de blocați", "import.csvDelim": "Delimitator CSV", "import.csvDelimHelp": "Delimitatoril implicit este virgula.", "import.csvExample": "Exemplu CSV brut", "import.csvFile": "Fisier CSV sau ZIP", "import.csvFileHelp": "Fă click sau trage aici un fisier CSV sau ZIP", "import.errorCopyingFile": "<PERSON><PERSON><PERSON> copiere fișier: {eroare}", "import.errorProcessingZIP": "Eroare procesare fișier ZIP: {eroare}", "import.errorStarting": "Eroare începere import: {eroare}", "import.importDone": "Terminat", "import.importStarted": "Import început", "import.instructions": "Instrucțiuni", "import.instructionsHelp": "Încarcă un fișier CSV sau un fișier ZIP care să conțina singur fișier CSV cu abonații importați în bloc. Fișierul CSV ar trebui să aibă următoarele anteturi cu numele exacte ale coloanelor. atributele (opțional) ar trebui să fie un șir JSON valid cu ghilimele duble.", "import.invalidDelim": "Delimitatorul ar trebui sa fie un singur caracter.", "import.invalidFile": "<PERSON><PERSON><PERSON> invalid: {er<PERSON><PERSON>}", "import.invalidMode": "Mod invalid", "import.invalidParams": "Parametrii invalizi: {eroare}", "import.invalidSubStatus": "Stare abonament invalidă", "import.listSubHelp": "Liste de abonare.", "import.mode": "Mod", "import.overwrite": "Supra<PERSON><PERSON><PERSON>?", "import.overwriteHelp": "Suprascrie numele, atributele, starea abonamentului a abonaților existenți?", "import.recordsCount": "{număr} / {total} înregistrari", "import.stopImport": "Stop import", "import.subscribe": "<PERSON><PERSON><PERSON>", "import.title": "Importă abonați", "import.upload": "Încar<PERSON><PERSON>", "lists.confirmDelete": "Ești sigur/a? Asta nu șterge abonații.", "lists.confirmSub": "Confirmă abonamentele pentru {nume}", "lists.invalidName": "Nume invalid", "lists.newList": "Listă nouă", "lists.optin": "Înscrie-te", "lists.optinHelp": "Înscrierea dublă trimite un e-mail către abonat solicitând confirmarea. În listele de înscriere dublă, campaniile sunt trimise numai abonaților confirmați.", "lists.optinTo": "Înscrie-te {nume}", "lists.optins.double": "Înscriere dublă", "lists.optins.single": "Înscriere unică", "lists.sendCampaign": "Trimite campania", "lists.sendOptinCampaign": "Trimite campania de înscriere.", "lists.type": "Tip", "lists.typeHelp": "Listele publice sunt deschise lumii pentru a se abona și numele lor pot apărea pe pagini publice, cum ar fi pagina de gestionare a abonamentelor.", "lists.types.private": "Privat", "lists.types.public": "Public", "logs.title": "<PERSON><PERSON><PERSON>", "maintenance.help": "Some actions may take a while to complete depending on the amount of data.", "maintenance.maintenance.unconfirmedOptins": "Unconfirmed opt-in subscriptions", "maintenance.olderThan": "Older than", "maintenance.title": "Maintenance", "maintenance.unconfirmedSubs": "Unconfirmed subscriptions older than {name} days.", "media.errorReadingFile": "<PERSON><PERSON>re citire fisier: {eroare}", "media.errorResizing": "Eroare redimensionare imagine: {eroare}", "media.errorSavingThumbnail": "<PERSON><PERSON>re la salvarea miniaturii: {eroare}", "media.errorUploading": "<PERSON><PERSON><PERSON>: {eroare}", "media.invalidFile": "<PERSON><PERSON><PERSON> invalid: {er<PERSON><PERSON>}", "media.title": "Media", "media.unsupportedFileType": "Tipuld e fișier nu este suportat ({type})", "media.upload": "Încar<PERSON><PERSON>", "media.uploadHelp": "Click sau trage una sau mai multe imagini aici", "media.uploadImage": "Încar<PERSON>ă imagine", "menu.allCampaigns": "Toate campaniile", "menu.allLists": "Toate listele", "menu.allSubscribers": "Toți abonații", "menu.dashboard": "Dashboard", "menu.forms": "Formulare", "menu.import": "Import", "menu.logs": "<PERSON><PERSON><PERSON>", "menu.maintenance": "Maintenance", "menu.media": "Media", "menu.newCampaign": "Creaza nou", "menu.settings": "<PERSON><PERSON><PERSON>", "public.archiveEmpty": "No archived messages yet.", "public.archiveTitle": "Mailing list archive", "public.blocklisted": "Permanently unsubscribed.", "public.campaignNotFound": "Mesajul emailului nu a fost găsit.", "public.confirmOptinSubTitle": "Confirmă abonarea", "public.confirmSub": "Confirmă abonarea", "public.confirmSubInfo": "Ai fost adaugat/ă la listele urmă<PERSON>areȘ", "public.confirmSubTitle": "Confirmă", "public.dataRemoved": "Abonamentele si toate datele asociate au fost sterse.", "public.dataRemovedTitle": "<PERSON> ș<PERSON>e", "public.dataSent": "Datele tale ti-au fost trimise atasate la email.", "public.dataSentTitle": "Date trimise prin email", "public.errorFetchingCampaign": "Eroare la preluarea mesajului de email", "public.errorFetchingEmail": "Mesajul emailului nu a fost găsit", "public.errorFetchingLists": "Eroare la preluarea listelor. Încearcă din nou.", "public.errorProcessingRequest": "Eroare la procesarea cererii. Încearcă din nou.", "public.errorTitle": "Eroare", "public.invalidFeature": "Această functie nu este disponibilă", "public.invalidLink": "Link invalid", "public.managePrefs": "Manage preferences", "public.managePrefsUnsub": "Uncheck lists to unsubscribe from them.", "public.noListsAvailable": "Nu există liste disponibile pentru abonare.", "public.noListsSelected": "Nu există liste valide pentru abonare.", "public.noSubInfo": "Nu există abonamente de confirmat", "public.noSubTitle": "Fără a<PERSON>e", "public.notFoundTitle": "Nu a fost găsit", "public.prefsSaved": "Your preferences have been saved.", "public.privacyConfirmWipe": "<PERSON><PERSON>r vrei să ș<PERSON>gi definitiv toate datele legate de abonament?", "public.privacyExport": "Exportă datele tale", "public.privacyExportHelp": "O copie a datelor iti vor fi trimise prin email.", "public.privacyTitle": "Confidențialitate și date", "public.privacyWipe": "Șterge-<PERSON><PERSON> datele", "public.privacyWipeHelp": "Șterge definitiv toate abonamentele si datele aferente din baza de date.", "public.sub": "Abonează-te", "public.subConfirmed": "Abonat/ă cu succes.", "public.subConfirmedTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.subName": "Nume (optional)", "public.subNotFound": "Abonamentul nu a fost găsit.", "public.subOptinPending": "Ti-a fost trimis un email pentru a confirma abonamentul/abonamentele.", "public.subPrivateList": "Listă privata", "public.subTitle": "Abonează-te", "public.unsub": "Dezabonează-te", "public.unsubFull": "De asem<PERSON>, dezabonează-te de la emailurile viitoare.", "public.unsubHelp": "Dorești să te dezabonezi de la această listă de email?", "public.unsubTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.unsubbedInfo": "Te-ai dezabonat cu succes.", "public.unsubbedTitle": "Dezabonat", "public.unsubscribeTitle": "Dezabonează-te de la lista de discuții", "settings.appearance.adminHelp": "Custom CSS to apply to the admin UI.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "Custom CSS", "settings.appearance.customJS": "Custom JavaScript", "settings.appearance.name": "Appearance", "settings.appearance.publicHelp": "Custom CSS and JavaScript to apply to the public pages.", "settings.appearance.publicName": "Public", "settings.bounces.action": "Acțiune", "settings.bounces.blocklist": "Lista de blocare", "settings.bounces.count": "<PERSON><PERSON><PERSON><PERSON>sp<PERSON>", "settings.bounces.countHelp": "Numa<PERSON>l de respingeri per abonat", "settings.bounces.delete": "Șterge", "settings.bounces.enable": "Activează procesarea respingerilor", "settings.bounces.enableMailbox": "Activează casuța poștală de respingere", "settings.bounces.enableSES": "Activează SES", "settings.bounces.enableSendgrid": "Activează SendGrid", "settings.bounces.enableWebhooks": "Activează webhookurile de respingere", "settings.bounces.enabled": "Activat", "settings.bounces.folder": "Dosar", "settings.bounces.folderHelp": "Numele folderului IMAP de scanat. De exemplu: Mesaje primite.", "settings.bounces.invalidScanInterval": "Intervalul de scanare al respingerilor treubie sa fie de minim 1 minut.", "settings.bounces.name": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Interval de scanare", "settings.bounces.scanIntervalHelp": "Interval la care căsuța poștală de respingeri trebuie scanată pentru respingeri (s pentru secunde, m pentru minut).", "settings.bounces.sendgridKey": "<PERSON><PERSON><PERSON> ", "settings.bounces.type": "Tip", "settings.bounces.username": "Utilizator", "settings.confirmRestart": "Asigura-te ca difuzarea campaniilor este întreruptă. Repornești?", "settings.duplicateMessengerName": "Nume duplicat al mesagerului: {nume}", "settings.errorEncoding": "<PERSON><PERSON><PERSON> la encodarea setărilor: {eroare}", "settings.errorNoSMTP": "Trebuie activat cel putin un bloc SMTP", "settings.general.adminNotifEmails": "Notificare emailuri admin", "settings.general.adminNotifEmailsHelp": "Lista separată prin virgulă a adreselor de e-mail către care ar trebui trimise notificări de administrator, cum ar fi actualizări de import, finalizarea campaniei, eșec etc.", "settings.general.checkUpdates": "Verifică actualizări", "settings.general.checkUpdatesHelp": "Verifică periodic lansările de aplicații noi și notifică.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publish campaigns on which archiving is enabled on the public website.", "settings.general.enablePublicSubPage": "Activează pagina de abonament public", "settings.general.enablePublicSubPageHelp": "Afișează o pagină de abonament publică cu toate listele publice pentru ca oamenii să se aboneze.", "settings.general.faviconURL": "URL favicon", "settings.general.faviconURLHelp": "(Opțional) adresa URL completă către pictograma statică care trebuie afișată în vizualizarea către utilizator, cum ar fi pagina de dezabonare. ", "settings.general.fromEmail": "Email implicit `de la`", "settings.general.fromEmailHelp": "Email implicit `de la` pentru a fi afișat pe e-mailurile din campanie. Acest lucru poate fi modificat pentru fiecare campanie.", "settings.general.language": "Limbă", "settings.general.logoURL": "URL logo", "settings.general.logoURLHelp": "(Opțional) URL complet către sigla statică care trebuie afișată în vizualizarea către utilizator, cum ar fi pagina de dezabonare.", "settings.general.name": "General", "settings.general.rootURL": " URL rădăcină", "settings.general.rootURLHelp": "Adresa URL publică a instalării (fără bară finală).", "settings.general.sendOptinConfirm": "Trimite confirmarea de înscriere", "settings.general.sendOptinConfirmHelp": "Trimite un e-mail de confirmare de înscriere atunci când abonații se înscriu prin formularul public sau când sunt adăugați de către administrator.", "settings.general.siteName": "Site name", "settings.invalidMessengerName": "Numele mesagerului nu este valid.", "settings.mailserver.authProtocol": "Protocol de autentificare", "settings.mailserver.host": "Gazdă", "settings.mailserver.hostHelp": "Adresa gazdei serverului SMTP.", "settings.mailserver.idleTimeout": "Timp de inactivitate", "settings.mailserver.idleTimeoutHelp": "Este timpul să asștepți o activitate nouă pe conexiune înainte de a o închide și a o scoate din unificator (s pentru secundă, m pentru minut).", "settings.mailserver.maxConns": "Conexiuni maxime", "settings.mailserver.maxConnsHelp": "Conexiuni maxime simultane la server", "settings.mailserver.password": "Pa<PERSON><PERSON>", "settings.mailserver.passwordHelp": "Intră pentru a schimba", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "Portul serverului SMTP", "settings.mailserver.skipTLS": "Omite verificare TLS", "settings.mailserver.skipTLSHelp": "Omite verificarea numelui de gazdă pe certificatul TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Activează STARTTLS.", "settings.mailserver.username": "Utilizator", "settings.mailserver.waitTimeout": "Așteaptă expirarea", "settings.mailserver.waitTimeoutHelp": "Timpul de așteptare pentru o activitate nouă pe o conexiune înainte de a o închide și a o scoate din piscină (s pentru secundă, m pentru minut).", "settings.media.provider": "Furnizr", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "<PERSON>", "settings.media.s3.bucketPathHelp": "Calea din interiorul bucket pentru încărcarea fișierelor. Implicit este /", "settings.media.s3.bucketType": "Tipul de bucket", "settings.media.s3.bucketTypePrivate": "Privat", "settings.media.s3.bucketTypePublic": "Public", "settings.media.s3.key": "Cheia de acces AWS", "settings.media.s3.publicURL": "Custom public URL (optional)", "settings.media.s3.publicURLHelp": "Custom S3 domain to use for image links instead of the default S3 backend URL.", "settings.media.s3.region": "Regiune", "settings.media.s3.secret": "Secret de acces AWS", "settings.media.s3.uploadExpiry": "Expirarea încărcării", "settings.media.s3.uploadExpiryHelp": "(Opțional) Specifică TTL (în secunde) pentru adresa URL presemnată generată. Se aplică numai pentru bucketurile private (s, m, h, d pentru secunde, minute, ore, zile).", "settings.media.s3.url": "URL backend S3", "settings.media.s3.urlHelp": "Schimbă numai dacă folosești un backend personalizat compatibil S3, cum ar fi Minio.", "settings.media.title": "Încărcări media", "settings.media.upload.path": "Calea de încărcare", "settings.media.upload.pathHelp": "Calea către directorul în care va fi încărcat media.", "settings.media.upload.uri": "Încarcă URI", "settings.media.upload.uriHelp": "Încarcă un URI care este vizibil pentru lumea exterioară. Mediile încărcate în upload_path vor fi accesibile publicului sub {root_url}, de exemplu, https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "Conexiuni maxime", "settings.messengers.maxConnsHelp": "Conexiuni maxime simultane la server.", "settings.messengers.messageSaved": "Setari Salvate. Se reîncarcă aplicația ...", "settings.messengers.name": "Mesageri", "settings.messengers.nameHelp": "ex: my-sms. Alfanumeric / liniuță.", "settings.messengers.password": "Pa<PERSON><PERSON>", "settings.messengers.retries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.messengers.retriesHelp": "De câte ori trebuie să reîncerci când un mesaj eșuează.", "settings.messengers.skipTLSHelp": "Omite verificarea numelui de gazdă pe certificatul TLS.", "settings.messengers.timeout": "Timp de inactivitate", "settings.messengers.timeoutHelp": "Timpul de așteptare pentru o activitate nouă pe o conexiune înainte de a o închide și a o scoate din piscină (s pentru secundă, m pentru minut).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Adresa URL rădăcină a serverului Postback.", "settings.messengers.username": "Utilizator", "settings.needsRestart": "Setările s-au schimbat. <PERSON><PERSON>rerupe toate campaniile care rulează și reporniți aplicația", "settings.performance.batchSize": "Di<PERSON><PERSON><PERSON>a lotului", "settings.performance.batchSizeHelp": "Numărul de abonați care pot fi extrași din baza de date într-o singură iterație. Fiecare iterație atrage abonații din baza de date, le trimite mesaje și apoi trece la următoarea iterație pentru a extrage următorul lot. Acest lucru ar trebui să fie în mod ideal mai mare decât debitul maxim realizabil (concurență * rată_mesaj).", "settings.performance.concurrency": "Concurență", "settings.performance.concurrencyHelp": "Lucrător simultan maxim (fire) care va încerca să trimită mesaje simultan.", "settings.performance.maxErrThreshold": "Pragul maxim de eroare", "settings.performance.maxErrThresholdHelp": "Numărul de erori (de exemplu: expirarea timpului SMTP în timpul e-mailurilor) o campanie în desfășurare ar trebui să tolereze înainte ca aceasta să fie întreruptă pentru investigație manuală sau intervenție. Setați la 0 pentru a nu face pauză niciodată.", "settings.performance.messageRate": "<PERSON><PERSON>", "settings.performance.messageRateHelp": "Numărul maxim de mesaje care trebuie trimise pe secundă per lucrător într-o secundă. Dacă concurența = 10 și rată_mesaj = 10, atunci până la 10x10 = 100 mesaje pot fi împinse în fiecare secundă. Acest lucru, împreună cu concurența, ar trebui modificat pentru a menține mesajele nete care se difuzează pe secundă sub limitele de tarifare ale serverelor de mesaje țintă, dacă există.", "settings.performance.name": "Performanță", "settings.performance.slidingWindow": "Activează limita ferestrei glisante", "settings.performance.slidingWindowDuration": "<PERSON><PERSON><PERSON>", "settings.performance.slidingWindowDurationHelp": "Durata perioadei ferestrei glisante (m pentru minut, h pentru oră).", "settings.performance.slidingWindowHelp": "Limitați numărul total de mesaje care sunt trimise într-o anumită perioadă. La atingerea acestei limite, mesajele sunt reținute de la trimitere până când se deschide fereastra de timp.", "settings.performance.slidingWindowRate": "<PERSON><PERSON> maxime", "settings.performance.slidingWindowRateHelp": "Numărul maxim de mesaje de trimis în timpul ferestrei.", "settings.privacy.allowBlocklist": "Permite blocarea listelro", "settings.privacy.allowBlocklistHelp": "Permite abonaților să se dezaboneze de la toate listele de e-mail și să se marcheze ca listă de blocuri?", "settings.privacy.allowExport": "Permite exportul", "settings.privacy.allowExportHelp": "Permite abonaților să exporte datele colectate pe aceștia?", "settings.privacy.allowPrefs": "Allow preference changes", "settings.privacy.allowPrefsHelp": "Allow subscribers to change preferences such as their names and multiple list subscriptions.", "settings.privacy.allowWipe": "Permite ștergerea", "settings.privacy.allowWipeHelp": "Permite abonaților să se șteargă, inclusiv abonamentele lor și toate celelalte date din baza de date. Vizualizările campaniei și clicurile pe linkuri sunt, de asemenea, eliminate, în timp ce numărul de vizualizări și clicuri rămâne (fără niciun abonat asociat acestora), astfel încât statisticile și analizele să nu fie afectate.", "settings.privacy.domainBlocklist": "Domain blocklist", "settings.privacy.domainBlocklistHelp": "E-mail addresses with these domains are disallowed from subscribing. Enter one domain per line, eg: somesite.com", "settings.privacy.individualSubTracking": "Urmărirea individuală a abonaților", "settings.privacy.individualSubTrackingHelp": "Urmărește vizualizările și clicurile campaniei la nivel de abonați. Când este de<PERSON>, urmărirea vizualizării și a clicurilor continuă fără a fi conectată la abonați individuali.", "settings.privacy.listUnsubHeader": "Include antetul `Dezabonare-lista` ", "settings.privacy.listUnsubHeaderHelp": "Include anteturi de dezabonare care permit clienților de e-mail să permită utilizatorilor să se dezaboneze printr-un singur clic.", "settings.privacy.name": "Confidențialitate", "settings.restart": "<PERSON><PERSON>", "settings.smtp.customHeaders": "Anteturi personalizate", "settings.smtp.customHeadersHelp": "Matrice opțională de antete de e-mail pentru a include în toate mesajele trimise de pe acest server. de exemplu: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Activat", "settings.smtp.heloHost": "HELO nume de gazdă", "settings.smtp.heloHostHelp": "Opțional. Unele servere SMTP necesită un FQDN în numele gazdei. În mod implicit, <PERSON><PERSON>ă ziua merge cu `localhost`. Setați acest lucru dacă trebuie utilizat un nume de gazdă personalizat.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.smtp.retriesHelp": "De câte ori trebuie să reîncercați când un mesaj eșuează.", "settings.smtp.sendTest": "Send e-mail", "settings.smtp.setCustomHeaders": "Setează  anteturi personalizate", "settings.smtp.testConnection": "Test connection", "settings.smtp.testEnterEmail": "Enter password to test", "settings.smtp.toEmail": "To e-mail", "settings.title": "<PERSON><PERSON><PERSON>", "settings.updateAvailable": "Este disponibilă o nouă actualizare {versiune}.", "subscribers.advancedQuery": "Avansat", "subscribers.advancedQueryHelp": "Expresie SQL parțială pentru interogarea atributelor abonatului", "subscribers.attribs": "Atribute", "subscribers.attribsHelp": "Atributele sunt definite ca o hartă JSON, de exemplu:", "subscribers.blocklistedHelp": "Abonații din lista neagră nu vor primi niciodată e-mailuri.", "subscribers.confirmBlocklist": "<PERSON><PERSON><PERSON> {număr} a<PERSON><PERSON><PERSON><PERSON>?", "subscribers.confirmDelete": "<PERSON><PERSON><PERSON> {număr} abon<PERSON><PERSON><PERSON>?", "subscribers.confirmExport": "Exporți {număr} abonați?", "subscribers.domainBlocklisted": "The e-mail domain is blocklisted.", "subscribers.downloadData": "Des<PERSON><PERSON><PERSON> datele", "subscribers.email": "E-mail", "subscribers.emailExists": "Emailul există deja.", "subscribers.errorBlocklisting": "<PERSON><PERSON><PERSON> a<PERSON>ți listă neagră: {eroare}", "subscribers.errorNoIDs": "Nu exista ID atribuit.", "subscribers.errorNoListsGiven": "<PERSON>u sunt oferite liste.", "subscribers.errorPreparingQuery": "Eroare la pregătirea interogării abonatului: {eroare}", "subscribers.errorSendingOptin": "Eroare la trimiterea e-mailului de înscriere.", "subscribers.export": "Export", "subscribers.invalidAction": "Acțiune invalidă", "subscribers.invalidEmail": "Email invalid", "subscribers.invalidJSON": "JSON invalid în atribute.", "subscribers.invalidName": "Nume invalid.", "subscribers.listChangeApplied": "Modificare listă aplicată", "subscribers.lists": "Liste", "subscribers.listsHelp": "Listele din care abonații s-au dezabonat nu pot fi eliminate.", "subscribers.listsPlaceholder": "Liste la care să te abonezi", "subscribers.manageLists": "Administrează liste", "subscribers.markUnsubscribed": "Marchează ca dezabonat", "subscribers.newSubscriber": "Abonat nou", "subscribers.numSelected": "{număr} abonați selectați", "subscribers.optinSubject": "Confirmă abonarea", "subscribers.preconfirm": "Pre-confirm subscriptions", "subscribers.preconfirmHelp": "Don't send opt-in e-mails and mark all list subscriptions as 'subscribed'.", "subscribers.query": "Interogare", "subscribers.queryPlaceholder": "Email sau nume", "subscribers.reset": "Reset", "subscribers.selectAll": "Select<PERSON>ză tot {număr}", "subscribers.sendOptinConfirm": "Send opt-in confirmation", "subscribers.sentOptinConfirm": "Opt-in confirmation sent", "subscribers.status.blocklisted": "În lista neagră", "subscribers.status.confirmed": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.status.enabled": "Activfat", "subscribers.status.subscribed": "Abonat", "subscribers.status.unconfirmed": "Neconfirmat", "subscribers.status.unsubscribed": "Dezabonat", "subscribers.subscribersDeleted": "{număr} a<PERSON><PERSON> ș<PERSON>i", "templates.cantDeleteDefault": "Nu se poate șterge șablonul implicit", "templates.default": "Mod implicit", "templates.dummyName": "Campanie fictivă", "templates.dummySubject": "Subiectul campaniei fictive", "templates.errorCompiling": "<PERSON><PERSON>re la compilarea șablonului: {eroare}", "templates.errorRendering": "<PERSON><PERSON>re la redarea mesajului: {eroare}", "templates.fieldInvalidName": "Lungime invalidă pentru nume.", "templates.makeDefault": "<PERSON><PERSON><PERSON><PERSON> implicit", "templates.newTemplate": "Template nou", "templates.placeholderHelp": "Substituentul {placeholder} ar trebui să apară exact o dată în șablon.", "templates.preview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templates.rawHTML": "HTML brut", "templates.subject": "Subject", "users.login": "<PERSON><PERSON>", "users.logout": "Logout"}