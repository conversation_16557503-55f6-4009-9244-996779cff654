{"_.code": "fr", "_.name": "Français (fr)", "admin.errorMarshallingConfig": "Erreur lors de la lecture de la configuration : {error}", "analytics.count": "<PERSON><PERSON><PERSON>", "analytics.fromDate": "<PERSON><PERSON><PERSON>", "analytics.invalidDates": "Dates invalides `depuis` ou `au`.", "analytics.isUnique": "Les comptes sont uniques par abonné.", "analytics.links": "<PERSON><PERSON>", "analytics.nonUnique": "Les comptes ne sont pas uniques car le suivi individuel des abonnés est désactivé.", "analytics.title": "Analyses", "analytics.toDate": "Au", "bounces.source": "Source", "bounces.unknownService": "Service inconnu.", "bounces.view": "Voir les rebonds", "campaigns.addAltText": "Ajouter un message alternatif en texte brut", "campaigns.archive": "Archive", "campaigns.archiveEnable": "Publier dans l'archive publique", "campaigns.archiveHelp": "Publier (en cours, en pause, terminé) le message de la campagne sur l'archive publique.", "campaigns.archiveMeta": "Métadonnées de la campagne", "campaigns.archiveMetaHelp": "Données d'abonné fictives à utiliser dans le message public, notamment le nom, l'adresse électronique et tout attribut facultatif utilisé dans le message ou le modèle de la campagne.", "campaigns.cantUpdate": "Impossible de mettre à jour une campagne en cours ou terminée.", "campaigns.clicks": "C<PERSON>s", "campaigns.confirmDelete": "Supprimer la campagne {name}", "campaigns.confirmSchedule": "Cette campagne démarrera automatiquement à la date et à l'heure planifiées. Confirmer la planification ?", "campaigns.confirmSwitchFormat": "Le contenu peut perdre sa mise en forme. Continuer ?", "campaigns.content": "Contenu", "campaigns.contentHelp": "Rédigez le contenu ici.", "campaigns.continue": "<PERSON><PERSON><PERSON>", "campaigns.copyOf": "<PERSON><PERSON> de {name}", "campaigns.customHeadersHelp": "Array d'en-têtes personnalisés à joindre aux messages sortants. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Date et heure", "campaigns.ended": "Terminée", "campaigns.errorSendTest": "Erreur lors de l'envoi du test : {error}", "campaigns.fieldInvalidBody": "Erreur lors de la compilation du corps de la campagne : {error}", "campaigns.fieldInvalidFromEmail": "Adresse d'envoi invalide.", "campaigns.fieldInvalidListIDs": "ID de liste invalides.", "campaigns.fieldInvalidMessenger": "Service de messagerie inconnu : {name}.", "campaigns.fieldInvalidName": "<PERSON><PERSON>ur du nom invalide.", "campaigns.fieldInvalidSendAt": "La date planifiée doit être future.", "campaigns.fieldInvalidSubject": "Longueur d'objet non valide.", "campaigns.formatHTML": "Formater le code HTML", "campaigns.fromAddress": "<PERSON><PERSON><PERSON> d'envoi", "campaigns.fromAddressPlaceholder": "Nom à afficher <<EMAIL>>", "campaigns.invalid": "Campagne non valide", "campaigns.invalidCustomHeaders": "En-têtes personnalisés non valides: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Une date est nécessaire pour planifier la campagne.", "campaigns.newCampaign": "Nouvelle campagne", "campaigns.noKnownSubsToTest": "Aucun·e abonné·e connu à tester.", "campaigns.noOptinLists": "Aucune liste opt-in trouvée pour créer une campagne.", "campaigns.noSubs": "Il n'y a aucun·e abonné·e dans les listes sélectionnées pour créer la campagne.", "campaigns.noSubsToTest": "Il n'y a aucun·e abonné·e à cibler.", "campaigns.notFound": "Campagne introuvable.", "campaigns.onlyActiveCancel": "<PERSON><PERSON> les campagnes actives peuvent être annulées.", "campaigns.onlyActivePause": "<PERSON><PERSON> les campagnes actives peuvent être mises en pause.", "campaigns.onlyDraftAsScheduled": "Seuls les campagnes à l'état de brouillon peuvent être planifiées.", "campaigns.onlyPausedDraft": "Seuls les brouillons et les campagnes mises en pause peuvent être lancés.", "campaigns.onlyScheduledAsDraft": "Seules les campagnes planifiées peuvent être enregistrées en tant que brouillons.", "campaigns.pause": "Pause", "campaigns.plainText": "Texte brut", "campaigns.preview": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.progress": "Avancement", "campaigns.queryPlaceholder": "Nom ou objet", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML brut", "campaigns.removeAltText": "Supprimer le message alternatif en texte brut", "campaigns.richText": "Texte riche", "campaigns.schedule": "Planifier la campagne", "campaigns.scheduled": "Planifiée", "campaigns.send": "Envoyer", "campaigns.sendLater": "Envoyer plus tard", "campaigns.sendTest": "Envoyer un message de test", "campaigns.sendTestHelp": "Pour ajouter plusieurs destinataires, appuyez sur Entrée après avoir tapé une adresse. Les adresses doivent faire partie des abonné·es existant·es.", "campaigns.sendToLists": "Envoyer aux listes", "campaigns.sent": "Envoyés", "campaigns.start": "Lancer la campagne", "campaigns.started": "La campagne « {name} » est lancée", "campaigns.startedAt": "D<PERSON>but", "campaigns.stats": "Statistiques", "campaigns.status.cancelled": "annulée", "campaigns.status.draft": "en brouillon", "campaigns.status.finished": "terminée", "campaigns.status.paused": "en pause", "campaigns.status.running": "active", "campaigns.status.scheduled": "planifiée", "campaigns.statusChanged": "La campagne « {name} » est {status}", "campaigns.subject": "Objet", "campaigns.testDisabled": "Entrer le mot de passe pour test", "campaigns.testEmails": "E-mails de test", "campaigns.testSent": "Message de test envoyé", "campaigns.timestamps": "Horodatages", "campaigns.trackLink": "Lien de suivi", "campaigns.views": "<PERSON><PERSON>", "dashboard.campaignViews": "vues de campagne", "dashboard.linkClicks": "clics sur liens", "dashboard.messagesSent": "messages envoyés", "dashboard.orphanSubs": "abonnements sans retour", "email.data.info": "Vous trouverez un fichier au format JSON contenant l'ensemble des données enregistrées à votre sujet en pièce jointe. Il peut être visualisé dans un éditeur de texte.", "email.data.title": "Vos donn<PERSON>", "email.optin.confirmSub": "Confirmer votre abonnement", "email.optin.confirmSubHelp": "Confirmez votre abonnement en cliquant sur le bouton ci-dessous :", "email.optin.confirmSubInfo": "Vous avez été ajouté·e aux listes suivantes :", "email.optin.confirmSubTitle": "Confirmer votre abonnement", "email.optin.confirmSubWelcome": "Bonjou<PERSON>,", "email.optin.privateList": "Liste privée", "email.status.campaignReason": "Description", "email.status.campaignSent": "Envoyée", "email.status.campaignUpdateTitle": "Mise à jour de campagne", "email.status.importFile": "<PERSON><PERSON><PERSON>", "email.status.importRecords": "Contacts importés", "email.status.importTitle": "Importer la mise à jour", "email.status.status": "Statut", "email.unsub": "<PERSON> d<PERSON>ab<PERSON>ner", "email.unsubHelp": "Vous ne souhaitez pas recevoir ces e-mails ?", "email.viewInBrowser": "Voir dans le navigateur", "forms.formHTML": "Formulaire HTML", "forms.formHTMLHelp": "Utilisez le code HTML suivant pour afficher un formulaire d'abonnement sur une page Web externe. Le formulaire doit avoir le champ email et un ou plusieurs champs `l` (listes UUID). Le champ \"nom\" est facultatif.", "forms.noPublicLists": "Il n'y a pas de listes publiques pour générer un formulaire.", "forms.publicLists": "Listes publiques", "forms.publicSubPage": "Page d'abonnement publique", "forms.selectHelp": "Sélectionnez les listes à ajouter au formulaire.", "forms.title": "Formulaires", "globals.buttons.add": "Ajouter", "globals.buttons.addNew": "Ajouter", "globals.buttons.back": "Retour", "globals.buttons.cancel": "Annuler", "globals.buttons.clone": "<PERSON><PERSON><PERSON>", "globals.buttons.close": "<PERSON><PERSON><PERSON>", "globals.buttons.continue": "<PERSON><PERSON><PERSON>", "globals.buttons.delete": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.deleteAll": "Tout effacer", "globals.buttons.edit": "É<PERSON>er", "globals.buttons.enabled": "Activé", "globals.buttons.insert": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.learnMore": "En savoir plus", "globals.buttons.more": "Plus", "globals.buttons.new": "Ajouter", "globals.buttons.ok": "Valider", "globals.buttons.remove": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.save": "Enregistrer", "globals.buttons.saveChanges": "Enregistrer les changements", "globals.days.0": "dim.", "globals.days.1": "lun.", "globals.days.2": "mar.", "globals.days.3": "mer.", "globals.days.4": "jeu.", "globals.days.5": "ven.", "globals.days.6": "sam.", "globals.days.7": "<PERSON><PERSON>", "globals.fields.createdAt": "Créé·e le", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Nom", "globals.fields.status": "Statut", "globals.fields.type": "Type", "globals.fields.updatedAt": "Mis à jour le", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Confirmer ?", "globals.messages.confirmDiscard": "Annuler les modifications ?", "globals.messages.created": "Création de « {name} »", "globals.messages.deleted": "Suppression de « {name} »", "globals.messages.deletedCount": "{name} ({num}) effacé(s)", "globals.messages.done": "Fait", "globals.messages.emptyState": "Rien", "globals.messages.errorCreating": "E<PERSON>ur lors de la création de {name} : {error}", "globals.messages.errorDeleting": "<PERSON><PERSON>ur lors de la <PERSON> de {name} : {error}", "globals.messages.errorFetching": "Erreur lors de la récupération de {name} : {error}", "globals.messages.errorInvalidIDs": "Un ou plusieurs identifiants non valides fournis : {error}", "globals.messages.errorUUID": "Erreur lors de la génération de l'UUID : {error}", "globals.messages.errorUpdating": "Erreur lors de la mise à jour de {name} : {error}", "globals.messages.internalError": "<PERSON>rreur interne du serveur", "globals.messages.invalidData": "Données invalides", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID invalide", "globals.messages.invalidUUID": "UUID invalide", "globals.messages.missingFields": "Champ(s) manquant(s) : {name}", "globals.messages.notFound": "{name} introuvable", "globals.messages.passwordChange": "Entrez un nouveau mot de passe pour en changer", "globals.messages.updated": "Mise à jour de \"{name}\"", "globals.months.1": "jan.", "globals.months.10": "oct.", "globals.months.11": "nov.", "globals.months.12": "déc.", "globals.months.2": "fév.", "globals.months.3": "mars", "globals.months.4": "avr.", "globals.months.5": "mai", "globals.months.6": "juin", "globals.months.7": "juil.", "globals.months.8": "août", "globals.months.9": "sept.", "globals.states.off": "Désactivé", "globals.terms.all": "<PERSON>ut", "globals.terms.analytics": "Analyses", "globals.terms.bounce": "Rebond | Rebonds", "globals.terms.bounces": "Rebonds", "globals.terms.campaign": "Campagne | Campagnes", "globals.terms.campaigns": "Campagnes", "globals.terms.dashboard": "Tableau de bord", "globals.terms.day": "Jour | Jours", "globals.terms.hour": "<PERSON><PERSON> | Heures", "globals.terms.list": "Liste | Listes", "globals.terms.lists": "Listes", "globals.terms.media": "Médias | Médias", "globals.terms.messenger": "Service de messagerie | Services de messagerie", "globals.terms.messengers": "Services de messagerie", "globals.terms.minute": "Minute | Minutes", "globals.terms.month": "Mois | Mois", "globals.terms.second": "Seconde | Secondes", "globals.terms.settings": "Paramètres", "globals.terms.subscriber": "Abonné·e | Abonné·es", "globals.terms.subscribers": "Abonné·es", "globals.terms.subscriptions": "Abonnement | Abonnements", "globals.terms.tag": "Étiquette | Étiquettes", "globals.terms.tags": "Étiquettes", "globals.terms.template": "Modèle | Modèles", "globals.terms.templates": "<PERSON><PERSON><PERSON><PERSON>", "globals.terms.tx": "Transactionnel | Transactionnels", "globals.terms.year": "Année | Années", "import.alreadyRunning": "Une importation est déjà en cours. Attendez qu'elle se termine ou arrêtez-la avant de réessayer.", "import.blocklist": "Bloquer les adresses importées", "import.csvDelim": "Délimiteur CSV", "import.csvDelimHelp": "Le délimiteur par défaut est la virgule.", "import.csvExample": "Exemple de CSV brut", "import.csvFile": "Fichier CSV ou ZIP", "import.csvFileHelp": "Cliquez ou glissez-déposez ici un fichier CSV ou ZIP", "import.errorCopyingFile": "Erreur lors de la copie du fichier : {error}", "import.errorProcessingZIP": "Erreur lors du traitement du fichier ZIP : {error}", "import.errorStarting": "Erreur lors du démarrage de l'importation : {error}", "import.importDone": "Importation terminée", "import.importStarted": "L'importation a commencé", "import.instructions": "Instructions", "import.instructionsHelp": "Téléchargez un fichier CSV (ou un fichier ZIP contenant un seul fichier CSV) pour importer des contacts en masse. Le fichier CSV doit avoir les en-têtes suivantes avec ces noms de colonnes exacts. Les attributs (facultatifs) doivent être des chaînes JSON valides entre guillemets doubles.", "import.invalidDelim": "Le délimiteur doit être un seul caractère.", "import.invalidFile": "Fichier non valide : {error}", "import.invalidMode": "<PERSON> invalide", "import.invalidParams": "Paramètres non valides : {error}", "import.invalidSubStatus": "Status d'abonnement invalide", "import.listSubHelp": "Abonner aux listes", "import.mode": "Mode", "import.overwrite": "Écraser ?", "import.overwriteHelp": "Remplacer le nom et les attributs des abonné·es existant·es ?", "import.recordsCount": "{num} / {total} contacts importés", "import.stopImport": "Arrêter l'importation", "import.subscribe": "<PERSON>'abonner", "import.title": "Importer des abonné·es", "import.upload": "Envoyer", "lists.confirmDelete": "Êtes-vous sûr·e de supprimer cette liste ? Cela ne supprimera pas les abonné·es.", "lists.confirmSub": "Confirmer les abonnements à {name}", "lists.invalidName": "Nom incorrect", "lists.newList": "Nouvelle liste", "lists.optin": "Abonnement \"opt-in\" (ajout par défaut)", "lists.optinHelp": "L'option \"opt-in double\" envoie un e-mail à l'abonné·e demandant sa confirmation. Pour les listes en \"opt-in double\", les campagnes ne sont envoyées qu'aux abonné·es s'étant confirmé·es.", "lists.optinTo": "Activer l'option opt-in pour {name}", "lists.optins.double": "Opt-in double", "lists.optins.single": "Opt-in simple", "lists.sendCampaign": "Envoyer la campagne", "lists.sendOptinCampaign": "Envoyer une campagne opt-in", "lists.type": "Type", "lists.typeHelp": "Les listes publiques sont libres d'accès en abonnement et leurs noms sont visibles sur les pages publiques telles que la page de gestion des abonnements.", "lists.types.private": "Privée", "lists.types.public": "Publique", "logs.title": "Journalisations", "maintenance.help": "Certaines actions peuvent prendre un certain temps, en fonction de la quantité de données.", "maintenance.maintenance.unconfirmedOptins": "Abonnements sélectionnés non-confirmés", "maintenance.olderThan": "Plus vieux que", "maintenance.title": "Maintenance", "maintenance.unconfirmedSubs": "Abonnements non confirmés datant de plus de {name} jours.", "media.errorReadingFile": "<PERSON><PERSON><PERSON> de lecture du fichier : {error}", "media.errorResizing": "Erreur lors du redimensionnement de l'image : {error}", "media.errorSavingThumbnail": "Erreur lors de l'enregistrement de la miniature : {error}", "media.errorUploading": "Erreur lors de l'envoi du fichier : {error}", "media.invalidFile": "Fichier non valide : {error}", "media.title": "Fichiers", "media.unsupportedFileType": "Type de fichier non pris en charge ({type})", "media.upload": "Importer", "media.uploadHelp": "C<PERSON>z ou glissez-déposez ici une ou plusieurs image(s)", "media.uploadImage": "Importer une image", "menu.allCampaigns": "Toutes les campagnes", "menu.allLists": "Toutes les listes", "menu.allSubscribers": "Tou·tes les abonné·es", "menu.dashboard": "Tableau de bord", "menu.forms": "Formulaires", "menu.import": "Importer", "menu.logs": "Journalisations", "menu.maintenance": "Maintenance", "menu.media": "Fichiers", "menu.newCampaign": "Nouvelle campagne", "menu.settings": "Paramètres", "public.archiveEmpty": "Aucun message archivé pour le moment.", "public.archiveTitle": "Archives des listes de diffusion", "public.blocklisted": "Désabonnement définitif.", "public.campaignNotFound": "La liste de diffusion est introuvable.", "public.confirmOptinSubTitle": "Confirmer votre abonnement", "public.confirmSub": "Confirmer votre abonnement", "public.confirmSubInfo": "Vous avez été ajouté·e aux listes suivantes :", "public.confirmSubTitle": "Confirmer votre abonnement", "public.dataRemoved": "Vos abonnements et toutes les données associées ont été supprimés.", "public.dataRemovedTitle": "Données personnelles supprimées", "public.dataSent": "Vos données personnelles vous ont été envoyées par e-mail.", "public.dataSentTitle": "Données <PERSON> envoyées", "public.errorFetchingCampaign": "Erreur lors de la récupération de l'e-mail.", "public.errorFetchingEmail": "E-mail introuvable", "public.errorFetchingLists": "Erreur lors de la récupération des listes. Veuillez réessayer.", "public.errorProcessingRequest": "Erreur lors du traitement de la demande. Veuillez réessayer.", "public.errorTitle": "<PERSON><PERSON><PERSON>", "public.invalidFeature": "Cette fonctionnalité n'est pas disponible.", "public.invalidLink": "<PERSON>n invalide", "public.managePrefs": "<PERSON><PERSON><PERSON> les préférences", "public.managePrefsUnsub": "Décochez les listes pour vous désabonner de celles-ci.", "public.noListsAvailable": "Aucune liste n'est disponible pour vous abonner.", "public.noListsSelected": "Aucune liste valide sélectionnée pour s'abonner.", "public.noSubInfo": "Il n'y a pas d'abonnement à confirmer.", "public.noSubTitle": "Aucun abonnement", "public.notFoundTitle": "Non trouvé", "public.prefsSaved": "Vos préférences ont été enregistrées.", "public.privacyConfirmWipe": "Voulez-vous vraiment supprimer définitivement toutes vos données d'abonnement ?", "public.privacyExport": "Exportez vos données <PERSON>les", "public.privacyExportHelp": "Une copie de vos données vous sera envoyée par e-mail.", "public.privacyTitle": "Confidentialité et données personnelles", "public.privacyWipe": "Effacez toutes vos données personnelles", "public.privacyWipeHelp": "Supprimez définitivement tous vos abonnements et données associées de notre base de données.", "public.sub": "<PERSON>'abonner", "public.subConfirmed": "Vous voici abonné·e avec succès.", "public.subConfirmedTitle": "Abonnement confirmé", "public.subName": "Nom (facultatif)", "public.subNotFound": "Abonnement introuvable.", "public.subOptinPending": "Un e-mail de confirmation d'inscription(s) vous a été envoyé.", "public.subPrivateList": "Liste privée", "public.subTitle": "<PERSON>'abonner", "public.unsub": "<PERSON> d<PERSON>ab<PERSON>ner", "public.unsubFull": "Se désabonner également de tous futurs e-mails.", "public.unsubHelp": "Voulez-vous vous désabonner de cette liste de diffusion ?", "public.unsubTitle": "<PERSON> d<PERSON>ab<PERSON>ner", "public.unsubbedInfo": "Vous vous êtes désabonné·e avec succès.", "public.unsubbedTitle": "Désabonné·e", "public.unsubscribeTitle": "Se désabonner de la liste de diffusion", "settings.appearance.adminHelp": "CSS personnalisé à appliquer à l'interface utilisateur d'administration.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "CSS personnalisé", "settings.appearance.customJS": "JavaScript personnalisé", "settings.appearance.name": "Apparence", "settings.appearance.publicHelp": "CSS et JavaScript personnalisés à appliquer aux pages publiques.", "settings.appearance.publicName": "Public", "settings.bounces.action": "Action", "settings.bounces.blocklist": "Liste de bloquage", "settings.bounces.count": "Comptage des rebonds", "settings.bounces.countHelp": "Nombre de rebonds par abonné", "settings.bounces.delete": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.enable": "Activer le traitement des rebonds", "settings.bounces.enableMailbox": "<PERSON><PERSON> la boîte aux lettres de rebond", "settings.bounces.enableSES": "Activer SES", "settings.bounces.enableSendgrid": "<PERSON><PERSON>", "settings.bounces.enableWebhooks": "Activez les 'webhooks' de rebond", "settings.bounces.enabled": "Activer", "settings.bounces.folder": "Dossier", "settings.bounces.folderHelp": "Nom du dossier IMAP à scanner. Exple : InBox.", "settings.bounces.invalidScanInterval": "L'intervalle de 'scan' des rebonds doit être d'au moins 1 minute.", "settings.bounces.name": "Rebonds", "settings.bounces.scanInterval": "Interval de 'scan'", "settings.bounces.scanIntervalHelp": "<PERSON>val<PERSON> auquel la boîte aux lettres de rebond doit être analysée pour les rebonds (s pour seconde, m pour minute).", "settings.bounces.sendgridKey": "Clés de SendGrid", "settings.bounces.type": "Type", "settings.bounces.username": "Identifiant", "settings.confirmRestart": "Assurez-vous que les campagnes actives soient en pause. <PERSON><PERSON><PERSON><PERSON> ?", "settings.duplicateMessengerName": "Doublon du nom de messagerie : {name}", "settings.errorEncoding": "Erreur lors de l'encodage des paramètres : {error}", "settings.errorNoSMTP": "Au moins un bloc SMTP doit être activé", "settings.general.adminNotifEmails": "E-mails pour les notifications admin", "settings.general.adminNotifEmailsHelp": "Liste d'adresses e-mail (séparées par des virgules) auxquelles les notifications d'admin telles que les mises à jour d'importation, fins de campagnes, échecs, etc. seront envoyées.", "settings.general.checkUpdates": "Vérifier les mises à jour", "settings.general.checkUpdatesHelp": "Vérifier régulièrement si de nouvelles applications sont disponibles et notifier-les.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publier les campagnes pour lesquelles l'archivage est activé sur le site web public.", "settings.general.enablePublicSubPage": "Activer la page d'abonnement publique", "settings.general.enablePublicSubPageHelp": "Afficher une page d'abonnement publique avec toutes les listes publiques auxquelles les personnes peuvent s'abonner.", "settings.general.faviconURL": "URL du favicon", "settings.general.faviconURLHelp": "(Facultatif) URL complète du favicon statique visible par l'utilisateur, comme sur la page de désabonnement.", "settings.general.fromEmail": "Adresse e-mail `De :` par défaut", "settings.general.fromEmailHelp": "Adresse e-mail `De :` à afficher par défaut dans les e-mails de campagne sortants. Ce paramètre est modifiable pour chaque campagne.", "settings.general.language": "<PERSON><PERSON>", "settings.general.logoURL": "URL du logo", "settings.general.logoURLHelp": "(Facultatif) URL complète du logo statique visible par l'utilisateur, comme sur la page de désabonnement.", "settings.general.name": "Général", "settings.general.rootURL": "URL racine", "settings.general.rootURLHelp": "URL publique de l'installation (sans slash final)", "settings.general.sendOptinConfirm": "Envoyez une confirmation d'adhésion", "settings.general.sendOptinConfirmHelp": "Envoyer un e-mail de confirmation d'adhésion quand de nouvelles personnes s'abonnent ou sont ajoutées par l'administrateur.", "settings.general.siteName": "Nom du site", "settings.invalidMessengerName": "Nom de messagerie invalide", "settings.mailserver.authProtocol": "Protocole d'authentification", "settings.mailserver.host": "<PERSON><PERSON><PERSON>", "settings.mailserver.hostHelp": "<PERSON><PERSON><PERSON> hô<PERSON> du serveur SMTP", "settings.mailserver.idleTimeout": "<PERSON><PERSON><PERSON>'inactiv<PERSON>", "settings.mailserver.idleTimeoutHelp": "Temps d'attente d'une nouvelle activité sur la connexion avant sa fermeture et suppression du pool (s pour seconde, m pour minute)", "settings.mailserver.maxConns": "Nb. de connexions max.", "settings.mailserver.maxConnsHelp": "Nombre maximum de connexions simultanées au serveur SMTP", "settings.mailserver.password": "Mot de passe", "settings.mailserver.passwordHelp": "Entrez un nouveau mot de passe si vous souhaitez le modifier", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "Port du serveur SMTP", "settings.mailserver.skipTLS": "Ignorer la vérification TLS", "settings.mailserver.skipTLSHelp": "Ignorer la vérification du nom d'hôte sur le certificat TLS", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Activer STARTTLS", "settings.mailserver.username": "Nom d'utilisateur", "settings.mailserver.waitTimeout": "<PERSON><PERSON><PERSON>'attente", "settings.mailserver.waitTimeoutHelp": "Temps d'attente d'une nouvelle activité sur une connexion avant sa fermeture et sa suppression du pool (s pour seconde, m pour minute)", "settings.media.provider": "Fournisseur", "settings.media.s3.bucket": "Compartiment", "settings.media.s3.bucketPath": "<PERSON>emin du compartiment", "settings.media.s3.bucketPathHelp": "Emplacement dans le compartiment pour la mise en ligne des fichiers. La valeur par défaut est /", "settings.media.s3.bucketType": "Type du compartiment", "settings.media.s3.bucketTypePrivate": "Priv<PERSON>", "settings.media.s3.bucketTypePublic": "Public", "settings.media.s3.key": "Clé d'accès AWS", "settings.media.s3.publicURL": "URL publique personnalisée (facultatif)", "settings.media.s3.publicURLHelp": "Domaine S3 personnalisé à utiliser pour les ressources de type image au lieu de l'URL S3 par défaut.", "settings.media.s3.region": "Région", "settings.media.s3.secret": "Mot de passe d'accès AWS", "settings.media.s3.uploadExpiry": "Durée de validité", "settings.media.s3.uploadExpiryHelp": "(Facultatif) Spécifiez la durée de validité (en secondes) pour l'URL prédéfinie générée. Uniquement applicable pour les compartiments privés (s, m, h, d pour les secondes, minutes, heures, jours).", "settings.media.s3.url": "URL du 'backend' S3", "settings.media.s3.urlHelp": "Ne changez que si vous utilisez un 'backend' personnalisé compatible S3 comme Minio.", "settings.media.title": "Mise en ligne de fi<PERSON>ers", "settings.media.upload.path": "Emplacement d'envoi des fichiers", "settings.media.upload.pathHelp": "Chemin vers le répertoire où les médias seront mis en ligne", "settings.media.upload.uri": "URI d'envoi des fichiers", "settings.media.upload.uriHelp": "URI d'envoi des fichiers (qui sera visible du monde extérieur). Les médias stockés à cet emplacement seront accessible publiquement sous {root_url}, par exemple à l'adresse : https://listmonk.votresite.com/uploads", "settings.messengers.maxConns": "Nombre de connexions max.", "settings.messengers.maxConnsHelp": "Nombre maximum de connexions simultanées au serveur", "settings.messengers.messageSaved": "Paramètres sauvegardés. Redémarrage de l'application...", "settings.messengers.name": "Nom du service d'envoi de messages", "settings.messengers.nameHelp": "Par exemple : my-sms. Utilisez uniquement des caractères alphanumériques et des tirets.", "settings.messengers.password": "Mot de passe", "settings.messengers.retries": "Tentatives de renvoi", "settings.messengers.retriesHelp": "Nombre de tentatives de renvoi en cas d'échec", "settings.messengers.skipTLSHelp": "Ignorer la vérification du nom d'hôte sur le certificat TLS", "settings.messengers.timeout": "<PERSON><PERSON><PERSON>'inactiv<PERSON>", "settings.messengers.timeoutHelp": "Temps d'attente d'une nouvelle activité sur la connexion avant sa fermeture et suppression du pool (s pour seconde, m pour minute).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "URL racine du serveur Postback", "settings.messengers.username": "Nom d'utilisateur", "settings.needsRestart": "Certains paramètres ont été modifiés. Mettez toutes les campagnes actives en pause et redémarrez l'application.", "settings.performance.batchSize": "<PERSON><PERSON>", "settings.performance.batchSizeHelp": "Le nombre d'abonné·es à extraire de la base de données en une seule itération. Chaque itération extrait les abonné·es de la base de données, leur envoie les messages, puis passe à l'itération suivante pour extraire le lot suivant. Idéalement cette valeur devrait être supérieure au débit maximum possible (Nb de threads * débit).", "settings.performance.concurrency": "Nombre de threads", "settings.performance.concurrencyHelp": "Nombre de workers (threads) concurrents maximum qui enverrons les messages simultanément.", "settings.performance.maxErrThreshold": "Seuil maximum d'erreurs", "settings.performance.maxErrThresholdHelp": "Le nombre d'erreurs (par exemple : délais d'expiration SMTP lors de l'envoi d'e-mails) qu'une campagne en cours d'exécution doit tolérer avant d'être suspendue pour une vérification ou une intervention manuelle. Réglez sur 0 pour ne jamais mettre en pause.", "settings.performance.messageRate": "Débit de messages (par thread)", "settings.performance.messageRateHelp": "Nombre maximum de messages à envoyer par worker / thread en une seconde. Si concurrence = 10 et débit = 10, alors jusqu'à 10x10 = 100 messages peuvent être mis en file d'envoi chaque seconde. Réglez les deux paramètres afin que le débit total soit inférieur aux seuils fixés par les serveurs de messagerie cibles de vos abonné·es pour ne pas finir en spam.", "settings.performance.name": "Débits et performances", "settings.performance.slidingWindow": "Activer une limite d'envois par fenêtre glissante (max. X messages envoyés sur une durée donnée)", "settings.performance.slidingWindowDuration": "<PERSON><PERSON><PERSON> de la fenêtre", "settings.performance.slidingWindowDurationHelp": "<PERSON><PERSON><PERSON> de la fenêtre glissante (m pour minute, h pour heure).", "settings.performance.slidingWindowHelp": "Limitez le nombre total de messages envoyés au cours d'une période donnée. Une fois cette limite atteinte, l'envoi des messages est suspendu jusqu'à ce que la fenêtre de temps soit écoulée.", "settings.performance.slidingWindowRate": "Nb. de messages max", "settings.performance.slidingWindowRateHelp": "Nombre maximum de messages à envoyer sur cette fenêtre", "settings.privacy.allowBlocklist": "Autoriser les abonné·es à bloquer tout envoi", "settings.privacy.allowBlocklistHelp": "Autoriser les abonné·es à se désabonner de toutes les listes de diffusion et à se marquer comme étant bloqué·es ?", "settings.privacy.allowExport": "Autoriser l'export des données par les abonné·es", "settings.privacy.allowExportHelp": "Autoriser les abonné·es à exporter les données collectées à leur sujet ?", "settings.privacy.allowPrefs": "Autoriser les changements de préférences", "settings.privacy.allowPrefsHelp": "Permettre aux abonnés de modifier leurs préférences, comme leur nom et l'abonnement à plusieurs listes.", "settings.privacy.allowWipe": "Autoriser la suppression des données par les abonné·es", "settings.privacy.allowWipeHelp": "Autoriser les abonné·es à supprimer leurs abonnements et toutes les autres données de la base de données. Les vues de campagne et les clics sur les liens sont également supprimés, tandis que le compteur de vues et de nombre de clics globaux restent inchangés (aucun·e abonné·e ne leur est associé) afin que les statistiques et les analyses ne soient pas affectées.", "settings.privacy.domainBlocklist": "<PERSON><PERSON>", "settings.privacy.domainBlocklistHelp": "Les adresses e-mail avec ces domaines ne sont pas autorisées à s'abonner. Entrer un domaine par ligne, exple : somesite.com", "settings.privacy.individualSubTracking": "Suivi individuel des abonné·es (vérifiez si la légalislation l'autorise)", "settings.privacy.individualSubTrackingHelp": "Suivez les vues et les clics par abonné·e pour les campagnes (vérifiez si la légalislation en vigueur l'autorise). Si l'option est désactivée, le suivi des vues et des clics s'effectue de façon anonyme.", "settings.privacy.listUnsubHeader": "Inclure l'en-tête de désabonnement simplifié (via certaines messageries)", "settings.privacy.listUnsubHeaderHelp": "Inclure des en-têtes de désabonnement qui permettent aux utilisateurs de se désabonner en un seul clic depuis leur client de messagerie.", "settings.privacy.name": "Vie privée", "settings.restart": "<PERSON><PERSON><PERSON><PERSON>", "settings.smtp.customHeaders": "En-têtes personnal<PERSON>", "settings.smtp.customHeadersHelp": "Tableau facultatif d'en-têtes à inclure dans tous les e-mails envoyés depuis ce serveur. Par exemple : [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Activé", "settings.smtp.heloHost": "Nom d'hôte HELO", "settings.smtp.heloHostHelp": "Facultatif. Certains serveurs SMTP nécessitent un nom de domaine complet dans le nom d'hôte. <PERSON><PERSON> <PERSON><PERSON>, HELOs utilise `localhost`. Définissez ce paramètre si un nom d'hôte personnalisé doit être utilisé.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Tentatives de renvoi", "settings.smtp.retriesHelp": "Nombre de tentatives de renvoi d'un message en cas d'échec", "settings.smtp.sendTest": "Envoyer un e-mail", "settings.smtp.setCustomHeaders": "Définir des en-têtes personnalisés", "settings.smtp.testConnection": "Tester la connexion", "settings.smtp.testEnterEmail": "Entrer le mot de passe pour tester", "settings.smtp.toEmail": "E-mail du destinataire", "settings.title": "Paramètres", "settings.updateAvailable": "Une nouvelle version ({version}) est disponible.", "subscribers.advancedQuery": "Requête avancée", "subscribers.advancedQueryHelp": "Expression SQL partielle pour interroger les attributs de l'abonné·e", "subscribers.attribs": "Attributs", "subscribers.attribsHelp": "Les attributs sont définis comme une map JSON, par exemple :", "subscribers.blocklistedHelp": "Les abonné·es bloqué·es ne recevront jamais d'e-mails.", "subscribers.confirmBlocklist": "Bloquer {num} abonné·e(s) ?", "subscribers.confirmDelete": "Supprimer {num} abonné·e(s) ?", "subscribers.confirmExport": "Exporter {num} abonné·e(s) ?", "subscribers.domainBlocklisted": "Le nom de domaine de l'e-mail est bloqué.", "subscribers.downloadData": "Télécharger les données", "subscribers.email": "E-mail", "subscribers.emailExists": "Cet e-mail existe déjà.", "subscribers.errorBlocklisting": "Erreur lors du blocage des abonné·es : {error}", "subscribers.errorNoIDs": "Aucun identifiant fourni.", "subscribers.errorNoListsGiven": "Aucune liste attribuée.", "subscribers.errorPreparingQuery": "Erreur lors de la préparation de la requête d'abonné·e : {error}", "subscribers.errorSendingOptin": "Erreur lors de l'envoi de l'e-mail d'opt-in.", "subscribers.export": "Exporter", "subscribers.invalidAction": "Cette action est invalide.", "subscribers.invalidEmail": "Cet e-mail est invalide.", "subscribers.invalidJSON": "JSON non valide dans les attributs.", "subscribers.invalidName": "Le nom entré présente une erreur.", "subscribers.listChangeApplied": "Modification de la liste effectuée.", "subscribers.lists": "Listes", "subscribers.listsHelp": "Les listes dont les abonné·es se sont déjà désabonné·es ne peuvent pas être supprimées.", "subscribers.listsPlaceholder": "Listes auxquelles s'abonner", "subscribers.manageLists": "<PERSON><PERSON><PERSON> les listes", "subscribers.markUnsubscribed": "Marquer comme désabonné·e", "subscribers.newSubscriber": "Nouvel·le abonné·e", "subscribers.numSelected": "{num} abonné·e(s) sélectionné·e(s)", "subscribers.optinSubject": "Confirmer votre abonnement", "subscribers.preconfirm": "Pré-confirmer les abonnements", "subscribers.preconfirmHelp": "Ne pas envoyer l'e-mail de confirmation et marquer tous les listes d'abonnement comme 'abonné'.", "subscribers.query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribers.queryPlaceholder": "E-mail ou nom", "subscribers.reset": "Réinitialiser", "subscribers.selectAll": "Sélectionner tout {num}", "subscribers.sendOptinConfirm": "Envoyer une confirmation d'adhésion", "subscribers.sentOptinConfirm": "Confirmation d'adhésion envoyée", "subscribers.status.blocklisted": "Bloqué·e", "subscribers.status.confirmed": "Confirmé·e", "subscribers.status.enabled": "Activé·e", "subscribers.status.subscribed": "Abonné·e", "subscribers.status.unconfirmed": "Non confirmé·e", "subscribers.status.unsubscribed": "Désabonné·e", "subscribers.subscribersDeleted": "{num} abonné·e(s) supprimé·e(s)", "templates.cantDeleteDefault": "Impossible de supprimer le modèle par défaut", "templates.default": "Défaut", "templates.dummyName": "Campagne de <PERSON>", "templates.dummySubject": "Objet de la campagne de test", "templates.errorCompiling": "Erreur lors de la compilation du modèle : {error}", "templates.errorRendering": "Message d'erreur lors du rendu : {error}", "templates.fieldInvalidName": "<PERSON><PERSON>ur du nom invalide.", "templates.makeDefault": "Définir par défaut", "templates.newTemplate": "Nouveau modèle", "templates.placeholderHelp": "L'espace réservé {placeholder} doit apparaître exactement une fois dans le modèle.", "templates.preview": "<PERSON><PERSON><PERSON><PERSON>", "templates.rawHTML": "HTML brut", "templates.subject": "Objet", "users.login": "Connecter", "users.logout": "Déconnecter"}