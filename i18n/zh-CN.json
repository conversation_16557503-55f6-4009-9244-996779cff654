{"_.code": "zh-CN", "_.name": "简体中文 (zh-CN)", "admin.errorMarshallingConfig": "编组配置错误：{error}", "analytics.count": "计数", "analytics.fromDate": "从", "analytics.invalidDates": "无效的 `from` 或 `to` 日期。", "analytics.isUnique": "每个订阅者的计数都是唯一的。", "analytics.links": "链接", "analytics.nonUnique": "由于个人订户跟踪已关闭，因此计数不唯一。", "analytics.title": "统计信息", "analytics.toDate": "至", "bounces.source": "资源", "bounces.unknownService": "未知的服务。", "bounces.view": "查看退回邮", "campaigns.addAltText": "添加备用纯文本消息", "campaigns.archive": "存档", "campaigns.archiveEnable": "发布到公开存档", "campaigns.archiveHelp": "在公共档案中发布（运行、暂停、完成）活动消息。", "campaigns.archiveMeta": "活动元数据", "campaigns.archiveMetaHelp": "在公共消息中使用的模拟订阅者数据，包括姓名、电子邮件以及活动消息或模板中使用的任何可选属性。", "campaigns.cantUpdate": "无法更新正在运行或已完成的广告系列。", "campaigns.clicks": "点击次数", "campaigns.confirmDelete": "删除{名称}", "campaigns.confirmSchedule": "此活动将在预定的日期和时间自动开始。现在安排？", "campaigns.confirmSwitchFormat": "内容可能会丢失格式。继续？", "campaigns.content": "内容", "campaigns.contentHelp": "内容在这里", "campaigns.continue": "继续", "campaigns.copyOf": "{name}的副本", "campaigns.customHeadersHelp": "要附加到传出消息的自定义标头数组。例如： [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "日期和时间", "campaigns.ended": "结束", "campaigns.errorSendTest": "发送测试时出错：{error}", "campaigns.fieldInvalidBody": "编译广告系列正文时出错：{error}", "campaigns.fieldInvalidFromEmail": "无效的`from_email`。", "campaigns.fieldInvalidListIDs": "列表 ID 无效。", "campaigns.fieldInvalidMessenger": "未知的信使 {name}。", "campaigns.fieldInvalidName": "名称长度无效。", "campaigns.fieldInvalidSendAt": "预定日期应该在将来。", "campaigns.fieldInvalidSubject": "主题的长度无效。", "campaigns.formatHTML": "格式化 HTML", "campaigns.fromAddress": "从地址", "campaigns.fromAddressPlaceholder": "你的名字 <<EMAIL>>", "campaigns.invalid": "无效的广告系列", "campaigns.invalidCustomHeaders": "无效的自定义标头：{error}", "campaigns.markdown": "Markdown格式", "campaigns.needsSendAt": "广告系列需要安排一个日期。", "campaigns.newCampaign": "新广告系列", "campaigns.noKnownSubsToTest": "没有要测试的已知订阅者。", "campaigns.noOptinLists": "未找到创建广告系列的选择加入列表。", "campaigns.noSubs": "所选列表中没有订阅者来创建活动。", "campaigns.noSubsToTest": "没有可定位的订阅者。", "campaigns.notFound": " 找不到广告系列。", "campaigns.onlyActiveCancel": "只有有效的广告可以被取消。", "campaigns.onlyActivePause": "只有有效的广告系列可以暂停。", "campaigns.onlyDraftAsScheduled": "只有广告草稿可以被安排发送。", "campaigns.onlyPausedDraft": "只能启动暂停的广告系列和草稿。", "campaigns.onlyScheduledAsDraft": "只有预定的广告可以保存为草稿。", "campaigns.pause": "暂停", "campaigns.plainText": "纯文本", "campaigns.preview": "预览", "campaigns.progress": "进度", "campaigns.queryPlaceholder": "姓名或主题", "campaigns.rateMinuteShort": "分钟", "campaigns.rawHTML": "原始 HTML", "campaigns.removeAltText": "删除备用纯文本消息", "campaigns.richText": "富文本", "campaigns.schedule": "计划发送广告", "campaigns.scheduled": "预定的", "campaigns.send": "发送", "campaigns.sendLater": "稍后发送", "campaigns.sendTest": "发送测试消息", "campaigns.sendTestHelp": "输入地址后按 Enter 以添加多个收件人。地址必须属于现有订阅者。", "campaigns.sendToLists": "要发送到的列表", "campaigns.sent": "发送", "campaigns.start": "开始发送广告", "campaigns.started": "“{name}”开始", "campaigns.startedAt": "已开始", "campaigns.stats": "统计数据", "campaigns.status.cancelled": "已取消", "campaigns.status.draft": "草稿", "campaigns.status.finished": "完成的", "campaigns.status.paused": "暂停", "campaigns.status.running": "正在运行", "campaigns.status.scheduled": "已安排", "campaigns.statusChanged": " “{name}”是 {status}", "campaigns.subject": "主题", "campaigns.testEmails": "电子邮件", "campaigns.testSent": "已发送测试消息", "campaigns.timestamps": "时间戳", "campaigns.trackLink": "跟踪链接", "campaigns.views": "视图", "dashboard.campaignViews": "广告系列视图", "dashboard.linkClicks": "链接点击次数", "dashboard.messagesSent": "消息已发送", "dashboard.orphanSubs": "孤儿", "email.data.info": "记录在您身上的所有数据的副本作为 JSON 格式的文件附加。它可以在文本编辑器中查看。", "email.data.title": "您的数据", "email.optin.confirmSub": "确认订阅", "email.optin.confirmSubHelp": "单击下面的按钮确认您的订阅", "email.optin.confirmSubInfo": "您已被添加到以下列表中", "email.optin.confirmSubTitle": "确认订阅", "email.optin.confirmSubWelcome": "你好", "email.optin.privateList": "私人列表", "email.status.campaignReason": "原因", "email.status.campaignSent": "已发送", "email.status.campaignUpdateTitle": "广告更新", "email.status.importFile": "文件", "email.status.importRecords": "记录", "email.status.importTitle": "导入更新", "email.status.status": "状态", "email.unsub": "退订", "email.unsubHelp": "不想收到这些电子邮件？", "email.viewInBrowser": "在浏览器中查看", "forms.formHTML": "表单 HTML", "forms.formHTMLHelp": "使用以下 HTML 在外部网页上显示订阅表单。表单应具有电子邮件字段和一个或多个“l”（列出 UUID）字段。名称字段是可选的。", "forms.noPublicLists": "没有用于生成表单的公共列表。", "forms.publicLists": "公开列表", "forms.publicSubPage": "公开订阅页面", "forms.selectHelp": "选择要添加到表单的列表。", "forms.title": "表格", "globals.buttons.add": "添加", "globals.buttons.addNew": "添加新的", "globals.buttons.back": "后退", "globals.buttons.cancel": "取消", "globals.buttons.clone": "克隆", "globals.buttons.close": "关闭", "globals.buttons.continue": "继续", "globals.buttons.delete": "删除", "globals.buttons.deleteAll": "删除所有", "globals.buttons.edit": "编辑", "globals.buttons.enabled": "启用", "globals.buttons.insert": "添加", "globals.buttons.learnMore": "更多", "globals.buttons.more": "更多", "globals.buttons.new": "新建", "globals.buttons.ok": "好的", "globals.buttons.remove": "移除", "globals.buttons.save": "保存", "globals.buttons.saveChanges": "保存更改", "globals.days.0": "星期日", "globals.days.1": "星期日", "globals.days.2": "星期一", "globals.days.3": "星期二", "globals.days.4": "星期三", "globals.days.5": "星期四", "globals.days.6": "星期五", "globals.days.7": "星期六", "globals.fields.createdAt": "已创建", "globals.fields.description": "描述", "globals.fields.id": "身份标识", "globals.fields.name": "姓名", "globals.fields.status": "状态", "globals.fields.type": "类型", "globals.fields.updatedAt": "已更新", "globals.fields.uuid": "全局ID", "globals.messages.confirm": "你确定吗？", "globals.messages.confirmDiscard": "放弃更改？", "globals.messages.created": "“{name}”已创建", "globals.messages.deleted": "“{name}”已删除", "globals.messages.deletedCount": "{name} ({num}) 个已删除", "globals.messages.done": "完成", "globals.messages.emptyState": "这里没有什么", "globals.messages.errorCreating": "创建 {name} 时出错：{error}", "globals.messages.errorDeleting": "删除 {name} 时出错：{error}", "globals.messages.errorFetching": "获取 {name} 时出错：{error}", "globals.messages.errorInvalidIDs": "一个或多个 ID 无效：{error}", "globals.messages.errorUUID": "生成 UUID 时出错：{error}", "globals.messages.errorUpdating": "更新 {name} 时出错：{error}", "globals.messages.internalError": "内部服务器错误", "globals.messages.invalidData": "无效数据", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID 无效", "globals.messages.invalidUUID": "无效的 UUID", "globals.messages.missingFields": "缺少字段：{name}", "globals.messages.notFound": "{name} 未找到", "globals.messages.passwordChange": "输入要更改的值", "globals.messages.updated": "“{name}”已更新", "globals.months.1": "一月", "globals.months.10": "十月", "globals.months.11": "十一月", "globals.months.12": "十二月", "globals.months.2": "二月", "globals.months.3": "三月", "globals.months.4": "四月", "globals.months.5": "五月", "globals.months.6": "六月", "globals.months.7": "七月", "globals.months.8": "八月", "globals.months.9": "九月", "globals.states.off": "关闭", "globals.terms.all": "所有", "globals.terms.analytics": "统计", "globals.terms.bounce": "反弹 | 多个反弹", "globals.terms.bounces": "反弹", "globals.terms.campaign": "广告 | 多个广告", "globals.terms.campaigns": "广告", "globals.terms.dashboard": "仪表盘", "globals.terms.day": "一天 | 多天", "globals.terms.hour": "一小时 | 多小时", "globals.terms.list": "列表 | 多个列表", "globals.terms.lists": "列表", "globals.terms.media": "媒体 | 多个媒体", "globals.terms.messenger": "信使 | 多个信使", "globals.terms.messengers": "信使", "globals.terms.minute": "分钟 | 几分钟", "globals.terms.month": "月 | 几个月", "globals.terms.second": "秒 | 几秒", "globals.terms.settings": "设置", "globals.terms.subscriber": "订阅者 | 多个订阅者", "globals.terms.subscribers": "订阅者", "globals.terms.subscriptions": "订阅 | 订阅", "globals.terms.tag": "标签 | 多个标签", "globals.terms.tags": "标签", "globals.terms.template": "模板 | 多个模板", "globals.terms.templates": "模板", "globals.terms.tx": "交易 | 交易", "globals.terms.year": "年 | 多年", "import.alreadyRunning": "导入已在运行。等待它完成或停止它，然后再试一次。", "import.blocklist": "黑名单", "import.csvDelim": "CSV 分隔符", "import.csvDelimHelp": "默认分隔符是逗号。", "import.csvExample": "原始 CSV示例", "import.csvFile": "CSV 或 ZIP 文件", "import.csvFileHelp": "单击或拖动 CSV 或 ZIP 文件到此处", "import.errorCopyingFile": "复制文件时出错：{error}", "import.errorProcessingZIP": "处理 ZIP 文件时出错：{error}", "import.errorStarting": "开始导入时出错：{error}", "import.importDone": "完毕", "import.importStarted": "导入已开始", "import.instructions": "说明", "import.instructionsHelp": "上传包含单个 CSV 文件的 CSV 文件或 ZIP 文件以批量导入订阅者。CSV 文件应具有以下带有确切列名的标题。attributes（可选）应该是带有双引号的有效 JSON 字符串。", "import.invalidDelim": "分隔符应该是单个字符。", "import.invalidFile": "无效文件：{error}", "import.invalidMode": "无效模式", "import.invalidParams": "无效参数：{error}", "import.invalidSubStatus": "订阅状态无效", "import.listSubHelp": "要订阅的列表", "import.mode": "模式", "import.overwrite": "覆盖 ？", "import.overwriteHelp": "覆盖现有订阅者的名称、属性、订阅状态？", "import.recordsCount": "{num} / {total} 条记录", "import.stopImport": "停止导入", "import.subscribe": "订阅", "import.title": "导入订阅者", "import.upload": "上传", "lists.confirmDelete": "你确定吗？这不会删除订阅者。", "lists.confirmSub": "确认订阅 {name}", "lists.invalidName": "名称无效", "lists.newList": "新列表", "lists.optin": "选择加入", "lists.optinHelp": "双重选择会向订阅者发送一封电子邮件，要求确认。在双重选择加入列表中，活动仅发送给已确认的订阅者。", "lists.optinTo": "选择加入 {name}", "lists.optins.double": "双重选择加入", "lists.optins.single": "单选加入", "lists.sendCampaign": "发送广告", "lists.sendOptinCampaign": "发送选择加入广告", "lists.type": "类型", "lists.typeHelp": "公共列表向全世界开放订阅，其名称可能会出现在订阅管理页面等公共页面上。", "lists.types.private": "私人的", "lists.types.public": "公开", "logs.title": "日志", "maintenance.help": "根据数据量，某些操作可能需要一段时间才能完成。", "maintenance.maintenance.unconfirmedOptins": "未经确认的选择加入订阅", "maintenance.olderThan": "早于", "maintenance.title": "维护", "maintenance.unconfirmedSubs": "超过 {name} 天的未确认订阅。", "media.errorReadingFile": "读取文件时出错：{error}", "media.errorResizing": "调整图像大小时出错：{error}", "media.errorSavingThumbnail": "保存缩略图时出错：{error}", "media.errorUploading": "上传文件时出错：{error}", "media.invalidFile": "无效文件：{error}", "media.title": "媒体", "media.unsupportedFileType": "不支持的文件类型 ({type})", "media.upload": "上传", "media.uploadHelp": "在此处单击或拖动一张或多张图片", "media.uploadImage": "上传图片", "menu.allCampaigns": "所有广告系列", "menu.allLists": "所有列表", "menu.allSubscribers": "所有订阅者", "menu.dashboard": "仪表板", "menu.forms": "表格", "menu.import": "导入", "menu.logs": "日志", "menu.maintenance": "维护", "menu.media": "媒体", "menu.newCampaign": "创建新的", "menu.settings": "设置", "public.archiveEmpty": "还没有已存档信息", "public.archiveTitle": "邮件列表存档", "public.blocklisted": "已永久取消订阅", "public.campaignNotFound": "未找到电子邮件。", "public.confirmOptinSubTitle": "确认订阅", "public.confirmSub": "确认订阅", "public.confirmSubInfo": "您已被添加到以下列表中：", "public.confirmSubTitle": "确认", "public.dataRemoved": "您的订阅和所有相关数据已被删除。", "public.dataRemovedTitle": "已删除数据", "public.dataSent": "您的数据已作为附件通过电子邮件发送给您", "public.dataSentTitle": "通过电子邮件发送的数据", "public.errorFetchingCampaign": "获取电子邮件消息时出错。", "public.errorFetchingEmail": "未找到电子邮件", "public.errorFetchingLists": "获取列表时出错。请重试。", "public.errorProcessingRequest": "处理请求时出错。请重试。", "public.errorTitle": "错误", "public.invalidFeature": "该功能不可用。", "public.invalidLink": "无效的链接", "public.managePrefs": "管理偏好设置", "public.managePrefsUnsub": "取消选中列表以取消订阅。", "public.noListsAvailable": "没有可供订阅的列表。", "public.noListsSelected": "没有可以选择订阅的有效列表", "public.noSubInfo": "没有要确认的订阅。", "public.noSubTitle": "没有订阅", "public.notFoundTitle": "未找到", "public.prefsSaved": "你的偏好设置已经被保存", "public.privacyConfirmWipe": "您确定要永久删除所有订阅数据吗？", "public.privacyExport": "导出您的数据", "public.privacyExportHelp": "您的数据副本将通过电子邮件发送给您。", "public.privacyTitle": "隐私和数据", "public.privacyWipe": "擦除您的数据", "public.privacyWipeHelp": "从数据库中永久删除所有订阅和相关数据。", "public.sub": "订阅", "public.subConfirmed": "订阅成功。", "public.subConfirmedTitle": "已确认", "public.subName": "姓名（可选）", "public.subNotFound": "未找到订阅", "public.subOptinPending": "已向您发送一封电子邮件以确认您的订阅。", "public.subPrivateList": "私人列表", "public.subTitle": "订阅", "public.unsub": "退订", "public.unsubFull": "也取消订阅所有未来的电子邮件。", "public.unsubHelp": "您想退订此邮件列表吗？", "public.unsubTitle": "退订", "public.unsubbedInfo": "您已成功退订。", "public.unsubbedTitle": "退订", "public.unsubscribeTitle": "退订邮件列表", "settings.appearance.adminHelp": "应用到管理 UI 的自定义 CSS。", "settings.appearance.adminName": "管理员", "settings.appearance.customCSS": "自定义 CSS", "settings.appearance.customJS": "JavaScript", "settings.appearance.name": "外观", "settings.appearance.publicHelp": "自定义 CSS 和 JavaScript 以应用于公共页面。", "settings.appearance.publicName": "公开", "settings.bounces.action": "行动", "settings.bounces.blocklist": "黑名单", "settings.bounces.count": "反弹计数", "settings.bounces.countHelp": "每个订阅者的反弹次数", "settings.bounces.delete": "删除", "settings.bounces.enable": "启用退回处理", "settings.bounces.enableMailbox": "启用退回邮箱", "settings.bounces.enableSES": "启用SES", "settings.bounces.enableSendgrid": "启用SendGrid", "settings.bounces.enableWebhooks": "启用反弹webhooks", "settings.bounces.enabled": "已启用", "settings.bounces.folder": "文件夹", "settings.bounces.folderHelp": "要扫描的 IMAP 文件夹的名称。例如：收件箱。", "settings.bounces.invalidScanInterval": "反弹扫描间隔应至少为 1 分钟。", "settings.bounces.name": "反弹", "settings.bounces.scanInterval": "扫描间隔", "settings.bounces.scanIntervalHelp": "应扫描退回邮箱以查找退回邮件的时间间隔（s 表示秒，m 表示分钟）。", "settings.bounces.sendgridKey": "SendGrid键", "settings.bounces.type": "类型", "settings.bounces.username": "用户名", "settings.confirmRestart": "确保暂停正在运行的广告系列。重新开始？", "settings.duplicateMessengerName": "重复的信使名称：{name}", "settings.errorEncoding": "错误编码设置：{error}", "settings.errorNoSMTP": "至少应启用一个SMTP块", "settings.general.adminNotifEmails": "管理员通知电子邮件", "settings.general.adminNotifEmailsHelp": "应向其发送管理通知（例如导入更新、活动完成、失败等）的电子邮件地址的逗号分隔列表。", "settings.general.checkUpdates": "检查更新", "settings.general.checkUpdatesHelp": "定期检查新的应用程序版本并通知。", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "在公共网站上发布启用存档的活动。", "settings.general.enablePublicSubPage": "启用公共订阅页面", "settings.general.enablePublicSubPageHelp": "显示一个公共订阅页面，其中包含供人们订阅的所有公共列表。", "settings.general.faviconURL": "网站图标网址", "settings.general.faviconURLHelp": "（可选）要在面向用户的视图（例如退订页面）上显示的静态网站图标的完整 URL。", "settings.general.fromEmail": "默认“发件人”电子邮件", "settings.general.fromEmailHelp": "默认“发件人”电子邮件显示在传出的营销活动电子邮件中。这可以在每个广告系列中更改。", "settings.general.language": "语言", "settings.general.logoURL": "Logo网址", "settings.general.logoURLHelp": "（可选）要在面向用户的视图（例如退订页面）上显示的静态徽标的完整 URL。", "settings.general.name": "通用", "settings.general.rootURL": "根网址", "settings.general.rootURLHelp": "安装的公共 URL（没有尾部斜杠）。", "settings.general.sendOptinConfirm": "发送选择加入确认", "settings.general.sendOptinConfirmHelp": "当订阅者通过公共表单注册或由管理员添加时，发送选择加入确认电子邮件。", "settings.general.siteName": "站点名称", "settings.invalidMessengerName": "信使名称无效。", "settings.mailserver.authProtocol": "身份验证协议", "settings.mailserver.host": "主机", "settings.mailserver.hostHelp": "SMTP服务器的主机地址。", "settings.mailserver.idleTimeout": "空闲超时", "settings.mailserver.idleTimeoutHelp": "在关闭连接并将其从池中删除之前等待连接上的新活动的时间（s 表示秒，m 表示分钟）。", "settings.mailserver.maxConns": "最大连接数", "settings.mailserver.maxConnsHelp": "与服务器的最大并发连接数。", "settings.mailserver.password": "密码", "settings.mailserver.passwordHelp": "输入更改", "settings.mailserver.port": "端口", "settings.mailserver.portHelp": "SMTP服务器的端口。", "settings.mailserver.skipTLS": "跳过TLS验证", "settings.mailserver.skipTLSHelp": "跳过对TLS证书的主机名检查。", "settings.mailserver.tls": "TLS协议", "settings.mailserver.tlsHelp": "TLS/SSL加密。STARTTLS是常用的。", "settings.mailserver.username": "用户名", "settings.mailserver.waitTimeout": "等待超时", "settings.mailserver.waitTimeoutHelp": "在关闭连接并将其从池中删除之前等待连接上的新活动的时间（s 表示秒，m 表示分钟）。", "settings.media.provider": "提供者", "settings.media.s3.bucket": "存储桶", "settings.media.s3.bucketPath": "存储桶路径", "settings.media.s3.bucketPathHelp": "存储桶内用于上传文件的路径。默认为 /", "settings.media.s3.bucketType": "存储桶类型", "settings.media.s3.bucketTypePrivate": "私人的", "settings.media.s3.bucketTypePublic": "公开", "settings.media.s3.key": "AWS访问密钥", "settings.media.s3.publicURL": "自定义公共 URL（可选）", "settings.media.s3.publicURLHelp": "用于图像链接的自定义S3域，而不是默认的S3后端URL。", "settings.media.s3.region": "地区", "settings.media.s3.secret": "AWS访问密钥", "settings.media.s3.uploadExpiry": "上传到期", "settings.media.s3.uploadExpiryHelp": "（可选）为生成的预签名UR 指定TTL（以秒为单位）。仅适用于私有存储桶（s、m、h、d 表示秒、分钟、小时、天）。", "settings.media.s3.url": "S3后端URL", "settings.media.s3.urlHelp": "只有在使用像Minio这样的自定义S3兼容后端时才进行更改。", "settings.media.title": "媒体上传", "settings.media.upload.path": "上传路径", "settings.media.upload.pathHelp": "将上传媒体的目录的路径。", "settings.media.upload.uri": "上传URI", "settings.media.upload.uriHelp": "上传对外界可见的 URI。上传到 upload_path 的媒体将在 {root_url} 下公开访问，例如 https://listmonk.yoursite.com/uploads。", "settings.messengers.maxConns": "最大连接数", "settings.messengers.maxConnsHelp": "与服务器的最大并发连接数。", "settings.messengers.messageSaved": "设置已保存。正在重新加载应用程序...", "settings.messengers.name": "信使", "settings.messengers.nameHelp": "例如：我的短信。字母数字/破折号。", "settings.messengers.password": "密码", "settings.messengers.retries": "重试", "settings.messengers.retriesHelp": "消息失败时重试的次数。", "settings.messengers.skipTLSHelp": "跳过对TLS证书的主机名检查。", "settings.messengers.timeout": "空闲超时", "settings.messengers.timeoutHelp": "在关闭连接并将其从池中删除之前等待连接上的新活动的时间（s 表示秒，m 表示分钟）。", "settings.messengers.url": "网址", "settings.messengers.urlHelp": "Postback服务器的根URL。", "settings.messengers.username": "用户名", "settings.needsRestart": "设置已更改。暂停所有正在运行的广告系列并重新启动应用", "settings.performance.batchSize": "批量大小", "settings.performance.batchSizeHelp": "在单次迭代中从数据库中提取的订阅者数量。每次迭代都会从数据库中提取订阅者，向他们发送消息，然后继续进行下一次迭代以提取下一批。理想情况下，这应该高于可实现的最大吞吐量（并发 * message_rate）。", "settings.performance.concurrency": "并发", "settings.performance.concurrencyHelp": "将尝试同时发送消息的最大并发工作线程（线程）。", "settings.performance.maxErrThreshold": "最大误差阈值", "settings.performance.maxErrThresholdHelp": "正在运行的活动在暂停以进行手动调查或干预之前应该容忍的错误数（例如：发送电子邮件时的 SMTP 超时）。设置为 0 以永不暂停。", "settings.performance.messageRate": "发消息速率", "settings.performance.messageRateHelp": "每个工作人员每秒发送的最大消息数。如果 concurrency = 10 且 message_rate = 10，则每秒最多可以推送 10x10=100 条消息。这与并发性一起，应该进行调整，以使每秒发出的净消息保持在目标消息服务器速率限制（如果有）之下。", "settings.performance.name": "性能", "settings.performance.slidingWindow": "启用滑动窗口限制", "settings.performance.slidingWindowDuration": "持续时间", "settings.performance.slidingWindowDurationHelp": "滑动窗口期的持续时间（m 代表分钟，h 代表小时）。", "settings.performance.slidingWindowHelp": "限制在给定时间段内发出的消息总数。达到此限制后，将暂停发送消息，直到时间窗口清除。", "settings.performance.slidingWindowRate": "最大消息数", "settings.performance.slidingWindowRateHelp": "在窗口持续时间内发送的最大消息数。", "settings.privacy.allowBlocklist": "允许列入黑名单", "settings.privacy.allowBlocklistHelp": "允许订阅者从所有邮件列表中退订并将自己标记为已列入黑名单？", "settings.privacy.allowExport": "允许导出", "settings.privacy.allowExportHelp": "允许订阅者导出收集到的数据？", "settings.privacy.allowPrefs": "允许更改偏好", "settings.privacy.allowPrefsHelp": "允许订阅者更改首选项，例如他们的姓名和多个列表订阅。", "settings.privacy.allowWipe": "允许擦除", "settings.privacy.allowWipeHelp": "允许订阅者删除自己，包括他们的订阅和数据库中的所有其他数据。广告系列浏览量和链接点击量也会被删除，而浏览量和点击量仍然存在（没有与之关联的订阅者），因此统计数据和分析不会受到影响。", "settings.privacy.domainBlocklist": "域阻止列表", "settings.privacy.domainBlocklistHelp": "不允许订阅具有这些域的电子邮件地址。每行输入一个域，例如：somesite.com", "settings.privacy.individualSubTracking": "个人订户跟踪", "settings.privacy.individualSubTrackingHelp": "跟踪订阅者级别的广告系列视图和点击次数。禁用后，查看和点击跟踪将继续，而不与单个订阅者相关联。", "settings.privacy.listUnsubHeader": "包括 `List-Unsubscribe` 标头", "settings.privacy.listUnsubHeaderHelp": "包括允许电子邮件客户端允许用户通过单击取消订阅的取消订阅标题", "settings.privacy.name": "隐私", "settings.restart": "重新开始", "settings.smtp.customHeaders": "自定义标头", "settings.smtp.customHeadersHelp": "要包含在从此服务器发送的所有消息中的可选电子邮件标头数组。例如： [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "已启用", "settings.smtp.heloHost": "HELO主机名", "settings.smtp.heloHostHelp": "可选的。某些 SMTP 服务器要求主机名中包含 FQDN。默认情况下，HELLO 使用 `localhost`。如果应该使用自定义主机名，请设置此项。", "settings.smtp.name": "SMTP服务器", "settings.smtp.retries": "重试", "settings.smtp.retriesHelp": "消息失败时重试的次数。", "settings.smtp.sendTest": "发送电子邮件", "settings.smtp.setCustomHeaders": "设置自定义标头", "settings.smtp.testConnection": "测试连接", "settings.smtp.testEnterEmail": "输入密码用于测试", "settings.smtp.toEmail": "发到邮箱", "settings.title": "设置", "settings.updateAvailable": "有新的更新 {version} 可用。", "subscribers.advancedQuery": "高级", "subscribers.advancedQueryHelp": "查询订阅者属性的部分SQL表达式", "subscribers.attribs": "属性", "subscribers.attribsHelp": "属性定义为JSON映射，例如：", "subscribers.blocklistedHelp": "列入黑名单的订阅者永远不会收到任何电子邮件。", "subscribers.confirmBlocklist": "屏蔽 {num} 个订阅者？", "subscribers.confirmDelete": "删除 {num} 个订阅者？", "subscribers.confirmExport": "导出 {num} 个订阅者？", "subscribers.domainBlocklisted": "电子邮件域被列入黑名单。", "subscribers.downloadData": "下载数据", "subscribers.email": "电子邮件", "subscribers.emailExists": "电子邮件已经存在。", "subscribers.errorBlocklisting": "将订阅者列入黑名单时出错：{error}", "subscribers.errorNoIDs": "没有给出ID。", "subscribers.errorNoListsGiven": "没有给出列表。", "subscribers.errorPreparingQuery": "准备订阅者查询时出错：{error}", "subscribers.errorSendingOptin": "发送选择加入电子邮件时出错。", "subscribers.export": "导出", "subscribers.invalidAction": "无效的操作。", "subscribers.invalidEmail": "不合规电邮。", "subscribers.invalidJSON": "属性中的JSON无效。", "subscribers.invalidName": "名称无效。", "subscribers.listChangeApplied": "已应用列表更改。", "subscribers.lists": "列表", "subscribers.listsHelp": "不能删除订阅者自己取消订阅的列表。", "subscribers.listsPlaceholder": "要订阅的列表", "subscribers.manageLists": "管理列表", "subscribers.markUnsubscribed": "标记为退订", "subscribers.newSubscriber": "新订阅者", "subscribers.numSelected": "已选择 {num} 个订阅者", "subscribers.optinSubject": "确认订阅", "subscribers.preconfirm": "预先确认订阅", "subscribers.preconfirmHelp": "不要发送选择加入的电子邮件并将所有列表订阅标记为“已订阅”。", "subscribers.query": "查询", "subscribers.queryPlaceholder": "电子邮件或姓名", "subscribers.reset": "重置", "subscribers.selectAll": "全选 {num}", "subscribers.sendOptinConfirm": "发送选择加入确认", "subscribers.sentOptinConfirm": "已发送选择加入确认", "subscribers.status.blocklisted": "列入黑名单", "subscribers.status.confirmed": "已确认", "subscribers.status.enabled": "启用", "subscribers.status.subscribed": "订阅", "subscribers.status.unconfirmed": "未确认", "subscribers.status.unsubscribed": "退订", "subscribers.subscribersDeleted": "{num} 个订阅者已删除", "templates.cantDeleteDefault": "无法删除默认模板", "templates.default": "默认", "templates.dummyName": "空广告", "templates.dummySubject": "空广告主题", "templates.errorCompiling": "编译模板时出错：{error}", "templates.errorRendering": "错误呈现消息：{error}", "templates.fieldInvalidName": "名称长度无效", "templates.makeDefault": "默认设置", "templates.newTemplate": "新模板", "templates.placeholderHelp": "占位符 {placeholder} 应该在模板中恰好出现一次。", "templates.preview": "预览", "templates.rawHTML": "原始HTML", "templates.subject": "主题", "users.login": "登录", "users.logout": "登出"}