{"_.code": "nl", "_.name": "Nederlands (nl)", "admin.errorMarshallingConfig": "Fout bij lezen configuratie: {error}", "analytics.count": "Aantal", "analytics.fromDate": "<PERSON>", "analytics.invalidDates": "Ongeldige `van` of `tot` datums.", "analytics.isUnique": "De telling zijn uniek per abonnee.", "analytics.links": "Links", "analytics.nonUnique": "De tellingen zijn niet uniek omdat het volgen van individuele abonnees is uitgeschakeld.", "analytics.title": "Analyse", "analytics.toDate": "<PERSON><PERSON>", "bounces.source": "<PERSON><PERSON>", "bounces.unknownService": "Onbekende service.", "bounces.view": "<PERSON><PERSON> bounces", "campaigns.addAltText": "Voeg alternatieve tekst zonder opmaak toe", "campaigns.archive": "Archive", "campaigns.archiveEnable": "Publish to public archive", "campaigns.archiveHelp": "Publish (running, paused, finished) the campaign message on the public archive.", "campaigns.archiveMeta": "Campaign metadata", "campaigns.archiveMetaHelp": "Dummy subscriber data to use in the public message including name, email, and any optional attributes used in the campaign message or template.", "campaigns.cantUpdate": "Kan een lopende of afgelopen campagne niet updaten.", "campaigns.clicks": "Kliks", "campaigns.confirmDelete": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "campaigns.confirmSchedule": "Deze campagne zal automatisch starten op het geplande tijdstip. Nu inplannen?", "campaigns.confirmSwitchFormat": "De inhoud kan opmaak verliezen. Doorgaan?", "campaigns.content": "<PERSON><PERSON><PERSON>", "campaigns.contentHelp": "<PERSON><PERSON><PERSON> hier", "campaigns.continue": "<PERSON><PERSON><PERSON>", "campaigns.copyOf": "<PERSON><PERSON> {name}", "campaigns.customHeadersHelp": "A<PERSON>y van custom headers om bij te voegen aan uitgaande berichten. bv: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Datum en tijd", "campaigns.ended": "Beëindigd", "campaigns.errorSendTest": "Fout bij verzenden test: {error}", "campaigns.fieldInvalidBody": "Fout bij <PERSON>en campagne-inhoud: {error}", "campaigns.fieldInvalidFromEmail": "Ongeldige afzender.", "campaigns.fieldInvalidListIDs": "Ongeldige lijst IDs.", "campaigns.fieldInvalidMessenger": "<PERSON><PERSON><PERSON><PERSON> messenger {name}.", "campaigns.fieldInvalidName": "Ongeldige lengte voor naam.", "campaigns.fieldInvalidSendAt": "Geplande datum moet in de toekomst zijn.", "campaigns.fieldInvalidSubject": "Ongeldige lengte voor onderwerp.", "campaigns.formatHTML": "Formatteer HTML", "campaigns.fromAddress": "Afzender", "campaigns.fromAddressPlaceholder": "<PERSON><PERSON><PERSON> <<EMAIL>>", "campaigns.invalid": "Ongeldige campagne", "campaigns.invalidCustomHeaders": "Ongeldige custom headers: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "<PERSON><PERSON>ne heeft een datum nodig om ingepland te worden.", "campaigns.newCampaign": "Nieuwe campagne", "campaigns.noKnownSubsToTest": "<PERSON><PERSON> a<PERSON> om mee te testen.", "campaigns.noOptinLists": "<PERSON><PERSON> opt-in lijsten gevonden om een campagne te maken.", "campaigns.noSubs": "<PERSON>r zijn geen a<PERSON> in de geselecteerde lijsten om een campagne te maken.", "campaigns.noSubsToTest": "<PERSON>r zijn geen abonnees om mee te testen.", "campaigns.notFound": "<PERSON>agne niet gevonden.", "campaigns.onlyActiveCancel": "Alleen lopende campagnes kunnen stopgezet worden.", "campaigns.onlyActivePause": "Alleen lopende campagnes kunnen gepauzeerd worden.", "campaigns.onlyDraftAsScheduled": "Alleen concept campagnes kunnen ingepland worden.", "campaigns.onlyPausedDraft": "Alleen gepauzeerde en concept campagnes kunnen gestart worden.", "campaigns.onlyScheduledAsDraft": "Aleen geplande campagnes kunnen worden opgeslagen als concept.", "campaigns.pause": "<PERSON><PERSON><PERSON>", "campaigns.plainText": "Tekst zonder opmaak", "campaigns.preview": "Voorbeeld", "campaigns.progress": "Voortgang", "campaigns.queryPlaceholder": "<PERSON><PERSON> of onderwerp", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML code", "campaigns.removeAltText": "Verwijder plain text bericht", "campaigns.richText": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON>", "campaigns.schedule": "Plan campagne", "campaigns.scheduled": "<PERSON><PERSON><PERSON>", "campaigns.send": "Verzenden", "campaigns.sendLater": "Verzend later", "campaigns.sendTest": "Verzend testbericht", "campaigns.sendTestHelp": "Druk op Enter na het typen van een e-mailadres om meerdere ontvangers toe te voegen. De ontvangers moeten abonnee zijn. ", "campaigns.sendToLists": "Lijsten om naar te verzenden", "campaigns.sent": "Verzonden", "campaigns.start": "Start campagne", "campaigns.started": "\"{name}\" is gestart", "campaigns.startedAt": "Gestart", "campaigns.stats": "Statistieken", "campaigns.status.cancelled": "Gestopt", "campaigns.status.draft": "Concept", "campaigns.status.finished": "Afgelopen", "campaigns.status.paused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.status.running": "Lopend", "campaigns.status.scheduled": "<PERSON><PERSON><PERSON>", "campaigns.statusChanged": "\"{name}\" is {status}", "campaigns.subject": "Onderwerp", "campaigns.testDisabled": "Enter password to test", "campaigns.testEmails": "E-mails", "campaigns.testSent": "Testbericht verzonden", "campaigns.timestamps": "Tijdstippen", "campaigns.trackLink": "Traceerbare link", "campaigns.views": "Bekeken", "dashboard.campaignViews": "Campagneviews", "dashboard.linkClicks": "Linkkliks", "dashboard.messagesSent": "Berichten verzonden", "dashboard.orphanSubs": "<PERSON><PERSON>", "email.data.info": "In bijlage vind je een kopie van alle data verzameld over je in JSON formaat. Het kan beken worden met een tekstverwerkingsprogramma.", "email.data.title": "Jouw data", "email.optin.confirmSub": "Bevestig inschrijving", "email.optin.confirmSubHelp": "Bevestig je inschrijving door op onderstaande knop te klikken.", "email.optin.confirmSubInfo": "Je bent aan volgende lijsten toegevoegd:", "email.optin.confirmSubTitle": "Bevestig inschrijving", "email.optin.confirmSubWelcome": "Hall<PERSON>", "email.optin.privateList": "Privélijst", "email.status.campaignReason": "Reden", "email.status.campaignSent": "Verzonden", "email.status.campaignUpdateTitle": "Campagne-update", "email.status.importFile": "Bestand", "email.status.importRecords": "Records", "email.status.importTitle": "Importeerupdate", "email.status.status": "Status", "email.unsub": "Uits<PERSON><PERSON>j<PERSON>", "email.unsubHelp": "Wil je deze e-mails niet meer ontvangen?", "email.viewInBrowser": "Bekijk in browser", "forms.formHTML": "Formulier HTML", "forms.formHTMLHelp": "Gebruik de volgende HTML om een inschrijvingsformulier te tonen op een externe webpagina. Het formulier moet het email veld en een of meer `l` (lijst UUID) velden bevatten. Het naam veld is optioneel.", "forms.noPublicLists": "Er zijn geen publieke lijsten om formulieren te genereren.", "forms.publicLists": "Publieke lijsten", "forms.publicSubPage": "Publieke inschrijvingspagina", "forms.selectHelp": "Selecteer li<PERSON><PERSON> om aan het formulier toe te voegen.", "forms.title": "Formulieren", "globals.buttons.add": "Toevoegen", "globals.buttons.addNew": "<PERSON><PERSON><PERSON>", "globals.buttons.back": "Terug", "globals.buttons.cancel": "Stop", "globals.buttons.clone": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.close": "Sluiten", "globals.buttons.continue": "Doorgaan", "globals.buttons.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.buttons.deleteAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> alles", "globals.buttons.edit": "Bewerken", "globals.buttons.enabled": "Ingeschakeld", "globals.buttons.insert": "Invoegen", "globals.buttons.learnMore": "<PERSON><PERSON> leren", "globals.buttons.more": "<PERSON><PERSON>", "globals.buttons.new": "<PERSON><PERSON><PERSON>", "globals.buttons.ok": "OK", "globals.buttons.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.buttons.save": "Opsla<PERSON>", "globals.buttons.saveChanges": "Veranderingen opslaan", "globals.days.0": "<PERSON><PERSON>", "globals.days.1": "Ma", "globals.days.2": "Di", "globals.days.3": "Wo", "globals.days.4": "Do", "globals.days.5": "Vr", "globals.days.6": "<PERSON>a", "globals.days.7": "Zat", "globals.fields.createdAt": "Aangemaakt", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "<PERSON><PERSON>", "globals.fields.status": "Status", "globals.fields.type": "Type", "globals.fields.updatedAt": "Geüpdatet", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Weet je het zeker?", "globals.messages.confirmDiscard": "Veranderingen weggooien?", "globals.messages.created": "\"{name}\" aang<PERSON><PERSON>t", "globals.messages.deleted": "\"{name}\" verwi<PERSON><PERSON>d", "globals.messages.deletedCount": "{name} ({num}) verwijderd", "globals.messages.done": "Done", "globals.messages.emptyState": "<PERSON>s te zien hier", "globals.messages.errorCreating": "Fout bij aanmaken {name}: {error}", "globals.messages.errorDeleting": "Fout bij verwijderen {name}: {error}", "globals.messages.errorFetching": "Fout bij ophalen {name}: {error}", "globals.messages.errorInvalidIDs": "<PERSON><PERSON> of meer IDs zijn ongeldig: {error}", "globals.messages.errorUUID": "Fout bij generen UUID: {error}", "globals.messages.errorUpdating": "Fout bij updaten {name}: {error}", "globals.messages.internalError": "Interne serverfout", "globals.messages.invalidData": "Ongeldige data", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Ongeldige ID(s)", "globals.messages.invalidUUID": "Ongeldige UUID(s)", "globals.messages.missingFields": "Ontbrekend(e) veld(en): {name}", "globals.messages.notFound": "{name} niet gevonden", "globals.messages.passwordChange": "<PERSON><PERSON> een nieuw wachtwoord in", "globals.messages.updated": "\"{name}\" ge<PERSON><PERSON><PERSON>t", "globals.months.1": "Jan", "globals.months.10": "Okt", "globals.months.11": "Nov", "globals.months.12": "Dec", "globals.months.2": "Feb", "globals.months.3": "Maa", "globals.months.4": "Apr", "globals.months.5": "<PERSON>", "globals.months.6": "Jun", "globals.months.7": "Jul", "globals.months.8": "Aug", "globals.months.9": "Sep", "globals.states.off": "Uit", "globals.terms.all": "All", "globals.terms.analytics": "Analyse", "globals.terms.bounce": "Bounce | Bounces", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Campagne | Campagnes", "globals.terms.campaigns": "Campagnes", "globals.terms.dashboard": "Dashboard", "globals.terms.day": "Dag | Dagen", "globals.terms.hour": "Uur | Uren", "globals.terms.list": "Lijst | Lijsten", "globals.terms.lists": "<PERSON><PERSON><PERSON>", "globals.terms.media": "Media | Media", "globals.terms.messenger": "Messenger | Messengers", "globals.terms.messengers": "Messengers", "globals.terms.minute": "Minuut | Minuten", "globals.terms.month": "Maand | <PERSON>anden", "globals.terms.second": "Seconde | Seconden", "globals.terms.settings": "Instellingen", "globals.terms.subscriber": "Abonnee | Abonnees", "globals.terms.subscribers": "Abonnees", "globals.terms.subscriptions": "Subscription | Subscriptions", "globals.terms.tag": "Label | Labels", "globals.terms.tags": "Labels", "globals.terms.template": "Sjabloon | Sjablonen", "globals.terms.templates": "Sjablonen", "globals.terms.tx": "Transactional | Transactional", "globals.terms.year": "Jaar | Jaren", "import.alreadyRunning": "<PERSON>r is al een importeeractie bezig. Wacht tot deze gedaan is of annuleer voor het opnieuw te proberen.", "import.blocklist": "Geblokkeerd", "import.csvDelim": "CSV scheidingsteken", "import.csvDelimHelp": "Standaard scheidingsteken is komma.", "import.csvExample": "Voorbeeld CSV", "import.csvFile": "CSV- of ZIP-bestand", "import.csvFileHelp": "Klik of sleep een CSV- of ZIP-bestand hierheen", "import.errorCopyingFile": "Fout bij kopi<PERSON>ren bestand: {error}", "import.errorProcessingZIP": "Fout bij behandelen ZIP-bestand: {error}", "import.errorStarting": "Fout bij importeren: {error}", "import.importDone": "<PERSON><PERSON><PERSON>", "import.importStarted": "Importeren gestart", "import.instructions": "Instructies", "import.instructionsHelp": "Upload een CSV-bestand of een ZIP-bestand met een CSV-bestand om abonnees in bulk te importeren. Het CSV-bestand moet de volgende hoofdingen hebben met de exacte kolomnamen. attributes (optioneel) moet een geldige JSON-string zijn met dubbel ontsnapte aanhalingstekens.", "import.invalidDelim": "Scheidingsteken moet een enkel karakter zijn.", "import.invalidFile": "Ongeldig bestand: {error}", "import.invalidMode": "Ongeldige modus", "import.invalidParams": "Ongeldige parameters: {error}", "import.invalidSubStatus": "Ongeldige inschrijvingsstatus", "import.listSubHelp": "<PERSON>jsten om op in te schrijven.", "import.mode": "Modus", "import.overwrite": "Overscrijven?", "import.overwriteHelp": "<PERSON><PERSON>, <PERSON><PERSON>, inschrij<PERSON><PERSON><PERSON><PERSON> van bestaande abonnees overschrijven?", "import.recordsCount": "{num} / {total} records", "import.stopImport": "Stop importeren", "import.subscribe": "Inschrijven", "import.title": "Abonnees importeren", "import.upload": "Uploaden", "lists.confirmDelete": "Ben je zeker? Dit verwijdert niet alle abonnees.", "lists.confirmSub": "Bevestig de inschrijving(en) voor {name}", "lists.invalidName": "Ongeldige naam", "lists.newList": "<PERSON><PERSON><PERSON> lij<PERSON>", "lists.optin": "Opt-in", "lists.optinHelp": "Dubbele opt-in verzend een e-mail naar de abonnee om te bevestigen. In dubbele opt-in lijsten worden campagnes enkel naar bevestigde abonnees verstuurd.", "lists.optinTo": "Opt-in voor {name}", "lists.optins.double": "<PERSON><PERSON><PERSON> opt-in", "lists.optins.single": "<PERSON><PERSON><PERSON> opt-in", "lists.sendCampaign": "Verzend campagne", "lists.sendOptinCampaign": "Verzend opt-in campagne", "lists.type": "Type", "lists.typeHelp": "<PERSON><PERSON><PERSON> kan zich inschrijven voor publieke lijsten en de naam van de lijst kan op publieke pagina's verschijnen.", "lists.types.private": "Priv<PERSON>", "lists.types.public": "Publiek", "logs.title": "Logboeken", "maintenance.help": "Some actions may take a while to complete depending on the amount of data.", "maintenance.maintenance.unconfirmedOptins": "Unconfirmed opt-in subscriptions", "maintenance.olderThan": "Older than", "maintenance.title": "Maintenance", "maintenance.unconfirmedSubs": "Unconfirmed subscriptions older than {name} days.", "media.errorReadingFile": "Fout bij lezen bestand: {error}", "media.errorResizing": "Fout bij wijzigen formaat afbeelding: {error}", "media.errorSavingThumbnail": "Fout bij opslaan thumbnail: {error}", "media.errorUploading": "Fout bij uploaden bestand: {error}", "media.invalidFile": "Ongeldig bestand: {error}", "media.title": "Media", "media.unsupportedFileType": "Bestandstype niet ondersteund ({type})", "media.upload": "Uploaden", "media.uploadHelp": "Klik of sleep een of meer afbeeldingen naar hier", "media.uploadImage": "Afbeelding uploaden", "menu.allCampaigns": "Alle campagnes", "menu.allLists": "Alle lijsten", "menu.allSubscribers": "Alle abonnees", "menu.dashboard": "Dashboard", "menu.forms": "Formulieren", "menu.import": "<PERSON><PERSON><PERSON><PERSON>", "menu.logs": "Logboeken", "menu.maintenance": "Maintenance", "menu.media": "Media", "menu.newCampaign": "Nieuwe a<PERSON>", "menu.settings": "Instellingen", "public.archiveEmpty": "No archived messages yet.", "public.archiveTitle": "Mailing list archive", "public.blocklisted": "Permanently unsubscribed.", "public.campaignNotFound": "Het e-mailbericht werd niet gevonden.", "public.confirmOptinSubTitle": "Bevestig inschrijving", "public.confirmSub": "Bevestig inschrijving", "public.confirmSubInfo": "Je bent aan volgende lijsten toegevoegd:", "public.confirmSubTitle": "Bevestig", "public.dataRemoved": "Je inschrijving en alle gerelateerde data is verwijderd.", "public.dataRemovedTitle": "Data verwijderd", "public.dataSent": "Je data is naar je ge-e-maild als bijlage.", "public.dataSentTitle": "Data e-mailen", "public.errorFetchingCampaign": "Fout bij ophalen e-mailbericht.", "public.errorFetchingEmail": "E-mailbericht niet gevonden.", "public.errorFetchingLists": "Fout bij ophalen lij<PERSON>. Probeer opnieuw.", "public.errorProcessingRequest": "Fout bij behandelen verzoek. Probeer opnieuw.", "public.errorTitle": "Fout", "public.invalidFeature": "Deze functie is niet be<PERSON>ar", "public.invalidLink": "Ongeldige link", "public.managePrefs": "Manage preferences", "public.managePrefsUnsub": "Uncheck lists to unsubscribe from them.", "public.noListsAvailable": "<PERSON><PERSON> lijsten besch<PERSON> om in te schrijven", "public.noListsSelected": "<PERSON>n geldige lijsten geselecteerd om op in te schrijven", "public.noSubInfo": "<PERSON>r zijn geen inschrijvingen om te bevestigen.", "public.noSubTitle": "<PERSON><PERSON> inschrij<PERSON>", "public.notFoundTitle": "<PERSON><PERSON>", "public.prefsSaved": "Your preferences have been saved.", "public.privacyConfirmWipe": "Ben je zeker dat je all je inschrijvingsdata permanent wil verwijderen?", "public.privacyExport": "Exporteer je data", "public.privacyExportHelp": "<PERSON>en kopie van je data zal naar je ge-e-maild worden.", "public.privacyTitle": "Privacy en data", "public.privacyWipe": "Verwijder je data", "public.privacyWipeHelp": "Verwijder al je inschrijvingen en gerelateerde data permanent uit de database.", "public.sub": "Inschrijven", "public.subConfirmed": "Succesvol ingeschreven.", "public.subConfirmedTitle": "Bevestigd", "public.subName": "<PERSON><PERSON> (optioneel)", "public.subNotFound": "Inschrijving niet gevonden.", "public.subOptinPending": "Een e-mail is verzonden om je inschrijving te bevestigen.", "public.subPrivateList": "Privélijst", "public.subTitle": "Inschrijven", "public.unsub": "Uits<PERSON><PERSON>j<PERSON>", "public.unsubFull": "<PERSON><PERSON><PERSON><PERSON><PERSON> je ook uit voor alle toekomstige e-mails.", "public.unsubHelp": "Wil je je uits<PERSON><PERSON><PERSON><PERSON> van deze mailinglijst?", "public.unsubTitle": "Uits<PERSON><PERSON>j<PERSON>", "public.unsubbedInfo": "<PERSON> <PERSON> met succes uitgeschreven.", "public.unsubbedTitle": "Uitgeschreven", "public.unsubscribeTitle": "<PERSON>its<PERSON><PERSON><PERSON><PERSON> van mailinglijst", "settings.appearance.adminHelp": "Custom CSS om toe te passen op de admin UI.", "settings.appearance.adminName": "Administrator", "settings.appearance.customCSS": "Aangepaste CSS", "settings.appearance.customJS": "Aangepaste JavaScript", "settings.appearance.name": "Uiterlijk", "settings.appearance.publicHelp": "Custom CSS and JavaScript om toe te passen op de publieke pagina's", "settings.appearance.publicName": "Publiek", "settings.bounces.action": "<PERSON><PERSON>", "settings.bounces.blocklist": "Geblokkeerd", "settings.bounces.count": "Aantal bounces", "settings.bounces.countHelp": "Aantal bounces per abonnee", "settings.bounces.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.bounces.enable": "Bounce processing inschakelen", "settings.bounces.enableMailbox": "Bounce mailbox inschakelen", "settings.bounces.enableSES": "SES inschakelen", "settings.bounces.enableSendgrid": "SendGrid inschakelen", "settings.bounces.enableWebhooks": "Bounce webhooks inschakelen", "settings.bounces.enabled": "Ingeschakeld", "settings.bounces.folder": "Map", "settings.bounces.folderHelp": "<PERSON><PERSON>MAP map om te scannen. Bv.: Inbox.", "settings.bounces.invalidScanInterval": "Bounce scan interval moet minstens 1 minuut zijn.", "settings.bounces.name": "<PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.scanIntervalHelp": "Interval waarin de bounce mailbox gescanned moet worden voor bounces (s voor seconden, m voor minuten).", "settings.bounces.sendgridKey": "<PERSON><PERSON><PERSON> sleutel", "settings.bounces.type": "Type", "settings.bounces.username": "Gebruikersnaam", "settings.confirmRestart": "<PERSON>org dat lopende campagnes gepauzeerd zijn. Herstarten?", "settings.duplicateMessengerName": "Dubbele messenger naam: {name}", "settings.errorEncoding": "Fout bij opslaan instellingen: {error}", "settings.errorNoSMTP": "Minstens een SMTP blok moet ingeschakeld zijn/", "settings.general.adminNotifEmails": "Admin notificatiemails", "settings.general.adminNotifEmailsHelp": "Kommagescheiden lijst van e-mailadressen waar admin notificaties zoals importeerupdates, campagne volto<PERSON>ing, fouten enz. naar moeten worden verzonden.", "settings.general.checkUpdates": "Controleer op updates", "settings.general.checkUpdatesHelp": "Controleer regelmatig voor nieuwe app releases en verwittig.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publish campaigns on which archiving is enabled on the public website.", "settings.general.enablePublicSubPage": "Publieke inschrijvingspagina inschakelen.", "settings.general.enablePublicSubPageHelp": "Laat een publieke inschrijvingspagina zien met alle publieke lijsten waarmee mensen zich kunnen inschrijven.", "settings.general.faviconURL": "Favicon URL", "settings.general.faviconURLHelp": "(Optional) volledige URL naar het favicon om te laten zien op user-facing pagina's zoa<PERSON> de uitsch<PERSON>jfpagina.", "settings.general.fromEmail": "Standaard afzender e-mail", "settings.general.fromEmailHelp": "Default afzender e-mail voor uitgaande campagnemails. Dit kan aangepast worden per campagne.", "settings.general.language": "Taal", "settings.general.logoURL": "Logo URL", "settings.general.logoURLHelp": "(Optional) volledige URL naar het logo om te laten zien op user-facing pagina's zoals de uitschrijfpagina.", "settings.general.name": "<PERSON><PERSON><PERSON><PERSON>", "settings.general.rootURL": "Root-URL", "settings.general.rootURLHelp": "Publieke URL van de installatie (geen trailing slash).", "settings.general.sendOptinConfirm": "Verzend opt-in bevestiging", "settings.general.sendOptinConfirmHelp": "Verzend een opt-in bevestigingsmail als abonnees inschrijven via het publieke formulier of als ze door een administrator worden toegevoegd.", "settings.general.siteName": "Site name", "settings.invalidMessengerName": "Ongeldige messenger naam.", "settings.mailserver.authProtocol": "Authenticatieprotocol", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "SMTP server hostadres.", "settings.mailserver.idleTimeout": "Maximale wachttijd", "settings.mailserver.idleTimeoutHelp": "Hoe lang op nieuwe activeit gewacht moet worden voor een verbinding wordt gesloten en van de pool wordt verwijderd (s voor seconden, m voor minuten). ", "settings.mailserver.maxConns": "Max. # connecties", "settings.mailserver.maxConnsHelp": "Maximum # concurrente connecties naar de server.", "settings.mailserver.password": "Wachtwoord", "settings.mailserver.passwordHelp": "Enter om te veranderen", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "SMTP server port.", "settings.mailserver.skipTLS": "TLS verificatie overslaan", "settings.mailserver.skipTLSHelp": "Hostname check op het TLS certificaat overslaan.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "STARTTLS inschakelen.", "settings.mailserver.username": "Gebruikersnaam", "settings.mailserver.waitTimeout": "<PERSON><PERSON>ttij<PERSON>", "settings.mailserver.waitTimeoutHelp": "Hoe lang op nieuwe activeit gewacht moet worden voor een verbinding wordt gesloten en van de pool wordt verwijderd (s voor seconden, m voor minuten). ", "settings.media.provider": "Provider", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "Bucket pad", "settings.media.s3.bucketPathHelp": "Pad in de bucket om bestanden te uploaden. Standaard is /", "settings.media.s3.bucketType": "Bucket type", "settings.media.s3.bucketTypePrivate": "Priv<PERSON>", "settings.media.s3.bucketTypePublic": "Publiek", "settings.media.s3.key": "AWS access key", "settings.media.s3.publicURL": "Aangepaste publieke URL (optioneel)", "settings.media.s3.publicURLHelp": "Aangepast S3 domein om te gebruiken voor links naar afbeeldingen in plaats van de standaard S3 backend URL.", "settings.media.s3.region": "Regio", "settings.media.s3.secret": "AWS access secret", "settings.media.s3.uploadExpiry": "Upload vervaldatum", "settings.media.s3.uploadExpiryHelp": "(Optioneel) TTL (in seconden) voor de gegenereerde, getekende URL. <PERSON><PERSON> van toepassing voor privébuckets (s, m, h, d voor seconden, minuten, uren, dagen).", "settings.media.s3.url": "S3 backend URL", "settings.media.s3.urlHelp": "Enkel veranderen als je een custom S3-compatibele backend gebruikt zoals Minio.", "settings.media.title": "Media uploads", "settings.media.upload.path": "Upload pad", "settings.media.upload.pathHelp": "Pad naar de map waar media geüpload zal worden.", "settings.media.upload.uri": "Upload URI", "settings.media.upload.uriHelp": "Upload URI zichtbaar voor de buitenwereld. De media geüpload naar upload_path zal publiek beschikbaar zijn onder {root_url}, bijvoorbeeld, https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "<PERSON><PERSON> connecties", "settings.messengers.maxConnsHelp": "Maximum concurrente connecties naar de server.", "settings.messengers.messageSaved": "Instellingen opgeslagen. App wordt herstart...", "settings.messengers.name": "Messengers", "settings.messengers.nameHelp": "Bv: my-sms. Alphanumerisch / koppelteken.", "settings.messengers.password": "Wachtwoord", "settings.messengers.retries": "Nieuwe pogingen", "settings.messengers.retriesHelp": "Aantal keer om opnieuw te proberen als een bericht mislukt.", "settings.messengers.skipTLSHelp": "Hostname check op het TLS certificaat overslaan.", "settings.messengers.timeout": "Maximale wachttijd", "settings.messengers.timeoutHelp": "Hoe lang op nieuwe activeit gewacht moet worden voor een verbinding wordt gesloten en van de pool wordt verwijderd (s voor seconden, m voor minuten). ", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Root URL van de Postback server.", "settings.messengers.username": "Gebruikersnaam", "settings.needsRestart": "Instellingen veranderd. Pauzeer alle lopende campagnes en herstart de app", "settings.performance.batchSize": "<PERSON><PERSON><PERSON><PERSON>", "settings.performance.batchSizeHelp": "Het aantal abonnees om per iteratie uit de database te lezen. Elke iteratie leest abonnees uit de database, verzend berichten naar hen, en gaat dan verder naar de volgende iteratie met de volgende batch. Dit aantal zou hoger moeten zijn dan de maximale doorvoer (Gelijktijdig * Berichtensnelheid).", "settings.performance.concurrency": "Gelijktijdig", "settings.performance.concurrencyHelp": "Maximum aantal workers (threads) die gelijktijdig proberen berichten te versturen.", "settings.performance.maxErrThreshold": "Maximum aantal fouten", "settings.performance.maxErrThresholdHelp": "Het a<PERSON><PERSON> fouten (bv.: SMTP-timeouts tijdens het e-mailen) dat een lopende campagne verdraagt voor het gepauzeerd wordt voor handmatig onderzoek of ingrijpen. Zet op 0 om dit nooit te pauzeren.", "settings.performance.messageRate": "<PERSON><PERSON><PERSON>nelhe<PERSON>", "settings.performance.messageRateHelp": "Maximum aantal berichten dat per worker per seconde verstuurd wordt. Als Gelijktijdig = 10 en Berichtensnelheid = 10, kunnen er 10x10=100 berichten per seconde verstuurd worden. De<PERSON> waarde moet samen met Gelijktijdig aangepast worden om het aantal uitgaande berichten per seconde onder de limiet van de berichtserver te houden.", "settings.performance.name": "Uitvoeren", "settings.performance.slidingWindow": "Sliding window limiet inschakelen", "settings.performance.slidingWindowDuration": "<PERSON><PERSON>", "settings.performance.slidingWindowDurationHelp": "<PERSON><PERSON> van de periode van de sliding window (m for minute, h for hour).", "settings.performance.slidingWindowHelp": "Beperk het aantal berichten dat binnen een bepaalde periode verstuurd wordt. Als de limiet bereikt wordt, worden berichten niet verstuurd tot het aantal terug onder de limiet zit.", "settings.performance.slidingWindowRate": "<PERSON><PERSON>", "settings.performance.slidingWindowRateHelp": "Maximum aantal berichten om te versturen binnen de periode.", "settings.privacy.allowBlocklist": "<PERSON><PERSON><PERSON><PERSON>", "settings.privacy.allowBlocklistHelp": "Abonnees toelaten zich voor alle mailinglijsten uit te schrijven en zichzelf te markeren als geblokkeerd?", "settings.privacy.allowExport": "Exporteren toelaten", "settings.privacy.allowExportHelp": "Abonnees toelaten om data die over hen is verzameld te exporteren?", "settings.privacy.allowPrefs": "Allow preference changes", "settings.privacy.allowPrefsHelp": "Allow subscribers to change preferences such as their names and multiple list subscriptions.", "settings.privacy.allowWipe": "Data wipe <PERSON><PERSON><PERSON>", "settings.privacy.allowWipeHelp": "Abonnees toelaten z<PERSON>, al hun inschrijvingen en alle andere data over hun te verwijderen uit de database. Views en klikken op links van campagnes worden verwijderd, maar het aantal views en kliks blijft hetzelfde zodat statistieken niet veranderen.", "settings.privacy.domainBlocklist": "Domein blocklist", "settings.privacy.domainBlocklistHelp": "E-mail adress<PERSON> met deze domeinen kunnen zich niet inschrijven. <PERSON><PERSON> een domein in per lijn, bv.: somesite.com", "settings.privacy.individualSubTracking": "Individuele abonnees volgen", "settings.privacy.individualSubTrackingHelp": "Track campagneviews en -clicks per abonnee. Als dit uitgeschakeld is, worden views en kliks bijgehouden zonder aan individuele abonnees gelinkt te worden.", "settings.privacy.listUnsubHeader": "Voeg `List-Unsubscribe` header toe", "settings.privacy.listUnsubHeaderHelp": "Voeg header toe zodat e-mailprogramma's gebruike<PERSON> zich kunnen laten uitschrijven in een klik.", "settings.privacy.name": "Privacy", "settings.restart": "Herstarten", "settings.smtp.customHeaders": "Aangepaste headers", "settings.smtp.customHeadersHelp": "Optionele lijst met e-mail headers om toe te voegen aan alle berichten van deze server. Bv.: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Ingeschakeld", "settings.smtp.heloHost": "HELO hostnaam", "settings.smtp.heloHostHelp": "Optioneel. Sommige SMTP-servers vereisen een FQDN in de hostnaam. Standaard nemen HELLOs `localhost`. Stel dit in als een custom hostname gebruikt moet worden.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Nieuwe pogingen", "settings.smtp.retriesHelp": "Aantal keer om opnieuw te proberen als een bericht mislukt.", "settings.smtp.sendTest": "Stuur e-mail", "settings.smtp.setCustomHeaders": "Stel custom headers in", "settings.smtp.testConnection": "Test verbinding", "settings.smtp.testEnterEmail": "Enter password to test", "settings.smtp.toEmail": "Naar e-mail", "settings.title": "Instellingen", "settings.updateAvailable": "<PERSON><PERSON> ni<PERSON> update {version} is be<PERSON><PERSON><PERSON><PERSON>.", "subscribers.advancedQuery": "Geavanceerd", "subscribers.advancedQueryHelp": "Gedeeltelijke SQL uitdrukking om abonnees attributen op te vragen", "subscribers.attribs": "Attributen", "subscribers.attribsHelp": "Attributen worden gedefinieerd in een JSON map, bijvoorbeeld:", "subscribers.blocklistedHelp": "Geblokkeerde abonnees zullen nooit e-mails ontvangen.", "subscribers.confirmBlocklist": "{num} a<PERSON><PERSON>(s) blo<PERSON><PERSON>?", "subscribers.confirmDelete": "{num} abonnee(s) verwijderen?", "subscribers.confirmExport": "{num} abonnee(s) exporteren?", "subscribers.domainBlocklisted": "Dit e-maildomein is geblokkeerd.", "subscribers.downloadData": "Data downloaden", "subscribers.email": "E-mail", "subscribers.emailExists": "E-mail bestaat al.", "subscribers.errorBlocklisting": "Fout bij blokker<PERSON> a<PERSON>: {error}", "subscribers.errorNoIDs": "<PERSON><PERSON> IDs ingegeven.", "subscribers.errorNoListsGiven": "<PERSON><PERSON> lijsten ing<PERSON>.", "subscribers.errorPreparingQuery": "Fout bij voorbereiden abonnees-query: {error}", "subscribers.errorSendingOptin": "Fout bij verzenden opt-in e-mail.", "subscribers.export": "<PERSON><PERSON><PERSON>", "subscribers.invalidAction": "Ongeldige actie.", "subscribers.invalidEmail": "Ongeldige e-mail.", "subscribers.invalidJSON": "Ongeldige JSON in attributen.", "subscribers.invalidName": "Ongeldige naam.", "subscribers.listChangeApplied": "<PERSON><PERSON>ing aan lijst toegepast.", "subscribers.lists": "<PERSON><PERSON><PERSON>", "subscribers.listsHelp": "<PERSON><PERSON><PERSON> wa<PERSON>van abonnees zichzelf hebben uitgeschreven kunnen niet worden verwijderd.", "subscribers.listsPlaceholder": "Lijsten om voor in te schrijven", "subscribers.manageLists": "Lijsten managen", "subscribers.markUnsubscribed": "Markeer als uitgeschreven", "subscribers.newSubscriber": "<PERSON><PERSON><PERSON>", "subscribers.numSelected": "{num} a<PERSON><PERSON>(s) geselecteerd", "subscribers.optinSubject": "Inschrijving bevestigen", "subscribers.preconfirm": "Inschrijvingen automatisch bevestigen", "subscribers.preconfirmHelp": "Verzend geen opt-in e-mails en markeer alle inschrijvingen als 'bevestigd'.", "subscribers.query": "Query", "subscribers.queryPlaceholder": "E-mail of naam", "subscribers.reset": "Resetten", "subscribers.selectAll": "Selecteer alle {num}", "subscribers.sendOptinConfirm": "<PERSON><PERSON>ur opt-in bevestiging", "subscribers.sentOptinConfirm": "Opt-in bevestiging verzonden", "subscribers.status.blocklisted": "Geblokkeerd", "subscribers.status.confirmed": "Bevestigd", "subscribers.status.enabled": "Gea<PERSON>erd", "subscribers.status.subscribed": "Ingeschreven", "subscribers.status.unconfirmed": "Onbevestigd", "subscribers.status.unsubscribed": "Uitgeschreven", "subscribers.subscribersDeleted": "{num} abonnee(s) verwijderd", "templates.cantDeleteDefault": "Kan standaardtemplate niet verwijderen", "templates.default": "Standaard", "templates.dummyName": "Testcampagne", "templates.dummySubject": "Testcampagne onderwerp", "templates.errorCompiling": "Fout bij compileren template: {error}", "templates.errorRendering": "Fout bij renderen bericht: {error}", "templates.fieldInvalidName": "Ongeldige lengte voor naam.", "templates.makeDefault": "Stel in als standaard", "templates.newTemplate": "Nieuwe template", "templates.placeholderHelp": "De plaatshouder {placeholder} moet exact een keer voorkomen in de template.", "templates.preview": "Voorbeeld", "templates.rawHTML": "HTML code", "templates.subject": "Subject", "users.login": "Inloggen", "users.logout": "Uitloggen"}