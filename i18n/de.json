{"_.code": "de", "_.name": "<PERSON><PERSON><PERSON> (de)", "admin.errorMarshallingConfig": "<PERSON><PERSON> beim Einlesen der Konfiguration: {error}", "analytics.count": "<PERSON><PERSON><PERSON>", "analytics.fromDate": "<PERSON>", "analytics.invalidDates": "Ungültiges Datum in `von` oder `bis`.", "analytics.isUnique": "Statistiken können Abonnenten zugeordnet werden.", "analytics.links": "Verweise", "analytics.nonUnique": "Statistiken sind anonym, da Einzelabonnenten Tracking abgeschaltet ist.", "analytics.title": "Statistiken", "analytics.toDate": "Bis", "bounces.source": "<PERSON><PERSON>", "bounces.unknownService": "Unbekannter Dienst.", "bounces.view": "<PERSON><PERSON><PERSON> anzeigen", "campaigns.addAltText": "Füge eine alternative Nachricht in unformatierten Text hinzu (falls HTML nicht angezeigt werden kann).", "campaigns.archive": "Archiv", "campaigns.archiveEnable": "Im öffentlichen Archiv veröffentlichen", "campaigns.archiveHelp": "Veröffentliche die Nachricht (lauf<PERSON><PERSON>, pausierte, beendete) der Kampagne im öffentlichen Archiv.", "campaigns.archiveMeta": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.archiveMetaHelp": "Dummy-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die in der öffentlichen Nachricht verwendet werden sollen, einsch<PERSON>ßlich Name, E-Mail und alle optionalen Attribute, die in der Kampagnennachricht oder -vorlage verwendet werden.", "campaigns.cantUpdate": "Eine laufende oder abgeschlossene Kampagne kann nicht geändert werden.", "campaigns.clicks": "<PERSON><PERSON><PERSON>", "campaigns.confirmDelete": "<PERSON><PERSON><PERSON> {name}", "campaigns.confirmSchedule": "Diese Kampagne startet zu einem konfigurierten Zeitpunkt. Jetzt starten?", "campaigns.confirmSwitchFormat": "<PERSON><PERSON> du <PERSON>, kann es sein, dass deine Formatierung verloren geht.", "campaigns.content": "Inhalt", "campaigns.contentHelp": "Inhalt hier", "campaigns.continue": "Fortsetzen", "campaigns.copyOf": "<PERSON><PERSON> {name}", "campaigns.customHeadersHelp": "<PERSON>e von benutzerdefinierten Kopfzeilen, welche in ausgehenden Nachrichten gesetzt werden sollen . Beispiel: [{\"X-Kopfzeile\": \"wert\"}, {\"X-Kopfzeile2\": \"wert\"}]", "campaigns.dateAndTime": "Datum und Zeit", "campaigns.ended": "Abgeschlossen", "campaigns.errorSendTest": "<PERSON><PERSON> beim Senden der Testmail: {error}", "campaigns.fieldInvalidBody": "Fehler beim Erstellen des Kampagneninhalts: {error}", "campaigns.fieldInvalidFromEmail": "Ungültiges Format `from_email`.", "campaigns.fieldInvalidListIDs": "Ungültige Listen IDs.", "campaigns.fieldInvalidMessenger": "Unbekann<PERSON> Messenger {name}.", "campaigns.fieldInvalidName": "Ungültige Länge für `name`.", "campaigns.fieldInvalidSendAt": "Das Datum muss in der Zukunft liegen.", "campaigns.fieldInvalidSubject": "Ungültige Länge für `subject`.", "campaigns.formatHTML": "HTML formatieren", "campaigns.fromAddress": "Absender", "campaigns.fromAddressPlaceholder": "Dein Name <<EMAIL>>", "campaigns.invalid": "Ungültige Kampagne", "campaigns.invalidCustomHeaders": "Ungültige benutzerdefinierte Kopfzeilen: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Die Kampagne benötigt ein `send_at` Sendedatum, um automatisch verschickt zu werden.", "campaigns.newCampaign": "Neue Kampagne", "campaigns.noKnownSubsToTest": "Es sind keine Abonnenten für den Test vorhanden.", "campaigns.noOptinLists": "<PERSON><PERSON>-In Liste gefunden um die Kampagne anzulegen.", "campaigns.noSubs": "Die Kampagne kann nicht angelegt werden, da in den ausgewählten Listen keine Abonnenten vorhanden sind.", "campaigns.noSubsToTest": "Das Ziel hat keine Abonnenten.", "campaigns.notFound": "Die Kampagne konnte nicht gefunden werden.", "campaigns.onlyActiveCancel": "Nur aktive Kampagnen können abgebrochen werden.", "campaigns.onlyActivePause": "Nur aktive Kampagnen können pausiert werden.", "campaigns.onlyDraftAsScheduled": "Nur Kampagnen in Vorbereitung können geplant werden.", "campaigns.onlyPausedDraft": "Nur Kampagnen in Vorbereitung oder pausierte Kampagnen können gestartet werden.", "campaigns.onlyScheduledAsDraft": "Nur geplante Kampagnen können als Vorbereitung gespeichert werden.", "campaigns.pause": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.plainText": "Unformatierter Text", "campaigns.preview": "Vorschau", "campaigns.progress": "Fort<PERSON><PERSON>t", "campaigns.queryPlaceholder": "Name oder <PERSON><PERSON>ff", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "HTML Code", "campaigns.removeAltText": "Lösche den alternativen unformatierten Text", "campaigns.richText": "Rich-Text", "campaigns.schedule": "<PERSON><PERSON><PERSON><PERSON> planen", "campaigns.scheduled": "geplant", "campaigns.send": "Senden", "campaigns.sendLater": "Später senden", "campaigns.sendTest": "Testnachricht versenden", "campaigns.sendTestHelp": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>` nach einer E-Mail-Adresse um mehrere Adressaten hinzuzufügen. Die Adressaten müssen Abonnenten sein.", "campaigns.sendToLists": "Listen an die gesendet wird:", "campaigns.sent": "Gesendet", "campaigns.start": "<PERSON><PERSON><PERSON><PERSON> starten", "campaigns.started": "\"{name}\" gestartet", "campaigns.startedAt": "Gestartet", "campaigns.stats": "Statistiken", "campaigns.status.cancelled": "Abgebrochen", "campaigns.status.draft": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.status.finished": "<PERSON><PERSON>", "campaigns.status.paused": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.status.running": "<PERSON><PERSON><PERSON>", "campaigns.status.scheduled": "<PERSON><PERSON><PERSON>", "campaigns.statusChanged": "\"{name}\" ist {status}", "campaigns.subject": "<PERSON><PERSON><PERSON>", "campaigns.testEmails": "E-Mails", "campaigns.testSent": "Testnachricht gesendet", "campaigns.timestamps": "Zeitstempel", "campaigns.trackLink": "Track Link", "campaigns.views": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.campaignViews": "Kampagnenansichten", "dashboard.linkClicks": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.messagesSent": "Nachrichten gesendet", "dashboard.orphanSubs": "<PERSON><PERSON><PERSON><PERSON>", "email.data.info": "Eine Kopie aller gespeicherten Daten sind in der angehängten JSON-Datei gespeichert. <PERSON><PERSON> kann in einem Texteditor angezeigt werden.", "email.data.title": "<PERSON><PERSON>", "email.optin.confirmSub": "Abonnement bestätigen", "email.optin.confirmSubHelp": "Bestätige dein Abonnement mit einem Klick auf den nachfolgenden Knopf.", "email.optin.confirmSubInfo": "Du hast dich für folgende Listen angemeldet:", "email.optin.confirmSubTitle": "Abonnement bestätigen", "email.optin.confirmSubWelcome": "Hall<PERSON>", "email.optin.privateList": "Private Liste", "email.status.campaignReason": "<PERSON><PERSON><PERSON>", "email.status.campaignSent": "Gesendet", "email.status.campaignUpdateTitle": "Kampagnen Update", "email.status.importFile": "<PERSON><PERSON>", "email.status.importRecords": "Aufzeichnungen", "email.status.importTitle": "Update Importieren", "email.status.status": "Status", "email.unsub": "Abmelden", "email.unsubHelp": "Du möchtest diese E-Mails nicht mehr?", "email.viewInBrowser": "<PERSON><PERSON> anzeigen", "forms.formHTML": "Formular HTML", "forms.formHTMLHelp": "Benutze den folgenden HTML-Code, um das Formular zum Anmelden auf einer externen Seite anzuzeigen. Das Formular sollte das `email` Feld und eines oder mehrere `l` (Listen UUID) Felder enthalten. `name` ist optional.", "forms.noPublicLists": "Es existieren keine öffentlichen Listen, für die ein Formular erstellt werden kann.", "forms.publicLists": "Öffentliche Listen", "forms.publicSubPage": "Öffentliche Abonnement Seite", "forms.selectHelp": "<PERSON><PERSON>hle die Listen, die du zum Formular hinzufügen möchtest.", "forms.title": "Formulare", "globals.buttons.add": "Hinzufügen", "globals.buttons.addNew": "<PERSON><PERSON>", "globals.buttons.back": "Zurück", "globals.buttons.cancel": "Abbrechen", "globals.buttons.clone": "Duplizieren", "globals.buttons.close": "Schließen", "globals.buttons.continue": "Fortfahren", "globals.buttons.delete": "Löschen", "globals.buttons.deleteAll": "Alle Löschen", "globals.buttons.edit": "<PERSON><PERSON><PERSON>", "globals.buttons.enabled": "Aktiviert", "globals.buttons.insert": "Einfügen", "globals.buttons.learnMore": "<PERSON><PERSON><PERSON><PERSON> mehr", "globals.buttons.more": "<PERSON><PERSON>", "globals.buttons.new": "<PERSON>eu", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Entfernen", "globals.buttons.save": "Speichern", "globals.buttons.saveChanges": "Änderungen speichern", "globals.days.0": "So", "globals.days.1": "Mo", "globals.days.2": "Di", "globals.days.3": "<PERSON>", "globals.days.4": "Do", "globals.days.5": "Fr", "globals.days.6": "Sa", "globals.days.7": "Sa", "globals.fields.createdAt": "<PERSON><PERSON><PERSON><PERSON>", "globals.fields.description": "Beschreibung", "globals.fields.id": "ID", "globals.fields.name": "Name", "globals.fields.status": "Status", "globals.fields.type": "<PERSON><PERSON>", "globals.fields.updatedAt": "<PERSON>ktual<PERSON><PERSON>", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Bist du sicher?", "globals.messages.confirmDiscard": "Änderungen verwerfen?", "globals.messages.created": "\"{name}\" erstellt", "globals.messages.deleted": "\"{name}\" <PERSON><PERSON><PERSON><PERSON>", "globals.messages.deletedCount": "{name} ({num}) gelöscht", "globals.messages.done": "Abgeschlossen", "globals.messages.emptyState": "Hier ist nichts", "globals.messages.errorCreating": "<PERSON><PERSON> beim <PERSON> von {name}: {error}", "globals.messages.errorDeleting": "<PERSON><PERSON> beim Löschen von {name}: {error}", "globals.messages.errorFetching": "<PERSON><PERSON> beim <PERSON> von {name}: {error}", "globals.messages.errorInvalidIDs": "Eine oder mehrere IDs sind ungültig: {error}", "globals.messages.errorUUID": "<PERSON><PERSON> beim Erzeugen einer UUID: {error}", "globals.messages.errorUpdating": "<PERSON><PERSON> beim Aktualisieren von {name}: {error}", "globals.messages.internalError": "<PERSON><PERSON>", "globals.messages.invalidData": "Ungültige Daten", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Ungültige ID", "globals.messages.invalidUUID": "Ungültige UUID", "globals.messages.missingFields": "<PERSON><PERSON><PERSON><PERSON>: {name}", "globals.messages.notFound": "{name} nicht gefunden", "globals.messages.passwordChange": "Gib dein Passwort für die Änderung ein", "globals.messages.updated": "\"{name}\" aktual<PERSON>ert", "globals.months.1": "Jan", "globals.months.10": "Okt", "globals.months.11": "Nov", "globals.months.12": "<PERSON>z", "globals.months.2": "Feb", "globals.months.3": "<PERSON><PERSON><PERSON>", "globals.months.4": "Apr", "globals.months.5": "<PERSON>", "globals.months.6": "Jun", "globals.months.7": "Jul", "globals.months.8": "Aug", "globals.months.9": "Sep", "globals.states.off": "Aus", "globals.terms.all": "Alle", "globals.terms.analytics": "Statistiken", "globals.terms.bounce": "Bounce | Bounces", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Kampagne | Kampagnen", "globals.terms.campaigns": "<PERSON><PERSON>ag<PERSON>", "globals.terms.dashboard": "Überblick", "globals.terms.day": "Tag | Tage", "globals.terms.hour": "Stunde | Stunden", "globals.terms.list": "Liste | Listen", "globals.terms.lists": "Listen", "globals.terms.media": "Medien | Medien", "globals.terms.messenger": "Messenger | Messenger", "globals.terms.messengers": "<PERSON>", "globals.terms.minute": "Minute | Minuten", "globals.terms.month": "Monat | Monate", "globals.terms.second": "Sekunde | Sekunden", "globals.terms.settings": "Einstellungen", "globals.terms.subscriber": "Abonnent | Abonnenten", "globals.terms.subscribers": "Abonnenten", "globals.terms.subscriptions": "Abonnement | Abonnements", "globals.terms.tag": "Tag | Tags", "globals.terms.tags": "Tags", "globals.terms.template": "Vorlage | Vorlagen", "globals.terms.templates": "Vorlagen", "globals.terms.tx": "Transactional | Transactional", "globals.terms.year": "Jahr | Jahre", "import.alreadyRunning": "Bitte warte bis der aktuelle Importvorgang beendet wurde.", "import.blocklist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "import.csvDelim": "CSV-Trennzeichen", "import.csvDelimHelp": "Das Standard-Trennzeichen ist ein Komma.", "import.csvExample": "Beispiel CSV (Rohdaten)", "import.csvFile": "CSV- oder ZIP-Datei", "import.csvFileHelp": "<PERSON>licke oder ziehe eine CSV- oder ZIP-<PERSON><PERSON> hierher", "import.errorCopyingFile": "<PERSON><PERSON> beim Ko<PERSON>: {error}", "import.errorProcessingZIP": "Fehler beim Verarbeiten der ZIP Datei: {error}", "import.errorStarting": "<PERSON><PERSON> beim Import: {error}", "import.importDone": "Abgeschlossen", "import.importStarted": "Import gestartet", "import.instructions": "Anleitung", "import.instructionsHelp": "Lade eine CSV Datei (wahlweise auch als ZIP-Archiv) hoch, um eine Liste von Abonnenten zu importieren. Die CSV Datei muss folgende Spalten mit den exakten Namen haben. Attribute (optional) müssen valides JSON mit escapten, doppelten Anführungszeichen sein.", "import.invalidDelim": "`delim` muss ein einzelnes Zeichen sein", "import.invalidFile": "Ungültige Datei: {error}", "import.invalidMode": "Ung<PERSON><PERSON><PERSON> Mo<PERSON>", "import.invalidParams": "Ungültiger Parameter: {error}", "import.invalidSubStatus": "Ungültiger Abonnement Status", "import.listSubHelp": "Listen, die abonniert werden.", "import.mode": "Modus", "import.overwrite": "Überschreiben?", "import.overwriteHelp": "Überschreibe Name, Attribute und Abonnement-Status von bestehenden Abonnenten?", "import.recordsCount": "{num} / {total} Einträge", "import.stopImport": "Import stoppen", "import.subscribe": "Abonnieren", "import.title": "Abonnenten importieren", "import.upload": "Hochladen", "lists.confirmDelete": "Bist du sicher? Das Löschen einer Liste löscht keine Abonnenten.", "lists.confirmSub": "Bestätige das/die Abonnement/s von {name}", "lists.invalidName": "Ungültiger Name", "lists.newList": "Neue Liste", "lists.optin": "Opt-In", "lists.optinHelp": "Double Opt-In sendet eine E-Mail an den Abonnenten mit der Frage nach Bestätigung. Kampagnen werden nur an bestätigte Abonnenten gesendet.", "lists.optinTo": "<PERSON>t-<PERSON> <PERSON> {name}", "lists.optins.double": "Double Opt-In", "lists.optins.single": "Einfache Anmeldung", "lists.sendCampaign": "<PERSON><PERSON><PERSON><PERSON>", "lists.sendOptinCampaign": "Opt-In Kampagne senden", "lists.type": "<PERSON><PERSON>", "lists.typeHelp": "Öffentliche Listen können von allen abonniert werden. Die Namen der Listen könnten auf einer öffentlichen Seite, wie z.B. der Seite für die Abonnentenverwaltung erscheinen.", "lists.types.private": "Privat", "lists.types.public": "<PERSON><PERSON><PERSON><PERSON>", "logs.title": "Logs", "maintenance.help": "Je nach Datenmenge kann es eine <PERSON> dauern, bis einige Aktionen abgeschlossen sind.", "maintenance.maintenance.unconfirmedOptins": "Unbestätigte Opt-in-Abonnements", "maintenance.olderThan": "<PERSON><PERSON>s", "maintenance.title": "Wartung", "maintenance.unconfirmedSubs": "Unbestätigte Abonnements älter als {name} Tage.", "media.errorReadingFile": "<PERSON><PERSON> beim Lesen der Datei: {error}", "media.errorResizing": "<PERSON><PERSON> beim Anpassen der Größe des Bildes: {error}", "media.errorSavingThumbnail": "<PERSON><PERSON> beim Speichern des Thumbnails: {error}", "media.errorUploading": "<PERSON><PERSON> beim Hochladen der Datei: {error}", "media.invalidFile": "Ungültige Datei: {error}", "media.title": "Medien", "media.unsupportedFileType": "<PERSON>cht unterstützter Dateityp ({type})", "media.upload": "Hochladen", "media.uploadHelp": "<PERSON><PERSON><PERSON> oder ziehe ein oder mehrere Bilder hierhin", "media.uploadImage": "Bilder <PERSON><PERSON><PERSON>", "menu.allCampaigns": "Alle Kampagnen", "menu.allLists": "Alle Listen", "menu.allSubscribers": "Alle Abonnenten", "menu.dashboard": "Übersicht", "menu.forms": "Formulare", "menu.import": "Importieren", "menu.logs": "Logs", "menu.maintenance": "Wartung", "menu.media": "Medien", "menu.newCampaign": "<PERSON><PERSON>", "menu.settings": "Einstellungen", "public.archiveEmpty": "Noch keine archivierten Nachrichten.", "public.archiveTitle": "Archiv der Mailinglisten", "public.blocklisted": "Dauerhaft abgemeldet.", "public.campaignNotFound": "Die E-Mail wurde nicht gefunden.", "public.confirmOptinSubTitle": "Abonnement bestätigen", "public.confirmSub": "Abonnement bestätigen", "public.confirmSubInfo": "Du hast dich für folgenden Listen angemeldet:", "public.confirmSubTitle": "Bestätigen", "public.dataRemoved": "Deine An<PERSON> und alle Daten wurden entfernt.", "public.dataRemovedTitle": "<PERSON><PERSON>", "public.dataSent": "<PERSON><PERSON> wurden dir per E-Mail als Anhang gesendet.", "public.dataSentTitle": "Daten gesendet", "public.errorFetchingCampaign": "Fehler beim Abrufen der E-Mail", "public.errorFetchingEmail": "E-Mail nicht gefunden", "public.errorFetchingLists": "Fehler beim Abrufen der Listen. Bitte probiere es nochmal.", "public.errorProcessingRequest": "Fehler bei der Anfrage. Bitte probiere es nochmal.", "public.errorTitle": "<PERSON><PERSON>", "public.invalidFeature": "Dieses Feature ist nicht verfügbar", "public.invalidLink": "Ungültiger Link", "public.managePrefs": "Einstellungen verwalten", "public.managePrefsUnsub": "Deselektiere die Listen, um dich von ihnen abzumelden.", "public.noListsAvailable": "Keine Listen zum Abonnieren verfügbar.", "public.noListsSelected": "<PERSON>ine Liste zum Abonnieren ausgewählt.", "public.noSubInfo": "Es gibt keine zu bestätigenden Abonnements", "public.noSubTitle": "Keine Abonnements", "public.notFoundTitle": "Nicht gefunden", "public.prefsSaved": "Einstellungen wurden gespeichert.", "public.privacyConfirmWipe": "B<PERSON> du sicher, dass du alle Abonnements und Daten dauerhaft löschen möchtest?", "public.privacyExport": "Daten exportieren", "public.privacyExportHelp": "Eine Kopie der gespeicherten Daten wird an deine E-Mail-Adresse versendet.", "public.privacyTitle": "Privatsphäre und Datenschutz", "public.privacyWipe": "Alle Daten löschen.", "public.privacyWipeHelp": "Alle deine Abonnements, sowie die dazugehörigen Daten werden dauerhaft gelöscht.", "public.sub": "Abonnieren", "public.subConfirmed": "Abonnement erfolgreich.", "public.subConfirmedTitle": "Bestätigt", "public.subName": "Name (optional)", "public.subNotFound": "Abonnement nicht gefunden.", "public.subOptinPending": "Dir wurde eine E-Mail zur Bestätigung geschickt.", "public.subPrivateList": "Private Liste", "public.subTitle": "Abonnieren", "public.unsub": "Abmelden", "public.unsubFull": "<PERSON><PERSON> von allen zukünftigen E-Mails abmelden.", "public.unsubHelp": "Möchtest du dich von dieser E-Mail Liste abmelden?", "public.unsubTitle": "Abmelden", "public.unsubbedInfo": "Du wurdest erfolgreich abgemeldet", "public.unsubbedTitle": "Abgemeldet", "public.unsubscribeTitle": "Von E-Mail Liste abmelden.", "settings.appearance.adminHelp": "Eigenes CSS für die Adminoberfläche.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "Eigenes CSS", "settings.appearance.customJS": "Eigenes JavaScript", "settings.appearance.name": "<PERSON><PERSON><PERSON>", "settings.appearance.publicHelp": "Eigenes CSS und JavaScript für öffentliche Seiten.", "settings.appearance.publicName": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.action": "Aktion", "settings.bounces.blocklist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.bounces.count": "<PERSON><PERSON><PERSON>", "settings.bounces.countHelp": "<PERSON><PERSON><PERSON> pro Abonnent", "settings.bounces.delete": "Löschen", "settings.bounces.enable": "Verarb<PERSON><PERSON> von Bounces aktivieren", "settings.bounces.enableMailbox": "Bounce-Postfach aktivieren", "settings.bounces.enableSES": "SES aktivieren", "settings.bounces.enableSendgrid": "SendGrid aktivieren", "settings.bounces.enableWebhooks": "Bounce-Webhooks aktivieren", "settings.bounces.enabled": "Aktiviert", "settings.bounces.folder": "<PERSON><PERSON><PERSON>", "settings.bounces.folderHelp": "Name des zu scannenden IMAP-Ordners. z.B.: Inbox.", "settings.bounces.invalidScanInterval": "Der Bounce Scan-Interval sollte mindestens 1 Minute betragen.", "settings.bounces.name": "<PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Scan-Interval", "settings.bounces.scanIntervalHelp": "Interval mit dem das Bounce-Postfach gescannt werden soll (s for Sekunden, m für Minuten).", "settings.bounces.sendgridKey": "<PERSON><PERSON><PERSON>", "settings.bounces.type": "<PERSON><PERSON>", "settings.bounces.username": "<PERSON><PERSON><PERSON><PERSON>", "settings.confirmRestart": "<PERSON><PERSON> sic<PERSON>, dass laufende Kampagnen pausiert sind. Neustarten?", "settings.duplicateMessengerName": "Doppelter Messengerdienstname: {name}", "settings.errorEncoding": "Fehler bei der Kodierung der Einstellungen: {error}", "settings.errorNoSMTP": "Mindestens ein SMTP Block muss aktiviert sein", "settings.general.adminNotifEmails": "<PERSON><PERSON>", "settings.general.adminNotifEmailsHelp": "Kommagetrennte Liste von E-Mail <PERSON>ressen, welche <PERSON><PERSON>achrichtigungen erhalten sollen. Dies können Importupdates, Fertigste<PERSON><PERSON> von <PERSON>, <PERSON><PERSON> usw. sein", "settings.general.checkUpdates": "Suche nach Aktualisierungen", "settings.general.checkUpdatesHelp": "Prüfe regelmäßig nach Aktualisierungen und benachrichtige mich.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Veröffentlichen Sie Kampagnen, für die die Archivierung aktiviert ist, auf der öffentlichen Website.", "settings.general.enablePublicSubPage": "Aktiviere eine öffentliche Abonnement Seite", "settings.general.enablePublicSubPageHelp": "Zeige eine öffentliche Abonnement Seite mit allen öffentlichen Listen, die Personen abonnieren können.", "settings.general.faviconURL": "Favicon URL", "settings.general.faviconURLHelp": "(Optional) Vollständige URL zu einem statischen Favicon, welches für angezeigten Seiten wie Abmelden benutzt werden kann.", "settings.general.fromEmail": "Standard Absender-E-Mail", "settings.general.fromEmailHelp": "(Optional) Standard E-Mail für z.B. Abmeldungen.", "settings.general.language": "<PERSON><PERSON><PERSON>", "settings.general.logoURL": "Logo URL", "settings.general.logoURLHelp": "(Optional) Vollständige URL zu einem statischen Logo, welches für angezeigten Seiten wie Abmelden benutzt werden kann.", "settings.general.name": "Allgemein", "settings.general.rootURL": "Root URL", "settings.general.rootURLHelp": "Öffentliche URL der Installation (ohne Slash am Ende).", "settings.general.sendOptinConfirm": "Sende Opt-In Bestätigung", "settings.general.sendOptinConfirmHelp": "When new subscribers signup or are added via the admin form, send an opt-in confirmation e-mail.", "settings.general.siteName": "Seiten name", "settings.invalidMessengerName": "Der Name des Messengers ist ungültig", "settings.mailserver.authProtocol": "Autentifizierungsprotokoll", "settings.mailserver.host": "Server", "settings.mailserver.hostHelp": "SMTP Server Adresse.", "settings.mailserver.idleTimeout": "Maximale Wartezeit", "settings.mailserver.idleTimeoutHelp": "Wartezeit auf neue Aktivität bevor eine Verbindung geschlossen wird. (s für Sekunden, m für Minuten).", "settings.mailserver.maxConns": "<PERSON><PERSON>", "settings.mailserver.maxConnsHelp": "Maximale gleichzeitige Verbindungen zum SMTP Server", "settings.mailserver.password": "Passwort", "settings.mailserver.passwordHelp": "Gib dein Passwort ein, um es zu ändern", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "SMTP Server Port.", "settings.mailserver.skipTLS": "TLS Verifikation überspringen", "settings.mailserver.skipTLSHelp": "Überspringe die Hostname Prüfung im TLS Zertifikat.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Verwende STARTTLS.", "settings.mailserver.username": "<PERSON><PERSON><PERSON><PERSON>", "settings.mailserver.waitTimeout": "Maximale Wartezeit", "settings.mailserver.waitTimeoutHelp": "Wartezeit auf neue Aktivität bevor eine Verbindung geschlossen wird. (s für Sekunden, m für Minuten).", "settings.media.provider": "<PERSON><PERSON><PERSON>", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "Bucket Pfad", "settings.media.s3.bucketPathHelp": "Pfad im Bucket, in welchen die Dateien hochgeladen werden sollen. Standard ist /", "settings.media.s3.bucketType": "Bucket <PERSON>p", "settings.media.s3.bucketTypePrivate": "Privat", "settings.media.s3.bucketTypePublic": "<PERSON><PERSON><PERSON><PERSON>", "settings.media.s3.key": "AWS Access Key (Zugriffsschlüssel)", "settings.media.s3.publicURL": "Eigene öffentliche URL (optional)", "settings.media.s3.publicURLHelp": "Benutzerdefinierte S3-Domain zur Verwendung für Bilder-Links anstelle der standardmäßigen S3-Backend-URL.", "settings.media.s3.region": "Region", "settings.media.s3.secret": "AWS Access Secret", "settings.media.s3.uploadExpiry": "Upload Ablaufdatum", "settings.media.s3.uploadExpiryHelp": "(Optional) TTL (in Sekunden) für die generierte URL. Nur für private Buckets. (s, m, h, d für Sekunden, Minuten, Stunden, Tage).", "settings.media.s3.url": "S3 Backend-URL", "settings.media.s3.urlHelp": "Nur bei Verwendungen eines eigenen S3-kompatiblen Backends (wie Minio) ändern.", "settings.media.title": "Medien Uploads", "settings.media.upload.path": "Upload Pfad", "settings.media.upload.pathHelp": "Pfad zum Upload Verzeichnis.", "settings.media.upload.uri": "Upload URI", "settings.media.upload.uriHelp": "Upload URI, welche öffentlich sichtbar ist. Die hochgeladenen Medien sind öffentlich erreich unter {root_url}, z.B. https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "<PERSON><PERSON>", "settings.messengers.maxConnsHelp": "Maximale gleichzeitige Verbindungen zum SMTP Server.", "settings.messengers.messageSaved": "Einstellungen gespeichert. Lade neu...", "settings.messengers.name": "<PERSON>", "settings.messengers.nameHelp": "z.B.: my-sms. Alphanumerisch / Bindestrich.", "settings.messengers.password": "Passwort", "settings.messengers.retries": "<PERSON><PERSON><PERSON>", "settings.messengers.retriesHelp": "<PERSON><PERSON><PERSON> der Wiederholungen, wenn eine Nachricht fehlschlägt.", "settings.messengers.skipTLSHelp": "TLS Zertifikat nicht überprüfen.", "settings.messengers.timeout": "Max. Wartezeit", "settings.messengers.timeoutHelp": "Zeit bevor eine aktive Verbindung geschlossen und aus dem Pool entfernt wird. (s für Sekunden, m für Minuten).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Root URL des Postback Servers.", "settings.messengers.username": "<PERSON><PERSON><PERSON><PERSON>", "settings.needsRestart": "Einstellungen geändert. Pausiere alle laufenden Kampagnen und starte die App (Listmonk) neu", "settings.performance.batchSize": "Durchlaufgröße", "settings.performance.batchSizeHelp": "Die Anzahl an Abonnenten, die in einem Durchlauf verarbeitet werden. <PERSON>er Durchlauf holt die angegebene Anzahl an Abonnenten und schickt die Nachrichten. Idealerweise sollte dies höher sein als der maximal erreichbare Durchsatz (Anzahl Threads * Nachrichtenrate).", "settings.performance.concurrency": "<PERSON><PERSON><PERSON>s", "settings.performance.concurrencyHelp": "Maximale Anzahl an Threads, welche versuchen Nachrichten versenden.", "settings.performance.maxErrThreshold": "Maximale An<PERSON>hl <PERSON>", "settings.performance.maxErrThresholdHelp": "Die Anzahl der Fehler, welche toleriert werden sollen bevor eine Kampagne für die manuelle Kontrolle pausiert wird. 0 bedeutet kein Pausieren.", "settings.performance.messageRate": "Nachrichtenrate", "settings.performance.messageRateHelp": "Maximale Anzahl der Nachrichten, welche ein Thread pro Sekunde zu senden versucht. Beispiel: Wenn die Anzahl der Threads auf 10 und die Nachrichtenrate auch auf 10 gestellt wird, werden bis zu 10*10=100 Nachrichten pro Sekunden versendet. Bitte passend zu den Serverlimits konfigurieren.", "settings.performance.name": "Le<PERSON><PERSON>", "settings.performance.slidingWindow": "Zeitfenster aktivieren", "settings.performance.slidingWindowDuration": "<PERSON><PERSON>", "settings.performance.slidingWindowDurationHelp": "Dauer des Zeitfensters (m für Minuten, h für Stunden)", "settings.performance.slidingWindowHelp": "Begrenzt die Gesamtzahl der Nachrichten pro Zeit, welche gesendet werden. Wenn das Limit erreicht ist, wird gewartet bis das Zeitfenster abgelaufen ist, bevor neue Nachrichten gesendet werden.", "settings.performance.slidingWindowRate": "<PERSON><PERSON>", "settings.performance.slidingWindowRateHelp": "Maximale Anzahl Nachrichten, welche innerhalb des Zeitfensters versendet werden", "settings.privacy.allowBlocklist": "Aktiviere Sperrliste", "settings.privacy.allowBlocklistHelp": "Erlaube es Abonnenten ihre E-Mail-Adresse dauerhaft zu sperren.", "settings.privacy.allowExport": "Export aktivieren", "settings.privacy.allowExportHelp": "Erlaube Abonnenten alle ihre Daten zu exportieren?", "settings.privacy.allowPrefs": "Einstellungsänderungen zulassen", "settings.privacy.allowPrefsHelp": "Erlaube den Abonnenten, ihre Einstellungen zu ändern, wie z. B. ihren Namen und mehrere Listenabonnements.", "settings.privacy.allowWipe": "Löschen aktivieren", "settings.privacy.allowWipeHelp": "Erlaube Abonnenten alle Daten, welche über sie gespeichert sind zu löschen. Dies beinhaltet auch Klicks und Anzeigen, verändert allerdings nicht die Gesamtzahl. Statistiken bleiben auch unverändert.", "settings.privacy.domainBlocklist": "Domain-Sperrliste", "settings.privacy.domainBlocklistHelp": "E-Mail Adressen dieser Domains sind vom Abonnieren ausgeschlossen.  Eine Domain pro Zeile, z.B. somesite.com", "settings.privacy.individualSubTracking": "Einzelabonnenten Tracking", "settings.privacy.individualSubTrackingHelp": "Abonnentenviews und Klicks werden einzeln getrackt. <PERSON><PERSON> de<PERSON>, werden die Daten ohne Zuordnung zu Abonnenten gespeichert.", "settings.privacy.listUnsubHeader": "Inkludiere `List-Unsubscribe` (von Liste abmelden) Header", "settings.privacy.listUnsubHeaderHelp": "Inkludiere Header zum einfachen Abmelden in den E-Mails. Erlaubt es, den E-Mail Clients der Nutzer eine \",Ein Klick\"-Abmeldung anzubieten.", "settings.privacy.name": "Privatsphäre", "settings.restart": "Neustarten", "settings.smtp.customHeaders": "Benutzerdefinierte Header", "settings.smtp.customHeadersHelp": "(Optional) <PERSON><PERSON><PERSON> von benutzerdefinierten E-Mail Headern, welche in die Nachricht eingefügt werden sollen. Z.B.: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Aktiviert", "settings.smtp.heloHost": "HELO Hostname", "settings.smtp.heloHostHelp": "(Optional) Manche SMTP Server benötigen einen FQDN Hostnamen im HELO. Dieser kann hier gesetzt werden. Standard ist `localhost`.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Wiederholungen", "settings.smtp.retriesHelp": "Maximale Anzahl an Wiederholungen, wenn eine Machricht fehlschlägt.", "settings.smtp.sendTest": "E-mail senden", "settings.smtp.setCustomHeaders": "Benutzerdefinierten Header verwenden", "settings.smtp.testConnection": "Verbindung testen", "settings.smtp.testEnterEmail": "Passwort zum Testen e<PERSON>ben", "settings.smtp.toEmail": "Empfänger E-mail", "settings.title": "Einstellungen", "settings.updateAvailable": "Ein neues Update auf {version} ist verfügbar.", "subscribers.advancedQuery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribers.advancedQueryHelp": "Partieller SQL Ausdruck um Attribute der Abonnenten abzufragen", "subscribers.attribs": "Attribute", "subscribers.attribsHelp": "Attribute sind als JSON Map definiert, z.B.:", "subscribers.blocklistedHelp": "Blockierte Abonnenten werden nie wieder E-Mails erhalten.", "subscribers.confirmBlocklist": "Blockiere {num} Abonnent(en)?", "subscribers.confirmDelete": "<PERSON><PERSON><PERSON> {num} Abonnent(en)?", "subscribers.confirmExport": "Exportiere {num} Abonnent(en)?", "subscribers.domainBlocklisted": "Diese e-Mail Domain ist blockiert.", "subscribers.downloadData": "Daten herunterladen", "subscribers.email": "E-Mail", "subscribers.emailExists": "E-Mail existiert bereits.", "subscribers.errorBlocklisting": "Fehler. Abonnement ist geblockt: {error}", "subscribers.errorNoIDs": "<PERSON><PERSON> IDs angegeben.", "subscribers.errorNoListsGiven": "Keine Listen angegeben.", "subscribers.errorPreparingQuery": "Fehler beim Vorbereiten der Abonnentenabfrage: {error}", "subscribers.errorSendingOptin": "Fehler beim Senden der Opt-In E-Mail.", "subscribers.export": "Exportieren", "subscribers.invalidAction": "Ungültiger Vorgang.", "subscribers.invalidEmail": "Ungültige E-Mail.", "subscribers.invalidJSON": "Ungültiges JSON in den Attributen.", "subscribers.invalidName": "Ungültiger Name.", "subscribers.listChangeApplied": "Änderungen an der Liste gespeichert.", "subscribers.lists": "Listen", "subscribers.listsHelp": "Listen, von denen sich Abonnenten selbst abgemeldet haben, können nicht entfernt werden.", "subscribers.listsPlaceholder": "An den Listen anmelden ", "subscribers.manageLists": "Listen verwalten", "subscribers.markUnsubscribed": "Als abgemeldet markieren", "subscribers.newSubscriber": "<PERSON><PERSON><PERSON>", "subscribers.numSelected": "{num} Abonnent(en) ausgewählt", "subscribers.optinSubject": "Abonnement bestätigen", "subscribers.preconfirm": "Abonnement Opt-In überschreiben", "subscribers.preconfirmHelp": "<PERSON><PERSON>-In E-Mails senden und alle Abonnements als 'bestätigt' setzen.", "subscribers.query": "Abfrage", "subscribers.queryPlaceholder": "E-Mail oder Name", "subscribers.reset": "Z<PERSON>ücksetzen", "subscribers.selectAll": "<PERSON><PERSON>hle alle {num}", "subscribers.sendOptinConfirm": "Sende Opt-In Bestätigung", "subscribers.sentOptinConfirm": "Opt-In Bestätigung gesendet", "subscribers.status.blocklisted": "<PERSON><PERSON><PERSON>", "subscribers.status.confirmed": "Bestätigt", "subscribers.status.enabled": "Aktiviert", "subscribers.status.subscribed": "Angemeldet", "subscribers.status.unconfirmed": "Bestätigung ausstehend", "subscribers.status.unsubscribed": "Abgemeldet", "subscribers.subscribersDeleted": "{num} Abon<PERSON><PERSON> gelöscht", "templates.cantDeleteDefault": "Die Standardvorlage kann nicht gelöscht werden", "templates.default": "Standard", "templates.dummyName": "Test-<PERSON><PERSON><PERSON><PERSON>", "templates.dummySubject": "Test-Kampag<PERSON> Betreff", "templates.errorCompiling": "<PERSON><PERSON> beim Kompilieren des Templates: {error}", "templates.errorRendering": "Fehler beim Rendern der Nachricht: {error}", "templates.fieldInvalidName": "Ungültige Länge für `name`.", "templates.makeDefault": "Als Standard setzen", "templates.newTemplate": "Neue Vorlage", "templates.placeholderHelp": "Der Platzhalter \"{placeholder}\" darf nur einmal im Template vorkommen.", "templates.preview": "Vorschau", "templates.rawHTML": "HTML", "templates.subject": "<PERSON><PERSON><PERSON>", "users.login": "Anmelden", "users.logout": "Abmelden"}