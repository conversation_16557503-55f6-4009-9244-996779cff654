{"_.code": "jp", "_.name": "日本語 (jp)", "admin.errorMarshallingConfig": "マーシャリングコンフィグエラー: {error}", "analytics.count": "カウント", "analytics.fromDate": "から", "analytics.invalidDates": "無効な `から` 又は `まで` の日付.", "analytics.isUnique": "カウントは加入者特有のものとなります。", "analytics.links": "リンク", "analytics.nonUnique": "個々の加入者の追跡がオフとなっているため、カウントは特有のものではありません。", "analytics.title": "分析", "analytics.toDate": "まで", "bounces.source": "ソース", "bounces.unknownService": "不明のサービス。", "bounces.view": "バウンスビュー", "campaigns.addAltText": "代替のプレーンテキストメッセージを追加する", "campaigns.archive": "アーカイブ", "campaigns.archiveEnable": "公開アーカイブに発行する", "campaigns.archiveHelp": "公開アーカイブにキャンペーンメッセージを発行（実行中, 停止された, 終わりましたキャンペーン全部含めて）。", "campaigns.archiveMeta": "キャンペーンメタデータ", "campaigns.archiveMetaHelp": "キャンペーンのメッセージやテンプレートに使う偽データ（名やメールアドレスや設定）。", "campaigns.cantUpdate": "実行中又は終了しているキャンペーンの更新はできません。", "campaigns.clicks": "クリック", "campaigns.confirmDelete": "削除 {name}", "campaigns.confirmSchedule": "このキャンペーンは予定された日時に自動的に開始されます。スケジュールを開始しますか？", "campaigns.confirmSwitchFormat": "コンテンツのフォーマットが崩れる可能性があります。続けますか？", "campaigns.content": "コンテンツ", "campaigns.contentHelp": "コンテンツはこちらから", "campaigns.continue": "コンティニュー", "campaigns.copyOf": " {name}をコピー", "campaigns.customHeadersHelp": "送信メッセージに添付するカスタムヘッダーの配列。 例: [{\"X-Custom\": \"Value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "日時", "campaigns.ended": "終了", "campaigns.errorSendTest": "テスト送信エラー: {error}", "campaigns.fieldInvalidBody": "キャンペーン本体コンパイルエラー: {error}", "campaigns.fieldInvalidFromEmail": "無効な `メール_送り主`.", "campaigns.fieldInvalidListIDs": "無効なリストID", "campaigns.fieldInvalidMessenger": "不明な送り主 {name}。", "campaigns.fieldInvalidName": "無効な長さの名前です。", "campaigns.fieldInvalidSendAt": "予定日は将来の日付であること。", "campaigns.fieldInvalidSubject": "長さが無効です。", "campaigns.formatHTML": "HTMLをフォーマット", "campaigns.fromAddress": "送り主のアドレス", "campaigns.fromAddressPlaceholder": "あなたの氏名 <<EMAIL>>", "campaigns.invalid": "無効なキャンペーン", "campaigns.invalidCustomHeaders": "無効なカスタムヘッダー: {error}", "campaigns.markdown": "マークダウン", "campaigns.needsSendAt": "キャンペーンは予定日が必要です。", "campaigns.newCampaign": "新しいキャンペーン", "campaigns.noKnownSubsToTest": "テストする加入者が不明です。", "campaigns.noOptinLists": "キャンペーンを作るためのオプトインリストが見つかりません。", "campaigns.noSubs": "キャンペーンを作成するに選択したリストには加入者がいません。", "campaigns.noSubsToTest": "ターゲットとなる加入者がいません。", "campaigns.notFound": "キャンペーンが見つかりません。", "campaigns.onlyActiveCancel": "アクティブなキャンペーンのみキャンセル可能です。", "campaigns.onlyActivePause": "アクティブなキャンペーンのみ停止可能です。", "campaigns.onlyDraftAsScheduled": "ドラフトのキャンペーンのみスケジュールすることができます。", "campaigns.onlyPausedDraft": "停止されたキャンペーン、又はドラフトのみ開始できます。", "campaigns.onlyScheduledAsDraft": "スケジュールされたキャンペーンのみドラフトとして保存可能です。", "campaigns.pause": "停止", "campaigns.plainText": "プレーンテキスト", "campaigns.preview": "プレビュー", "campaigns.progress": "進捗", "campaigns.queryPlaceholder": "件名", "campaigns.rateMinuteShort": "分", "campaigns.rawHTML": "HTML(生)", "campaigns.removeAltText": "代替プレーンテキストメッセージの削除", "campaigns.richText": "リッチテキスト", "campaigns.schedule": "キャンペーンを計画する", "campaigns.scheduled": "スケジュール済み", "campaigns.send": "送信", "campaigns.sendLater": "後で送信", "campaigns.sendTest": "テストメッセージを送信", "campaigns.sendTestHelp": "複数の受信者を追加するには、アドレスを入力した後にエンターを押してください。アドレスは既存の加入者のものである必要があります。", "campaigns.sendToLists": "送信先リスト", "campaigns.sent": "送信済み", "campaigns.start": "キャンペーンを開始する", "campaigns.started": "\"{name}\" 開始済み", "campaigns.startedAt": "開始済み", "campaigns.stats": "統計", "campaigns.status.cancelled": "キャンセル済み", "campaigns.status.draft": "ドラフト", "campaigns.status.finished": "完了済み", "campaigns.status.paused": "停止中", "campaigns.status.running": "実行中", "campaigns.status.scheduled": "スケジュールされている", "campaigns.statusChanged": "\"{name}\" は {status}", "campaigns.subject": "件名", "campaigns.testDisabled": "使用禁止された", "campaigns.testEmails": "メール", "campaigns.testSent": "テストメッセージ送信済み", "campaigns.timestamps": "タイムスタンプ", "campaigns.trackLink": "リンクの追跡", "campaigns.views": "ビュー", "dashboard.campaignViews": "キャンペーンビュー", "dashboard.linkClicks": "リンクのクリック", "dashboard.messagesSent": "メッセージ送信済み", "dashboard.orphanSubs": "オーファン", "email.data.info": "あなたについて記録されたすべてのデータのコピーがJSON形式のファイルとして添付されています。テキストエディタで閲覧可能です。", "email.data.title": "あなたのデータ", "email.optin.confirmSub": "サブスクリプションを確認", "email.optin.confirmSubHelp": "下のボタンを押してサブスクリプションを確認する。", "email.optin.confirmSubInfo": "あなたは以下のリストに追加されました:", "email.optin.confirmSubTitle": "サブスクリプションを確認", "email.optin.confirmSubWelcome": "こんにちは", "email.optin.privateList": "プライベートリスト", "email.status.campaignReason": "理由", "email.status.campaignSent": "送信済み", "email.status.campaignUpdateTitle": "キャンペーンの更新", "email.status.importFile": "ファイル", "email.status.importRecords": "記録", "email.status.importTitle": "インポート更新", "email.status.status": "ステータス", "email.unsub": "登録を取り消す", "email.unsubHelp": "メールの配信を停止しますか？", "email.viewInBrowser": "ブラウザで閲覧", "forms.formHTML": "フォーム HTML", "forms.formHTMLHelp": "外部のウェブページにサブスクリプションフォームを表示するには、以下のHTMLを使用してください。フォームにはメールのフィールドと1つ又は複数の `l` (UUIDリスト) フィールドが含まれます. 名前のフィールドは任意です。", "forms.noPublicLists": "フォームを生成するための公開リストがありません。", "forms.publicLists": "公開リスト", "forms.publicSubPage": "公開サブスクリプションページ", "forms.selectHelp": "フォームを追加するリストを選択", "forms.title": "フォーム", "globals.buttons.add": "追加", "globals.buttons.addNew": "新規追加", "globals.buttons.back": "戻る", "globals.buttons.cancel": "キャンセル", "globals.buttons.clone": "クローン", "globals.buttons.close": "閉じる", "globals.buttons.continue": "続ける", "globals.buttons.delete": "消去", "globals.buttons.deleteAll": "全て消去", "globals.buttons.edit": "編集", "globals.buttons.enabled": "有効", "globals.buttons.insert": "入れる", "globals.buttons.learnMore": "さらに詳しく", "globals.buttons.more": "もっと", "globals.buttons.new": "新", "globals.buttons.ok": "OK", "globals.buttons.remove": "削除", "globals.buttons.save": "保存", "globals.buttons.saveChanges": "変更内容を保存", "globals.days.0": "日", "globals.days.1": "日", "globals.days.2": "月", "globals.days.3": "火", "globals.days.4": "水", "globals.days.5": "木", "globals.days.6": "金", "globals.days.7": "土", "globals.fields.createdAt": "作成済み", "globals.fields.description": "説明文", "globals.fields.id": "ID", "globals.fields.name": "名前", "globals.fields.status": "ステータス", "globals.fields.type": "タイプ", "globals.fields.updatedAt": "アップデート済み", "globals.fields.uuid": "UUID", "globals.messages.confirm": "本当に良いですか?", "globals.messages.confirmDiscard": "変更を破棄しますか？", "globals.messages.created": "\"{name}\" が作成されました", "globals.messages.deleted": "\"{name}\" が削除されました", "globals.messages.deletedCount": "{name} ({num}) が削除されました。", "globals.messages.done": "終わりました", "globals.messages.emptyState": "ここには何もありません", "globals.messages.errorCreating": "{name}作成エラー: {error}", "globals.messages.errorDeleting": "{name}削除エラー: {error}", "globals.messages.errorFetching": " {name}読み込みエラー: {error}", "globals.messages.errorInvalidIDs": "一つ、または複数のIDが無効です。: {error}", "globals.messages.errorUUID": "UUID生成エラー: {error}", "globals.messages.errorUpdating": "{name}更新エラー: {error}", "globals.messages.internalError": "内部サーバーエラー", "globals.messages.invalidData": "無効なデータ", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "無効なID", "globals.messages.invalidUUID": "無効なUUID", "globals.messages.missingFields": "フィールドがありません: {name}", "globals.messages.notFound": "{name} が見つかりません。", "globals.messages.passwordChange": "変更するには値を入力", "globals.messages.updated": "\"{name}\" 更新済み", "globals.months.1": "1月", "globals.months.10": "10月", "globals.months.11": "11月", "globals.months.12": "12月", "globals.months.2": "2月", "globals.months.3": "3月", "globals.months.4": "4月", "globals.months.5": "5月", "globals.months.6": "6月", "globals.months.7": "7月", "globals.months.8": "8月", "globals.months.9": "9月", "globals.states.off": "オフ", "globals.terms.all": "全部", "globals.terms.analytics": "分析", "globals.terms.bounce": "バウンス | バウンス", "globals.terms.bounces": "バウンス", "globals.terms.campaign": "キャンペーン | キャンペーン", "globals.terms.campaigns": "キャンペーン", "globals.terms.dashboard": "ダッシュボード", "globals.terms.day": "日 | 日", "globals.terms.hour": "時間 | 時間", "globals.terms.list": "リスト | リスト", "globals.terms.lists": "リスト", "globals.terms.media": "メディア | メディア", "globals.terms.messenger": "メッセンジャー | メッセンジャー", "globals.terms.messengers": "メッセンジャー", "globals.terms.minute": "分 | 分", "globals.terms.month": "月 | 月", "globals.terms.second": "秒 | 秒", "globals.terms.settings": "設定", "globals.terms.subscriber": "加入者 | 加入者", "globals.terms.subscribers": "加入者", "globals.terms.subscriptions": "サブスクリプション | サブスクリプション一覧", "globals.terms.tag": "タグ | タグ", "globals.terms.tags": "タグ", "globals.terms.template": "テンプレート | テンプレート", "globals.terms.templates": "テンプレート", "globals.terms.tx": "トランザクションメール | トランザクションメール", "globals.terms.year": "都市 | 都市", "import.alreadyRunning": "インポートはすでに実行されています。終わるまで待つか、停止してから再試行してください。", "import.blocklist": "ブロックリスト", "import.csvDelim": "CSV デリミタ", "import.csvDelimHelp": "デフォルトのデリミタはコンマです。", "import.csvExample": "raw CSV例", "import.csvFile": "CSV 又は ZIP ファイル", "import.csvFileHelp": "ここでCSVかZIPファイルをクリック、又はドラッグしてください。", "import.errorCopyingFile": "ファイルコピーエラー: {error}", "import.errorProcessingZIP": "ZIPファイル処理エラー: {error}", "import.errorStarting": "インポート開始エラー: {error}", "import.importDone": "完了", "import.importStarted": "インポート開始", "import.instructions": "指示", "import.instructionsHelp": "加入者を一括でインポートするにはCSVファイル、又はCSVファイルが一つ入ったZIPファイルをアップロードしてください。CSVファイルには正確なカラム名の含まれた以下のヘッダーが必要です。アトリビュート (任意)には有効なJSONの文字列で、エスケープしたダブルクオテーションで必要です。", "import.invalidDelim": "デリミタは1文字であること。", "import.invalidFile": "無効なファイル: {error}", "import.invalidMode": "無効なモード", "import.invalidParams": "無効なパラメータ: {error}", "import.invalidSubStatus": "無効なサブスクリプションステータス", "import.listSubHelp": "加入するリスト.", "import.mode": "モード", "import.overwrite": "上書きしますか?", "import.overwriteHelp": "既存の加入者の名前、アトリビュート、サブスクリプションステータスを上書きしますか？", "import.recordsCount": "{num} / {total} 記録", "import.stopImport": "インポートを中止", "import.subscribe": "加入", "import.title": "加入者をインポート", "import.upload": "アップロード", "lists.confirmDelete": "本当に良いですか？これは加入者を削除しません。", "lists.confirmSub": "{name}にサブスクリプション確認", "lists.invalidName": "無効な名前", "lists.newList": "新規リスト", "lists.optin": "オプトイン", "lists.optinHelp": "ダブルオプトインから加入者に確認のためのメールを送信します。ダブルオプトインのリストでは、確認された加入者のみにキャンペーンが送信されます。", "lists.optinTo": " {name}にダブルオプトイン", "lists.optins.double": "ダブルオプトイン", "lists.optins.single": "シングルオプトイン", "lists.sendCampaign": "キャンペーンを送信", "lists.sendOptinCampaign": "オプトインキャンペーン送信", "lists.type": "タイプ", "lists.typeHelp": "公開リストでは世界中から加入することができ、加入者の名前はサブスクリプション管理ページなどの公開ページに表示されることがあります。", "lists.types.private": "プライベート", "lists.types.public": "パブリック", "logs.title": "ログ", "maintenance.help": "データ量によりアクション完了するまでの時間が変わります。", "maintenance.maintenance.unconfirmedOptins": "未確認オプトインサブスクリプション", "maintenance.olderThan": "より古い", "maintenance.title": "メンテナンス", "maintenance.unconfirmedSubs": "{name}より古い未確認サブスクリプション", "media.errorReadingFile": "ファイル読み込みエラー: {error}", "media.errorResizing": "画像のリサイズエラー: {error}", "media.errorSavingThumbnail": "サムネイル保存エラー: {error}", "media.errorUploading": "ファイルアップロードのエラー: {error}", "media.invalidFile": "無効なファイル: {error}", "media.title": "メディア", "media.unsupportedFileType": "サポートされていないファイルタイプ ({type})", "media.upload": "アップロード", "media.uploadHelp": "ここに一枚か複数の画像をクリック、又はドラックしてください。", "media.uploadImage": "画像のアップロード", "menu.allCampaigns": "全てのキャンペーン", "menu.allLists": "全てのリスト", "menu.allSubscribers": "全ての加入者", "menu.dashboard": "ダッシュボード", "menu.forms": "フォーム", "menu.import": "インポート", "menu.logs": "ログ", "menu.maintenance": "メンテナンス", "menu.media": "メディア", "menu.newCampaign": "新規作成", "menu.settings": "設定", "public.archiveEmpty": "まだアーカイブメッセージはありません。", "public.archiveTitle": "メールアーカイブ", "public.blocklisted": "(永久)退会されました。", "public.campaignNotFound": "メールのメッセージが見つかりませんでした。", "public.confirmOptinSubTitle": "サブスクリプション確認", "public.confirmSub": "サブスクリプション確認", "public.confirmSubInfo": "以下のリストに追加されました:", "public.confirmSubTitle": "確認", "public.dataRemoved": "あなたのサブスクリプションと関連する全てのデータが削除されました。", "public.dataRemovedTitle": "削除されたデータ", "public.dataSent": "データは添付にてあなたのメールに送付されました。", "public.dataSentTitle": "データはメールで送られました。", "public.errorFetchingCampaign": "メールのメッセージが取得できませんでした。", "public.errorFetchingEmail": "メールのメッセージが見つかりませんでした。", "public.errorFetchingLists": "リストの取得にエラーがありました。再試行してください。", "public.errorProcessingRequest": "リクエスト中にエラーがありました。再試行してください。", "public.errorTitle": "エラー", "public.invalidFeature": "その機能は使用できません。", "public.invalidLink": "無効なリンク", "public.managePrefs": "設定変更", "public.managePrefsUnsub": "チェックを消すサブスクリプションは退会となります。", "public.noListsAvailable": "加入できるリストはありません。", "public.noListsSelected": "加入に有効なリストが選択されてません。", "public.noSubInfo": "確認できるサブスクリプションはありません。", "public.noSubTitle": "サブスクリプションはありません。", "public.notFoundTitle": "見つかりません", "public.prefsSaved": "設定保存成功しました。", "public.privacyConfirmWipe": "全ての加入データが永久に削除されますがよろしいでしょうか？", "public.privacyExport": "データをエクスポート", "public.privacyExportHelp": "データのコピーがメールにて送られます。", "public.privacyTitle": "プライバシーとデータ", "public.privacyWipe": "データを遠隔で消去する", "public.privacyWipeHelp": "データベースからサブスクリプションと関連データの全てを永久に削除する", "public.sub": "加入", "public.subConfirmed": "加入成功です。", "public.subConfirmedTitle": "確認済み", "public.subName": "名前 (任意)", "public.subNotFound": "サブスクリプションが見つかりません", "public.subOptinPending": "サブスクリプションを確認するためのメールが送信されました。", "public.subPrivateList": "プライベートリスト", "public.subTitle": "加入", "public.unsub": "登録を解除する。", "public.unsubFull": "今後全てのメール配信も停止する。", "public.unsubHelp": "このメーリングリストの登録も解除しますか？", "public.unsubTitle": "登録を解除する。", "public.unsubbedInfo": "登録の解除に成功しました。", "public.unsubbedTitle": "登録を解除する。", "public.unsubscribeTitle": "メーリングリストの登録を解除する", "settings.appearance.adminHelp": "管理UIに適用するカスタムCSS", "settings.appearance.adminName": "管理", "settings.appearance.customCSS": "カスタムCSS", "settings.appearance.customJS": "カスタムJavaScript", "settings.appearance.name": "アピアランス", "settings.appearance.publicHelp": "公開ページに適用するカスタムCSSとJavaScript。", "settings.appearance.publicName": "公開", "settings.bounces.action": "作用", "settings.bounces.blocklist": "ブロックリスト", "settings.bounces.count": "バウンス数", "settings.bounces.countHelp": "加入者ごとのバウンス数", "settings.bounces.delete": "削除", "settings.bounces.enable": "バウンス処理を有効にする", "settings.bounces.enableMailbox": "バウンスメールボックスを有効にする", "settings.bounces.enableSES": "SESを有効にする", "settings.bounces.enableSendgrid": "SendGridを有効にする", "settings.bounces.enableWebhooks": "バウンスウェブフックを有効にする", "settings.bounces.enabled": "有効", "settings.bounces.folder": "フォルダ", "settings.bounces.folderHelp": "スキャンするIMAPフォルダの名前。 例: Inbox.", "settings.bounces.invalidScanInterval": "バウンススキャン間隔は最低1分。", "settings.bounces.name": "バウンス", "settings.bounces.scanInterval": "スキャン間隔", "settings.bounces.scanIntervalHelp": "バウンスメールボックスのバウンスをスキャンする間隔 (秒はs,分はm).", "settings.bounces.sendgridKey": "SendGridキー", "settings.bounces.type": "タイプ", "settings.bounces.username": "ユーザーネーム", "settings.confirmRestart": "実行中のキャンペーンの停止を確認。再スタートしますか？", "settings.duplicateMessengerName": "メッセンジャーネームの複製: {name}", "settings.errorEncoding": "エンコード設定エラー: {error}", "settings.errorNoSMTP": "少なくとも一つのSMTPブロックが有効であること", "settings.general.adminNotifEmails": "管理者通知メール", "settings.general.adminNotifEmailsHelp": "インポートの更新、キャンペーンの完了、失敗など管理者通知を送信するメールアドレスのカンマ区切りリスト", "settings.general.checkUpdates": "アップデートの確認", "settings.general.checkUpdatesHelp": "定期的に新しいアプリのリリースを確認し、通知する。", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "公開ウエブサイトに公開アーカイブOK設定されたキャンペーンを発行する。", "settings.general.enablePublicSubPage": "公開サブスクリプションページを有効にする。", "settings.general.enablePublicSubPageHelp": "全ての公開リストを含む公開サブスクリプションページを表示し人々が加入できるようにする。", "settings.general.faviconURL": "ファビコンURL", "settings.general.faviconURLHelp": "(任意) 登録解除ページなどのユーザー向けビューに表示される静的ファビコンの完全なURL", "settings.general.fromEmail": "メールの`送り主`をデフォルトにする ", "settings.general.fromEmailHelp": "キャンペーンメール送信時に表示されるメールの `送り主`をデフォルトにする。キャンペーン毎に変更可能です。", "settings.general.language": "言語", "settings.general.logoURL": "ロゴURL", "settings.general.logoURLHelp": "(任意) 登録解除ページなどのユーザー向けビューに表示される静的ロゴの完全なURL。", "settings.general.name": "汎用", "settings.general.rootURL": "ルートURL", "settings.general.rootURLHelp": "インストール先の公開URL (末尾のスラッシュは不必要).", "settings.general.sendOptinConfirm": "オプトインの確認を送信", "settings.general.sendOptinConfirmHelp": "加入者が公開フォームからサインアップしたとき、又は管理者によって追加されたときに、オプトイン確認メールを送信。", "settings.general.siteName": "ウエブサイト名", "settings.invalidMessengerName": "無効なメッセンジャー名.", "settings.mailserver.authProtocol": "認証プロトコル", "settings.mailserver.host": "ホスト", "settings.mailserver.hostHelp": "SMTPサーバーのホストアドレス", "settings.mailserver.idleTimeout": "アイドルタイムアウト", "settings.mailserver.idleTimeoutHelp": "接続を閉じてプールから削除する前に、接続の新しいアクティビティの待機をする時間 (秒はs,分はm).", "settings.mailserver.maxConns": "最大接続数", "settings.mailserver.maxConnsHelp": "サーバーへの最大同時接続数。", "settings.mailserver.password": "パスワード", "settings.mailserver.passwordHelp": "エンターで変更", "settings.mailserver.port": "ポート", "settings.mailserver.portHelp": "SMTPサーバーポート", "settings.mailserver.skipTLS": "TLS検証をスキップ", "settings.mailserver.skipTLSHelp": "TLS証明のホスト名チェックをスキップ。", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "TLS/SSL暗号化。一般的にSTARTTLSが使用されます。", "settings.mailserver.username": "ユーザーネーム", "settings.mailserver.waitTimeout": "タイムアウト待機", "settings.mailserver.waitTimeoutHelp": "接続を閉じてプールから削除する前に、接続の新しいアクティビティの待機をする時間 (秒はs,分はm)", "settings.media.provider": "プロバイダー", "settings.media.s3.bucket": "バケット", "settings.media.s3.bucketPath": "バケットパス", "settings.media.s3.bucketPathHelp": "ファイルをアップロードするためのバケット内のパス。デフォルトは /", "settings.media.s3.bucketType": "バケットタイプ", "settings.media.s3.bucketTypePrivate": "プライベート", "settings.media.s3.bucketTypePublic": "パブリック", "settings.media.s3.key": "AWSアクセスキー", "settings.media.s3.publicURL": "カスタム公開URL (任意)", "settings.media.s3.publicURLHelp": "画像リンクにデフォルトのS3バックエンドURLを使用する代わりに、S3ドメインをカスタムする。", "settings.media.s3.region": "地域", "settings.media.s3.secret": "AWS シークレットアクセス", "settings.media.s3.uploadExpiry": "アップロードの有効期限", "settings.media.s3.uploadExpiryHelp": "(任意) 生成されたプリサインURLのTTLを（秒で）特定。プライベートバケットのみ適用可能。 (秒はs, 分はm, 時間はh, 日はd).", "settings.media.s3.url": "S3バックエンドURL", "settings.media.s3.urlHelp": "MinioのようなS3互換のカスタムバックエンドを使用する場合のみ変更。", "settings.media.title": "メディアアップロード", "settings.media.upload.path": "パスアップロード", "settings.media.upload.pathHelp": "メディアをアップロードするディレクトリへのパス", "settings.media.upload.uri": "URIアップロード", "settings.media.upload.uriHelp": "外部から閲覧可能なURIのアップロード。 upload_pathにアップロードされたメディアは{root_url}の下で一般に公開されます。例： https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "最大接続数", "settings.messengers.maxConnsHelp": "サーバーへの最大同時接続数.", "settings.messengers.messageSaved": "設定が保存されました。アプリをリロードしています...", "settings.messengers.name": "メッセンジャー", "settings.messengers.nameHelp": "例: my-sms. アルファニューメリック / ダッシュ.", "settings.messengers.password": "パスワード", "settings.messengers.retries": "再試行", "settings.messengers.retriesHelp": "メッセージ失敗時の再試行回数。", "settings.messengers.skipTLSHelp": "TLS証明のホストネームチェックをスキップ。", "settings.messengers.timeout": "アイドルタイムアウト", "settings.messengers.timeoutHelp": "接続を閉じてプールから削除する前に、接続の新しいアクティビティの待機をする時間 (秒はs,分はm)", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "ポストバックサーバーのルートURL", "settings.messengers.username": "ユーザーネーム", "settings.needsRestart": "設定が変更されました。実行中の全てのキャンペーンを停止し、アプリをリスタートさせてください。", "settings.performance.batchSize": "バッチサイズ", "settings.performance.batchSizeHelp": "一回のイテレーションでデータベースから取得する加入者の数。各イテレーションではデータベースから加入者を取り出し、メッセージを送信した後、次のバッチを取り出すためのイテレーションに進みます。理想として達成可能な最大スループット (並行性 * メッセージ_レート)よりも高くなければなりません.", "settings.performance.concurrency": "並行性", "settings.performance.concurrencyHelp": "同時にメッセージを送信しようとする並行ワーカー（スレッド）の最大数。", "settings.performance.maxErrThreshold": "最大エラーしきい値", "settings.performance.maxErrThresholdHelp": "実行中のキャンペーンが手動で調査・介入のために停止される前に許容すべきエラーの数 (例: メール時のSMTPタイムアウト) 0に設定すると停止されません。", "settings.performance.messageRate": "通信速度", "settings.performance.messageRateHelp": "1秒間にワーカー一1人当たりが発信するメッセージの最大数。 並行性 = 10 で 通信_速度 = 10の場合, 10x10=100 までのメッセージが毎秒押し出されます。これは並行性とともに、ターゲットメッセージサーバーの速度制限があれば、1秒あたりのメッセージがそれを超えないように調整されるべきです。", "settings.performance.name": "パフォーマンス", "settings.performance.slidingWindow": "スライディングウィンドウの制限を有効にする。", "settings.performance.slidingWindowDuration": "継続時間", "settings.performance.slidingWindowDurationHelp": "スライディングウィンドウの継続時間 (分はm, 時間はh).", "settings.performance.slidingWindowHelp": "一定期間内に送信されるメッセージの総数を制限する。この制限に達した場合、タイムウィンドウがクリアされるまでメッセージの送信は保留されます。", "settings.performance.slidingWindowRate": "メッセージ最大数", "settings.performance.slidingWindowRateHelp": "ウィンドウ持続時間内に送信するメッセージの最大数", "settings.privacy.allowBlocklist": "ブロックリストを許可する", "settings.privacy.allowBlocklistHelp": "加入者自身が全てのメーリングリストの登録を解除し、ブロックリストに追加することを許可しますか？", "settings.privacy.allowExport": "エクスポートを許可する", "settings.privacy.allowExportHelp": "加入者が自分自身について収集されたデータをエクスポートすることを許可しますか？", "settings.privacy.allowPrefs": "設定変更許可をする", "settings.privacy.allowPrefsHelp": "加入者に個人設定変更（名前やサブスクリプション状態）を許可する。", "settings.privacy.allowWipe": "ワイプを許可する", "settings.privacy.allowWipeHelp": "加入者サブスクリプション含むすべてのデータを含めて、データベースから自身を削除することを許可する。キャンペーンビューとリンククリックも削除されるが、統計と分析に影響が出ないよう、ビューとクリックカウントは残る (加入者を持たない状態)。", "settings.privacy.domainBlocklist": "ドメインブロックリスト", "settings.privacy.domainBlocklistHelp": "これらのドメインを持つメールアドレスは加入することができません。各行に一つドメインを入れてください。例: somesite.com", "settings.privacy.individualSubTracking": "加入者個別追跡", "settings.privacy.individualSubTrackingHelp": "加入者レベルのキャンペーンビューとクリックを追跡。無効にした場合、個々の加入者にリンクされることなく、ビューとクリックの追跡が継続されます。", "settings.privacy.listUnsubHeader": "`リスト-登録解除` ヘッダー", "settings.privacy.listUnsubHeaderHelp": "メールクライアントがワンクリックで登録解除をできるように登録解除用のヘッダーを含める。", "settings.privacy.name": "プライバシー", "settings.restart": "再起動", "settings.smtp.customHeaders": "カスタムヘッダー", "settings.smtp.customHeadersHelp": "このサーバーから送信する全てのメッセージに含まれる任意のメールヘッダーの配列。 例: [{\"X-カスタム\": \"バリュー\"}, {\"X-カスタム2\": \"バリュー\"}]", "settings.smtp.enabled": "有効", "settings.smtp.heloHost": "HELO ホストネーム", "settings.smtp.heloHostHelp": "任意. ホストネームにFQDNを求めるSMTPサーバーがあります。デフォルトで, HELLOsは`ローカルホスト`と付随します。カスタムホストネームが必要な場合は設定してください。", "settings.smtp.name": "SMTP", "settings.smtp.retries": "再トライ", "settings.smtp.retriesHelp": "メッセージ送信失敗時の再試行数", "settings.smtp.sendTest": "メール送信", "settings.smtp.setCustomHeaders": "カスタムヘッダー設定", "settings.smtp.testConnection": "接続テスト", "settings.smtp.testEnterEmail": "テストためのパスワード入力", "settings.smtp.toEmail": "メール宛", "settings.title": "設定", "settings.updateAvailable": "新しい {version} の更新が可能です。", "subscribers.advancedQuery": "アドバンスド", "subscribers.advancedQueryHelp": "加入者属性を問い合わせる部分的なSQL式", "subscribers.attribs": "属性", "subscribers.attribsHelp": "属性はJSONマップとして定義されます。例えば:", "subscribers.blocklistedHelp": "ブロックリストされた加入者は二度とメールを受け取りません。", "subscribers.confirmBlocklist": "加入者を {num}ブロックリストしますか ?", "subscribers.confirmDelete": "加入者を{num}削除しますか？", "subscribers.confirmExport": "加入者を{num}エクスポートしますか？", "subscribers.domainBlocklisted": "このメールのドメインはブロックリスト対象です。", "subscribers.downloadData": "データのダウンロード", "subscribers.email": "メール", "subscribers.emailExists": "このメールはすでに登録されています.", "subscribers.errorBlocklisting": "加入者ブロックリストエラー: {error}", "subscribers.errorNoIDs": "与えられたIDがありません。", "subscribers.errorNoListsGiven": "与えられたリストがありません。", "subscribers.errorPreparingQuery": "加入者の問い合わせ準備エラー: {error}", "subscribers.errorSendingOptin": "オプトインメール送信エラー。", "subscribers.export": "エクスポート", "subscribers.invalidAction": "無効なアクション.", "subscribers.invalidEmail": "無効なメール.", "subscribers.invalidJSON": "属性に無効なJSON。", "subscribers.invalidName": "無効な名前.", "subscribers.listChangeApplied": "リストの変更が適用されました。", "subscribers.lists": "リスト", "subscribers.listsHelp": "加入者が自ら解除したリストは削除できません。", "subscribers.listsPlaceholder": "登録するリスト。", "subscribers.manageLists": "リストを管理する", "subscribers.markUnsubscribed": "登録解除を設定する。", "subscribers.newSubscriber": "新加入者", "subscribers.numSelected": "選択された加入者{num}", "subscribers.optinSubject": "サブスクリプション確認", "subscribers.preconfirm": "サブスクリプションの事前確認", "subscribers.preconfirmHelp": "オプトインメールを送らず全てのリストサブスクリプションを'加入済み'とする.", "subscribers.query": "問い合わせ", "subscribers.queryPlaceholder": "メール又は名前", "subscribers.reset": "リセット", "subscribers.selectAll": "全て選択 {num}", "subscribers.sendOptinConfirm": "オプトイン確認を送信", "subscribers.sentOptinConfirm": "オプトイン確認送信済み", "subscribers.status.blocklisted": "ブロックリスト対象", "subscribers.status.confirmed": "確認済み", "subscribers.status.enabled": "有効", "subscribers.status.subscribed": "加入済み", "subscribers.status.unconfirmed": "未確認", "subscribers.status.unsubscribed": "登録解除", "subscribers.subscribersDeleted": "加入者{num}が削除されました。", "templates.cantDeleteDefault": "デフォルトのテンプレートを削除できません", "templates.default": "デフォルト", "templates.dummyName": "ダミーキャンペーン", "templates.dummySubject": "ダミーキャンペーン件名", "templates.errorCompiling": "テンプレートコンパイルエラー: {error}", "templates.errorRendering": "レンダリングメッセージエラー: {error}", "templates.fieldInvalidName": "名前の長さが無効です.", "templates.makeDefault": "デフォルトで設定", "templates.newTemplate": "新しいテンプレート", "templates.placeholderHelp": "プレースホルダー{placeholder}はテンプレートに一度だけ表示される必要があります。", "templates.preview": "プレビュー", "templates.rawHTML": "HTML(生)", "templates.subject": "件名", "users.login": "ログイン", "users.logout": "ログアウト"}