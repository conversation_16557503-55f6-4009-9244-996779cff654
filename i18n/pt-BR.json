{"_.code": "pt-BR", "_.name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pt-BR)", "admin.errorMarshallingConfig": "<PERSON>rro ao ler as configura<PERSON><PERSON><PERSON>: {error}", "analytics.count": "Contagem", "analytics.fromDate": "De", "analytics.invalidDates": "Data `from` ou `to` inválidas.", "analytics.isUnique": "As contagens são únicas por assinante.", "analytics.links": "Links", "analytics.nonUnique": "As contagens não são únicas pois o rastreamento de assinantes está desligado.", "analytics.title": "Analytics", "analytics.toDate": "Para", "bounces.source": "Fonte", "bounces.unknownService": "Serviço desconhecido.", "bounces.view": "Ver bounces", "campaigns.addAltText": "Adicionar mensagem alternativa em texto simples", "campaigns.archive": "Arquivo", "campaigns.archiveEnable": "Publicar no arquivo publico", "campaigns.archiveHelp": "Publicar (executando, pausada, finalizada) a mensagem da campanha no arquivo publico.", "campaigns.archiveMeta": "<PERSON><PERSON><PERSON> da campanha", "campaigns.archiveMetaHelp": "Dados de assinante fictício para utilizar na mensagem publica incluindo nome, email e qualquer atributo opcional usado na mensagem ou template da campanha.", "campaigns.cantUpdate": "Não é possível atualizar uma campanha em execução ou finalizada.", "campaigns.clicks": "Cliques", "campaigns.confirmDelete": "Excluir {name}", "campaigns.confirmSchedule": "A campanha irá começar automaticamente na data e hora agendadas. Agendar agora?", "campaigns.confirmSwitchFormat": "O conteúdo pode perder a formatação. Continuar?", "campaigns.content": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.contentHelp": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "campaigns.continue": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.copyOf": "Có<PERSON> de {name}", "campaigns.customHeadersHelp": "Array de cabeçalhos personalizados para anexar nas mensagens. eg: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Data e hora", "campaigns.ended": "Finalizada", "campaigns.errorSendTest": "Erro ao enviar o teste: {error}", "campaigns.fieldInvalidBody": "Erro ao compilar corpo da campanha: {error}", "campaigns.fieldInvalidFromEmail": "`from_email` inv<PERSON>lido.", "campaigns.fieldInvalidListIDs": "Lista de IDs inválida.", "campaigns.fieldInvalidMessenger": "<PERSON><PERSON><PERSON><PERSON> {name} desconhecido.", "campaigns.fieldInvalidName": "Quantidade de caracteres inválida para o nome.", "campaigns.fieldInvalidSendAt": "A data agendada deve ser no futuro.", "campaigns.fieldInvalidSubject": "Quantidade de caracteres inválida para o assunto.", "campaigns.formatHTML": "Formatar HTML", "campaigns.fromAddress": "Endereço do remetente", "campaigns.fromAddressPlaceholder": "<PERSON><PERSON> <<EMAIL>>", "campaigns.invalid": "<PERSON><PERSON><PERSON>", "campaigns.invalidCustomHeaders": "Cabeçalhos personalizados inválidos: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "A campanha precisa de uma data para ser programada.", "campaigns.newCampaign": "Nova campanha", "campaigns.noKnownSubsToTest": "Nenhum assinante conhecido para testar.", "campaigns.noOptinLists": "Nenhuma lista opt-in encontrada para criar campanha.", "campaigns.noSubs": "Não há assinantes nas listas selecionadas para criar a campanha.", "campaigns.noSubsToTest": "<PERSON>ão há nenhum assinante pra enviar.", "campaigns.notFound": "Campanha não encontrada.", "campaigns.onlyActiveCancel": "Apenas campanhas ativas podem ser canceladas.", "campaigns.onlyActivePause": "Apenas campanhas ativas podem ser pausadas.", "campaigns.onlyDraftAsScheduled": "Apenas campanhas em rascunho podem ser agendadas.", "campaigns.onlyPausedDraft": "Apenas campanhas pausadas e em rascunhos podem ser iniciadas.", "campaigns.onlyScheduledAsDraft": "<PERSON><PERSON>as campanhas <PERSON> podem ser salvas como rascunhos.", "campaigns.pause": "Pausar", "campaigns.plainText": "Texto simples", "campaigns.preview": "Pré-visualizar", "campaigns.progress": "Progresso", "campaigns.queryPlaceholder": "Nome ou assunto", "campaigns.rateMinuteShort": "min", "campaigns.rawHTML": "Código HTML", "campaigns.removeAltText": "Remover mensagem alternativa em texto simples", "campaigns.richText": "Texto com formatação", "campaigns.schedule": "<PERSON><PERSON><PERSON> camp<PERSON>", "campaigns.scheduled": "Agendada", "campaigns.send": "Enviar", "campaigns.sendLater": "Enviar mais tarde", "campaigns.sendTest": "Enviar mensagem de teste", "campaigns.sendTestHelp": "Pressione a tecla enter depois de digitar um endereço para adicionar vários destinatários. Os endereços devem pertencer a membros existentes.", "campaigns.sendToLists": "Listas para enviar para", "campaigns.sent": "Enviada", "campaigns.start": "In<PERSON><PERSON>", "campaigns.started": "<PERSON><PERSON><PERSON> \"{name}\" iniciada", "campaigns.startedAt": "Iniciada", "campaigns.stats": "Estatísticas", "campaigns.status.cancelled": "Cancelada", "campaigns.status.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.status.finished": "Finalizada", "campaigns.status.paused": "Pausada", "campaigns.status.running": "Executando", "campaigns.status.scheduled": "Agendado", "campaigns.statusChanged": "O status da campanha \"{name}\" é {status}", "campaigns.subject": "<PERSON><PERSON><PERSON>", "campaigns.testEmails": "E-mails", "campaigns.testSent": "Mensagem de teste enviada", "campaigns.timestamps": "Data e hora", "campaigns.trackLink": "Link de rastreamento", "campaigns.views": "Visualizações", "dashboard.campaignViews": "Visualizações da campanha", "dashboard.linkClicks": "Links clicados", "dashboard.messagesSent": "Mensagens enviadas", "dashboard.orphanSubs": "Órfãos", "email.data.info": "Uma cópia de todos os dados associados a você está anexado em um arquivo JSON. Ele pode ser ler o conteúdo em um editor de texto.", "email.data.title": "Seus dados", "email.optin.confirmSub": "Confirmar a assinatura", "email.optin.confirmSubHelp": "Confirme sua assinatura clicando no botão abaixo.", "email.optin.confirmSubInfo": "Você foi adicionado às seguintes listas:", "email.optin.confirmSubTitle": "Confirmar a assinatura", "email.optin.confirmSubWelcome": "O<PERSON><PERSON>", "email.optin.privateList": "Lista privada", "email.status.campaignReason": "Motivo", "email.status.campaignSent": "Enviada", "email.status.campaignUpdateTitle": "Atual<PERSON>r a campanha", "email.status.importFile": "Arquivo", "email.status.importRecords": "Registros", "email.status.importTitle": "Importar atualização", "email.status.status": "Status", "email.unsub": "Cancelar assinatura", "email.unsubHelp": "Não quer mais receber estes e-mails?", "email.viewInBrowser": "Ver no Navegador", "forms.formHTML": "Formulário HTML", "forms.formHTMLHelp": "Use este HTML para inserir um formulário de inscrição em uma página externa. O formulário deve ter o campo de e-mail e um ou mais campos `l` (lista UUID). O campo nome é opcional.", "forms.noPublicLists": "Não há nenhuma lista pública para gerar um formulário.", "forms.publicLists": "Listas públicas", "forms.publicSubPage": "Página pública de assinatura", "forms.selectHelp": "Selecione listas para adicionar ao formulário.", "forms.title": "Formulários", "globals.buttons.add": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.addNew": "Adicionar novo", "globals.buttons.back": "Voltar", "globals.buttons.cancel": "<PERSON><PERSON><PERSON>", "globals.buttons.clone": "Clonar", "globals.buttons.close": "<PERSON><PERSON><PERSON>", "globals.buttons.continue": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.delete": "Excluir", "globals.buttons.deleteAll": "Apagar tudo", "globals.buttons.edit": "<PERSON><PERSON>", "globals.buttons.enabled": "Habilitado", "globals.buttons.insert": "Inserir", "globals.buttons.learnMore": "<PERSON><PERSON> mais", "globals.buttons.more": "<PERSON><PERSON>", "globals.buttons.new": "Novo", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Excluir", "globals.buttons.save": "<PERSON><PERSON>", "globals.buttons.saveChanges": "<PERSON><PERSON>", "globals.days.0": "Dom", "globals.days.1": "Dom", "globals.days.2": "Seg", "globals.days.3": "<PERSON><PERSON>", "globals.days.4": "<PERSON>ua", "globals.days.5": "<PERSON>ui", "globals.days.6": "Sex", "globals.days.7": "<PERSON><PERSON><PERSON>", "globals.fields.createdAt": "<PERSON><PERSON><PERSON>", "globals.fields.description": "Descrição", "globals.fields.id": "ID", "globals.fields.name": "Nome", "globals.fields.status": "Status", "globals.fields.type": "Tipo", "globals.fields.updatedAt": "Atualizado", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Tem certeza?", "globals.messages.confirmDiscard": "Descartar alterações?", "globals.messages.created": "\"{name}\" criado", "globals.messages.deleted": "\"{name}\" exclu<PERSON>do", "globals.messages.deletedCount": "{name} ({num}) deletado", "globals.messages.done": "<PERSON><PERSON>", "globals.messages.emptyState": "Nada por aqui", "globals.messages.errorCreating": "Erro ao criar {name}: {error}", "globals.messages.errorDeleting": "Erro ao excluir {name}: {error}", "globals.messages.errorFetching": "Erro ao obter {name}: {error}", "globals.messages.errorInvalidIDs": "Um ou mais IDs inválidos: {error}", "globals.messages.errorUUID": "Erro ao gerar UUID: {error}", "globals.messages.errorUpdating": "Erro ao atualizar {name}: {error}", "globals.messages.internalError": "Erro no servidor", "globals.messages.invalidData": "<PERSON><PERSON>", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "ID inválido", "globals.messages.invalidUUID": "UUID inválido", "globals.messages.missingFields": "Campos ausente(s): {name}", "globals.messages.notFound": "{name} não encontrado", "globals.messages.passwordChange": "Digite um valor para alterar", "globals.messages.updated": "\"{name}\"atualizado", "globals.months.1": "Jan", "globals.months.10": "Out", "globals.months.11": "Nov", "globals.months.12": "<PERSON>z", "globals.months.2": "<PERSON>v", "globals.months.3": "Mar", "globals.months.4": "Abr", "globals.months.5": "<PERSON>", "globals.months.6": "Jun", "globals.months.7": "Jul", "globals.months.8": "Ago", "globals.months.9": "Set", "globals.states.off": "Off", "globals.terms.all": "<PERSON><PERSON>", "globals.terms.analytics": "Analytics", "globals.terms.bounce": "Bounce | Bounces", "globals.terms.bounces": "<PERSON><PERSON><PERSON>", "globals.terms.campaign": "Campanha | Campanhas", "globals.terms.campaigns": "<PERSON><PERSON><PERSON>", "globals.terms.dashboard": "<PERSON><PERSON>", "globals.terms.day": "Dia | Dias", "globals.terms.hour": "Hora | Horas", "globals.terms.list": "Lista | Listas", "globals.terms.lists": "Listas", "globals.terms.media": "Mídia | Mídias", "globals.terms.messenger": "Mensageiro | Mensageiros", "globals.terms.messengers": "Mensageiros", "globals.terms.minute": "Minuto | Minutos", "globals.terms.month": "Mês | Meses", "globals.terms.second": "Segundo | Segundos", "globals.terms.settings": "Configurações", "globals.terms.subscriber": "Assinante | Assinantes", "globals.terms.subscribers": "Assin<PERSON><PERSON>", "globals.terms.subscriptions": "Assinatura | Assinaturas", "globals.terms.tag": "Tag | Tags", "globals.terms.tags": "Tags", "globals.terms.template": "Modelo | Modelos", "globals.terms.templates": "Modelos", "globals.terms.tx": "Transacional | Transacionais", "globals.terms.year": "Ano | Anos", "import.alreadyRunning": "Uma importação já está em execução. Aguarde até que termine ou pare-a antes de tentar novamente.", "import.blocklist": "Lista de bloqueio", "import.csvDelim": "Delimitador CSV", "import.csvDelimHelp": "Delimitador padrão é vírgula.", "import.csvExample": "Exemplo de CSV bruto", "import.csvFile": "Arquivo CSV ou ZIP", "import.csvFileHelp": "Clique ou arraste um arquivo CSV ou ZIP aqui", "import.errorCopyingFile": "Erro ao copiar arquivo: {error}", "import.errorProcessingZIP": "Erro ao processar o arquivo ZIP: {error}", "import.errorStarting": "Erro ao iniciar importação: {error}", "import.importDone": "Finalizada", "import.importStarted": "Importação iniciada", "import.instructions": "Instruções", "import.instructionsHelp": "Envie um arquivo CSV ou um arquivo ZIP contendo um único arquivo CSV para a importação de assinantes lote. O arquivo CSV deve ter os seguintes cabeçalhos com os nomes exatos das colunas. Os atributos (opcional) devem ser uma string JSON válida com aspas duplas.", "import.invalidDelim": "O delimitador deve ser um único caractere.", "import.invalidFile": "Arquivo inválido: {error}", "import.invalidMode": "<PERSON><PERSON>", "import.invalidParams": "Parâmetros inválidos: {error}", "import.invalidSubStatus": "Status de assinatura inválido", "import.listSubHelp": "Listas para inscrever.", "import.mode": "Modo", "import.overwrite": "Sobrescrever?", "import.overwriteHelp": "Sobrescrever nome e atributos de inscritos existentes?", "import.recordsCount": "{num} / {total} registros", "import.stopImport": "Parar importação", "import.subscribe": "Inscrever", "import.title": "Importar inscritos", "import.upload": "Enviar arquivo", "lists.confirmDelete": "Você tem certeza? Isso não exclui inscritos.", "lists.confirmSub": "Confirmar assinatura(s) para {name}", "lists.invalidName": "Nome inválido", "lists.newList": "Nova lista", "lists.optin": "Confirmação da inscrição", "lists.optinHelp": "A inscrição com confirmação envia um e-mail para o inscrito pedindo que ele confirme a inscrição. Nas listas com inscrição com confirmação, as campanhas são enviadas apenas para inscritos que confirmaram a inscrição.", "lists.optinTo": "Inscrição com confirmação para {name}", "lists.optins.double": "Inscrição com confirmação", "lists.optins.single": "Inscrição simples", "lists.sendCampaign": "<PERSON><PERSON><PERSON> campanha", "lists.sendOptinCampaign": "Enviada campanha de confirmação de inscrição", "lists.type": "Tipo", "lists.typeHelp": "Listas públicas estão abertas ao mundo para se inscrever e seus nomes podem aparecer em páginas públicas, como na página de gerenciamento de inscrições.", "lists.types.private": "Privada", "lists.types.public": "Pública", "logs.title": "Logs", "maintenance.help": "Algumas ações podem levar um tempo a depender da quantidade de dados.", "maintenance.maintenance.unconfirmedOptins": "Assinaturas opt-in não confirmadas", "maintenance.olderThan": "<PERSON><PERSON> anti<PERSON> que", "maintenance.title": "Manutenção", "maintenance.unconfirmedSubs": "Assinaturas não confirmadas mais antigas que {name} dias.", "media.errorReadingFile": "Erro ao ler arquivo: {error}", "media.errorResizing": "Erro ao redimensionar imagem: {error}", "media.errorSavingThumbnail": "Erro ao salvar miniatura: {error}", "media.errorUploading": "Erro ao enviar o arquivo: {error}", "media.invalidFile": "Arquivo inválido: {error}", "media.title": "Mí<PERSON>", "media.unsupportedFileType": "Tipo de arquivo não suportado ({type})", "media.upload": "Enviar arquivo", "media.uploadHelp": "Clique ou arraste uma ou mais imagens aqui", "media.uploadImage": "Enviar Imagem", "menu.allCampaigns": "<PERSON><PERSON> as camp<PERSON><PERSON>", "menu.allLists": "<PERSON><PERSON> as listas", "menu.allSubscribers": "Todos os inscritos", "menu.dashboard": "<PERSON><PERSON>", "menu.forms": "Formulários", "menu.import": "Importação", "menu.logs": "Logs", "menu.maintenance": "Manutenção", "menu.media": "Mí<PERSON>", "menu.newCampaign": "Criar nova", "menu.settings": "Configurações", "public.archiveEmpty": "Sem mensagens no arquivo ainda.", "public.archiveTitle": "Arquivo da lista de emails", "public.blocklisted": "Inscrição cancelada permanentemente.", "public.campaignNotFound": "A mensagem do e-mail não foi encontrada.", "public.confirmOptinSubTitle": "Confirmar a assinatura", "public.confirmSub": "Confirmar a assinatura", "public.confirmSubInfo": "Você foi adicionado às seguintes listas:", "public.confirmSubTitle": "Confirmar", "public.dataRemoved": "Suas assinaturas e todos os dados associados foram removidos.", "public.dataRemovedTitle": "Dados removidos", "public.dataSent": "Seus dados foram enviados em anexo para seu e-mail.", "public.dataSentTitle": "Dados enviados para seu e-mail", "public.errorFetchingCampaign": "Erro ao obter a mensagem do e-mail.", "public.errorFetchingEmail": "Mensagem do e-mail não encontrada", "public.errorFetchingLists": "Erro ao obter as listas. Por favor, tente novamente.", "public.errorProcessingRequest": "Erro ao processar a solicitação. Por favor, tente novamente.", "public.errorTitle": "Erro", "public.invalidFeature": "Este recurso não está disponível.", "public.invalidLink": "<PERSON> in<PERSON>", "public.managePrefs": "Gerenciar preferências", "public.managePrefsUnsub": "<PERSON><PERSON><PERSON> as listas para cancelar a inscrição nelas.", "public.noListsAvailable": "Não há listas disponíveis para se inscrever.", "public.noListsSelected": "Não foram selecionadas listas válidas para inscrever.", "public.noSubInfo": "Não há nenhuma inscrição para confirmar.", "public.noSubTitle": "Sem inscrições", "public.notFoundTitle": "Não Encontrado", "public.prefsSaved": "Suas preferências foram salvas.", "public.privacyConfirmWipe": "Você tem certeza que deseja excluir todos os seus dados de assinatura permanentemente?", "public.privacyExport": "Exportar seus dados", "public.privacyExportHelp": "Uma cópia de seus dados será enviado por e-mail para você.", "public.privacyTitle": "Privacidade e dados", "public.privacyWipe": "<PERSON><PERSON> seus dados", "public.privacyWipeHelp": "Excluir todas as suas assinaturas e dados relacionados do banco de dados permanentemente.", "public.sub": "Inscrever-se", "public.subConfirmed": "Inscrito com sucesso.", "public.subConfirmedTitle": "<PERSON><PERSON><PERSON><PERSON>", "public.subName": "Nome (opcional)", "public.subNotFound": "Inscrição não encontrada.", "public.subOptinPending": "Um e-mail foi enviado a você para confirmar sua(s) inscrição(ões).", "public.subPrivateList": "Lista privada", "public.subTitle": "Inscrever-se", "public.unsub": "Cancelar a inscrição", "public.unsubFull": "Também cancelar a inscrição de todos os e-mails futuros.", "public.unsubHelp": "Deseja cancelar a inscrição desta lista de e-mail?", "public.unsubTitle": "Cancelar inscrição", "public.unsubbedInfo": "Você cancelou a inscrição com sucesso.", "public.unsubbedTitle": "Inscrição cancelada", "public.unsubscribeTitle": "Cancelar inscrição na lista de e-mails", "settings.appearance.adminHelp": "CSS customizado para aplicar na admin UI.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "CSS customizado", "settings.appearance.customJS": "JavaScript customizado", "settings.appearance.name": "Aparência", "settings.appearance.publicHelp": "CSS e JavaScript customizados para aplicar nas páginas públicas.", "settings.appearance.publicName": "Publico", "settings.bounces.action": "Ação", "settings.bounces.blocklist": "Blocklist", "settings.bounces.count": "Contagem Bo<PERSON>", "settings.bounces.countHelp": "Número de bounces por assinante", "settings.bounces.delete": "Deletar", "settings.bounces.enable": "Ativar processamento de bounce", "settings.bounces.enableMailbox": "Ativar caixa de email de bounce", "settings.bounces.enableSES": "Ativar SES", "settings.bounces.enableSendgrid": "Ativar SendGrid", "settings.bounces.enableWebhooks": "Ativar webhooks bounce", "settings.bounces.enabled": "<PERSON><PERSON>do", "settings.bounces.folder": "Pasta", "settings.bounces.folderHelp": "Noma da pasta IMAP para escanear. Ex: Inbox.", "settings.bounces.invalidScanInterval": "Intervalo de escaneamento de Bounce deve ser no mínimo 1 minuto.", "settings.bounces.name": "<PERSON><PERSON><PERSON>", "settings.bounces.scanInterval": "Intervalo de Escaneamento", "settings.bounces.scanIntervalHelp": "Intervalo no qual a caixa de emails de bounce deve ser escaneada por bounces (s para segundo, m para minuto).", "settings.bounces.sendgridKey": "Key SendGrid", "settings.bounces.type": "Tipo", "settings.bounces.username": "Nome de usuário", "settings.confirmRestart": "Certifique-se de que as campanhas em execução estão pausadas. Reiniciar?", "settings.duplicateMessengerName": "Nome duplicado do mensageiro: {name}", "settings.errorEncoding": "Erro ao codificar as configuraç<PERSON><PERSON>: {error}", "settings.errorNoSMTP": "Pelo menos um bloco SMTP deve estar habilitado", "settings.general.adminNotifEmails": "E-mails de notificação de administrador", "settings.general.adminNotifEmailsHelp": "Lista de e-mails separados por vírgula para os quais as notificações de administração, como atualizações de importação, conclusão da campanha, falha, etc. devem ser enviadas.", "settings.general.checkUpdates": "Verificar atualizações", "settings.general.checkUpdatesHelp": "Checar periodicamente por notificações e atualizações do app.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publicar campanhas nas quais o arquivamento está ativado no site público.", "settings.general.enablePublicSubPage": "Habilitar a página pública de inscrição", "settings.general.enablePublicSubPageHelp": "Habilitar a página pública de inscrição com todas as listas públicas para as pessoas se inscreverem.", "settings.general.faviconURL": "URL do Favicon", "settings.general.faviconURLHelp": "(Opcional) URL completo do favicon estático para ser visualizado pelo usuário, como a página de cancelamento de inscrição.", "settings.general.fromEmail": "E-mail `de` padrão", "settings.general.fromEmailHelp": "E-mail `de` padrão é usada nas mensagens de e-mails enviadas. <PERSON>so pode ser alterado por campanha.", "settings.general.language": "Idioma", "settings.general.logoURL": "URL do logotipo", "settings.general.logoURLHelp": "(Opcional) URL completo do logotipo estático para ser visualizado pelo usuário, como a página de cancelamento de inscrição.", "settings.general.name": "G<PERSON>", "settings.general.rootURL": "URL base", "settings.general.rootURLHelp": "URL público da instalação (sem barra final).", "settings.general.sendOptinConfirm": "Enviar confirmação opt-in", "settings.general.sendOptinConfirmHelp": "Quando novo assinante se cadastrar ou for adicionado pelo admin, enviar e-mail de confirmação opt-in.", "settings.general.siteName": "Nome do site", "settings.invalidMessengerName": "Nome de mensageiro inválido.", "settings.mailserver.authProtocol": "Protocolo Autenticação", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "Endereço do servidor SMTP.", "settings.mailserver.idleTimeout": "Tempo limite ocioso", "settings.mailserver.idleTimeoutHelp": "Tempo para esperar por uma nova atividade em uma conexão antes de fechá-la e removê-la do pool (s parar segundo, m para minuto).", "settings.mailserver.maxConns": "Máx. Conexões", "settings.mailserver.maxConnsHelp": "Número máximo de conexões simultâneas ao servidor SMTP.", "settings.mailserver.password": "<PERSON><PERSON>", "settings.mailserver.passwordHelp": "Digite para alterar", "settings.mailserver.port": "Porta", "settings.mailserver.portHelp": "Porta do servidor SMTP.", "settings.mailserver.skipTLS": "Pular verificação de TLS", "settings.mailserver.skipTLSHelp": "Pular verificação de hostname sobre o certificado TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Habilitar STARTTLS.", "settings.mailserver.username": "<PERSON><PERSON><PERSON><PERSON>", "settings.mailserver.waitTimeout": "Tempo limite de espera", "settings.mailserver.waitTimeoutHelp": "Tempo para esperar por uma nova atividade em uma conexão antes de fechá-la e removê-la do pool (s parar segundo, m para minuto).", "settings.media.provider": "<PERSON><PERSON><PERSON>", "settings.media.s3.bucket": "Bucket", "settings.media.s3.bucketPath": "<PERSON>in<PERSON> do <PERSON>", "settings.media.s3.bucketPathHelp": "Caminho dentro do bucket para enviar os arquivos. O padrão é /", "settings.media.s3.bucketType": "Tipo de <PERSON>", "settings.media.s3.bucketTypePrivate": "Privado", "settings.media.s3.bucketTypePublic": "Público", "settings.media.s3.key": "Chave de acesso AWS", "settings.media.s3.publicURL": "URL público customizado (opcional)", "settings.media.s3.publicURLHelp": "Domínio S3 customizado para usar nos links das imagens ao invés do padrão de URL do S3.", "settings.media.s3.region": "Região", "settings.media.s3.secret": "<PERSON><PERSON><PERSON> de acesso AWS", "settings.media.s3.uploadExpiry": "Expiração do arquivo enviado", "settings.media.s3.uploadExpiryHelp": "(Opcional) Especificar TTL (em segundos) para a URL pré-assinada gerada. Apenas aplicável para buckets privados (s, m, h, d para segundos, minutos, horas e dias).", "settings.media.s3.url": "URL backend do S3", "settings.media.s3.urlHelp": "Altere apenas se usar um backnd customizado compatível com S3, como o Minio.", "settings.media.title": "<PERSON><PERSON><PERSON> mídia<PERSON>", "settings.media.upload.path": "<PERSON><PERSON><PERSON>", "settings.media.upload.pathHelp": "Caminho para o diretório onde a mídia será enviado.", "settings.media.upload.uri": "URI de envio", "settings.media.upload.uriHelp": "URI de envio que é visível ao mundo exterior. Todas as mídias enviadas para o upload_path será publicamente acessível em {root_url}, por exemplo, https://listmonk.exemplo.com.br/uploads.", "settings.messengers.maxConns": "Máx. conex<PERSON>", "settings.messengers.maxConnsHelp": "Máximo de conexões simultâneas para o servidor.", "settings.messengers.messageSaved": "Configurações salvas. Recarregando o aplicativo...", "settings.messengers.name": "Mensageiros", "settings.messengers.nameHelp": "ex: meu-sms. Alfanuméricos / traço.", "settings.messengers.password": "<PERSON><PERSON>", "settings.messengers.retries": "Tentativas", "settings.messengers.retriesHelp": "Número de tentativas quando uma mensagem falhar.", "settings.messengers.skipTLSHelp": "Pular verificação de hostname sobre o certificado TLS.", "settings.messengers.timeout": "Tempo de espera limite", "settings.messengers.timeoutHelp": "Tempo para esperar por uma nova atividade em uma conexão antes de fechá-la e removê-la do pool (s parar segundo, m para minuto).", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "URL base do servidor Postback.", "settings.messengers.username": "<PERSON><PERSON><PERSON><PERSON>", "settings.needsRestart": "Configurações alteradas. Pa<PERSON> todas as campanhas em execução e reiniciar o aplicativo", "settings.performance.batchSize": "Tamanho do lote", "settings.performance.batchSizeHelp": "O número de inscritos para puxar do banco de dados em uma única iteração. Cada iteração puxa assinantes da base de dados, envia mensagens para eles, e então passa para a próxima iteração para puxar o próximo lote. O ideal é que isso seja mais alto do que o máximo possível de transferência (concorrência * taxa de mensagem).", "settings.performance.concurrency": "Concorrência", "settings.performance.concurrencyHelp": "Máximo de trabalhador simultâneo (threads) que tentará enviar mensagens simultaneamente.", "settings.performance.maxErrThreshold": "Limite máxi<PERSON> de er<PERSON>", "settings.performance.maxErrThresholdHelp": "O número de erros (por exemplo: tempo limite SMTP ao enviar e-mail) uma campanha em curso deve tolerar antes de ser pausada para investigação manual ou intervenção. Marque 0 para nunca pausar.", "settings.performance.messageRate": "Taxa de mensagens", "settings.performance.messageRateHelp": "Número máximo de mensagens a serem enviadas por segundo por trabalhador em um segundo. Se a concorrência = 10 e taxa de mensagem = 10, então até 10x10=100 mensagens podem ser enviadas a cada segundo. Isto, juntamente com a concorrência, deve ser ajustado para manter as mensagens saindo da rede por segundo abaixo dos limites de taxa dos servidores de mensagens de destino, se houver.", "settings.performance.name": "Performance", "settings.performance.slidingWindow": "Habilitar limite da janela deslizante", "settings.performance.slidingWindowDuration": "Duração", "settings.performance.slidingWindowDurationHelp": "Duração do período da janela deslizante (m para minuto, h para hora).", "settings.performance.slidingWindowHelp": "Limitar o número total de mensagens enviadas em determinado período. Ao atingir este limite, as mensagens são impedidas de ser enviadas até ao fim da janela temporária.", "settings.performance.slidingWindowRate": "<PERSON><PERSON> men<PERSON>", "settings.performance.slidingWindowRateHelp": "Número máximo de mensagens a serem enviadas dentro da duração da janela.", "settings.privacy.allowBlocklist": "Permitir lista de bloqueio", "settings.privacy.allowBlocklistHelp": "Permitir que os inscritos cancelem a inscrição de todas as listas de e-mails e se marquem como bloqueados?", "settings.privacy.allowExport": "Permitir <PERSON>", "settings.privacy.allowExportHelp": "Permitir que os assinantes exportem os dados coletados neles?", "settings.privacy.allowPrefs": "<PERSON><PERSON><PERSON> preferências", "settings.privacy.allowPrefsHelp": "Permita que os assinantes alterem as preferências, como seus nomes e assinaturas de várias listas.", "settings.privacy.allowWipe": "<PERSON><PERSON><PERSON>", "settings.privacy.allowWipeHelp": "Permitir que os assinantes se excluam incluindo suas inscrições e todos os outros dados da base de dados. Visualizações da campanha e cliques de links também são removidos enquanto o total de visualizações e cliques permanecem (com nenhum inscrito associado a eles) para que as estatísticas e análises não sejam afetadas.", "settings.privacy.domainBlocklist": "Block<PERSON> <PERSON> do<PERSON>", "settings.privacy.domainBlocklistHelp": "Endereços de e-mail com estes domínios serão proibidos de se cadastrarem. Um domínio por linha, ex: somesite.com", "settings.privacy.individualSubTracking": "Rastreamento individual de inscrito", "settings.privacy.individualSubTrackingHelp": "Rastrear visualizações e cliques de cada inscrito. Quando desativado, o rastreio da visualizações e clique continuar sem estar associado a nenhuma inscrição.", "settings.privacy.listUnsubHeader": "Incluir cabeçalho `List-Unsubscribe`", "settings.privacy.listUnsubHeaderHelp": "Incluir cabeçalhos de desinscrição que permitem aos clientes de e-mail cancelem a inscrição em um único clique.", "settings.privacy.name": "Privacidade", "settings.restart": "Reiniciar", "settings.smtp.customHeaders": "Cabeçalhos personalizados", "settings.smtp.customHeadersHelp": "Array opcional de cabeçalhos de e-mail para incluir em todas as mensagens enviadas a partir deste servidor. por exemplo: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Habilitado", "settings.smtp.heloHost": "Nome do host HELO", "settings.smtp.heloHostHelp": "Opcional. Alguns servidores SMTP exigem um FQDN no nome do host. <PERSON><PERSON> <PERSON>, os HELLOs vão com 'localhost'. Defina isto se um nome de host personalizado deve ser usado.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Tentativas", "settings.smtp.retriesHelp": "Número de tentativas quando uma mensagem falhar.", "settings.smtp.sendTest": "Enviar e-mail", "settings.smtp.setCustomHeaders": "Definir cabeçalhos personalizados", "settings.smtp.testConnection": "<PERSON><PERSON>", "settings.smtp.testEnterEmail": "Digite a senha para testar", "settings.smtp.toEmail": "E-mail para", "settings.title": "Configurações", "settings.updateAvailable": "Atualização: a nova versão {version} já está disponível.", "subscribers.advancedQuery": "Avançado", "subscribers.advancedQueryHelp": "Expressão de SQL parcial para consultar atributos dos inscritos", "subscribers.attribs": "Atributos", "subscribers.attribsHelp": "Atributos são definidos como um mapa JSON, por exemplo:", "subscribers.blocklistedHelp": "Inscritos bloqueados nunca receberão quaisquer e-mails.", "subscribers.confirmBlocklist": "Bloquear {num} inscrito(s)?", "subscribers.confirmDelete": "Excluir {num} inscrito(s)?", "subscribers.confirmExport": "Exportar {num} inscrito(s)?", "subscribers.domainBlocklisted": "O domínio desse emails está na blocklist.", "subscribers.downloadData": "Baixar dados", "subscribers.email": "E-mail", "subscribers.emailExists": "E-mail já existe.", "subscribers.errorBlocklisting": "Erro ao bloquear inscritos: {error}", "subscribers.errorNoIDs": "Nenhum ID informado.", "subscribers.errorNoListsGiven": "Nenhuma lista informada.", "subscribers.errorPreparingQuery": "Erro ao preparar consulta de inscritos: {error}", "subscribers.errorSendingOptin": "Erro ao enviar e-mail de confirmação de inscrição.", "subscribers.export": "Exportar", "subscribers.invalidAction": "Ação inválida.", "subscribers.invalidEmail": "E-mail inválido.", "subscribers.invalidJSON": "JSON inválido nos atributos.", "subscribers.invalidName": "Nome inválido.", "subscribers.listChangeApplied": "Alterações na lista aplicadas.", "subscribers.lists": "Listas", "subscribers.listsHelp": "Listas das quais os inscritos cancelaram a inscrição por eles mesmos não podem ser removidos.", "subscribers.listsPlaceholder": "Listas para inscrever", "subscribers.manageLists": "Gerenciar listas", "subscribers.markUnsubscribed": "Marcar como inscrição cancelada", "subscribers.newSubscriber": "Novo inscrito", "subscribers.numSelected": "{num} inscrito(s) selecionado(s)", "subscribers.optinSubject": "Confirmar a inscrição", "subscribers.preconfirm": "Pré-confirmar assinaturas", "subscribers.preconfirmHelp": "Não enviar emails de confirmação opt-in e marcar toda a lista como 'subscribed'.", "subscribers.query": "Consulta", "subscribers.queryPlaceholder": "E-mail ou nome", "subscribers.reset": "Redefinir", "subscribers.selectAll": "Selecionar todos {num}", "subscribers.sendOptinConfirm": "Enviar confirmação opt-in", "subscribers.sentOptinConfirm": "Confirmação opt-in enviada", "subscribers.status.blocklisted": "Lista de bloqueados", "subscribers.status.confirmed": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.status.enabled": "Habilitado", "subscribers.status.subscribed": "Inscrito", "subscribers.status.unconfirmed": "<PERSON>ão confirmado", "subscribers.status.unsubscribed": "Inscrição cancelada", "subscribers.subscribersDeleted": "{num} inscrito(s) excluído(s)", "templates.cantDeleteDefault": "Não é possível excluir o modelo padrão", "templates.default": "Padrão", "templates.dummyName": "Campanha <PERSON>", "templates.dummySubject": "<PERSON><PERSON><PERSON> da campanha fictícia", "templates.errorCompiling": "Erro ao compilar modelo: {error}", "templates.errorRendering": "Erro ao renderizar mensagem: {error}", "templates.fieldInvalidName": "Comprimento inválido para o nome.", "templates.makeDefault": "Definir como padrão", "templates.newTemplate": "Novo modelo", "templates.placeholderHelp": "O palavra reservada {placeholder} deve aparecer exatamente uma vez no modelo.", "templates.preview": "Pré-visualizar", "templates.rawHTML": "Código HTML", "templates.subject": "<PERSON><PERSON><PERSON>", "users.login": "Entrar", "users.logout": "<PERSON><PERSON>"}