{"_.code": "ru", "_.name": "Русский (ru)", "admin.errorMarshallingConfig": "Ошибка преобразования конфига: {error}", "analytics.count": "Количество", "analytics.fromDate": "С", "analytics.invalidDates": "Неверно `from` или `to` даты.", "analytics.isUnique": "Счётчики уникальны для каждого подписчика.", "analytics.links": "Ссылки", "analytics.nonUnique": "Счётчики не уникальны, т.к. индивидуальное отслеживание подписчиков выключено.", "analytics.title": "Аналитика", "analytics.toDate": "По", "bounces.source": "Источник", "bounces.unknownService": "Неизвестная услуга.", "bounces.view": "Просмотр отскоков", "campaigns.addAltText": "Добавить альтернативное простое текстовое сообщение", "campaigns.archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.archiveEnable": "Опубликовать в общедоступном архиве", "campaigns.archiveHelp": "Опубликовать (запущено, на паузе, завершено) сообщение компании в общедоступном архиве.", "campaigns.archiveMeta": "Метаданные компании", "campaigns.archiveMetaHelp": "Данные фиктивных подписчиков для использования в публичном сообщении, включая имя, электронную почту и любые дополнительные атрибуты, используемые в сообщении или шаблоне кампании.", "campaigns.cantUpdate": "Не возможно обновить запущенную или завершённую компанию.", "campaigns.clicks": "Клики", "campaigns.confirmDelete": "Удалить {name}", "campaigns.confirmSchedule": "Эта компания будет автоматически запущена в запланированное время. Запланировать сейчас?", "campaigns.confirmSwitchFormat": "Содержимое может потерять форматирование. Продолжить?", "campaigns.content": "Содержимое", "campaigns.contentHelp": "Содержимое", "campaigns.continue": "Продолжить", "campaigns.copyOf": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "campaigns.customHeadersHelp": "Список дополнительных заголовков в исходящем письме, напр: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "campaigns.dateAndTime": "Дата и время", "campaigns.ended": "Окончено", "campaigns.errorSendTest": "Ошибка отправки теста: {error}", "campaigns.fieldInvalidBody": "Ошибка сборки тела компании: {error}", "campaigns.fieldInvalidFromEmail": "Неверный `from_email`.", "campaigns.fieldInvalidListIDs": "Неверные ID списков.", "campaigns.fieldInvalidMessenger": "Неизвестный мессенджер {name}.", "campaigns.fieldInvalidName": "Неверная длина имени.", "campaigns.fieldInvalidSendAt": "Запланированная дата должна быть позже текущей.", "campaigns.fieldInvalidSubject": "Неверная длина темы.", "campaigns.formatHTML": "Формат HTML", "campaigns.fromAddress": "Адрес отправителя", "campaigns.fromAddressPlaceholder": "Ваше имя <<EMAIL>>", "campaigns.invalid": "Неверная компания", "campaigns.invalidCustomHeaders": "Недопустимые пользовательские заголовки: {error}", "campaigns.markdown": "Разметка", "campaigns.needsSendAt": "Для планирования компании необходима дата.", "campaigns.newCampaign": "Новая компания", "campaigns.noKnownSubsToTest": "Для теста нет известных подписчиков.", "campaigns.noOptinLists": "Не найдено списков с подтверждением подписки для создания кампании .", "campaigns.noSubs": "В выбранных списках нет подписчиков для создания кампании.", "campaigns.noSubsToTest": "Нед подписциков для цели.", "campaigns.notFound": "Компания не найдена.", "campaigns.onlyActiveCancel": "Только активные компании могут быть отменены.", "campaigns.onlyActivePause": "Только активные компании могут быть приостановлены.", "campaigns.onlyDraftAsScheduled": "Можно запланировать только черновики кампаний.", "campaigns.onlyPausedDraft": "Можно запускать только приостановленные кампании и черновики.", "campaigns.onlyScheduledAsDraft": "Только запланированные кампании можно сохранить как черновики.", "campaigns.pause": "Приостановить", "campaigns.plainText": "Простой текст", "campaigns.preview": "Предпросмотр", "campaigns.progress": "Прогресс", "campaigns.queryPlaceholder": "Имя темы", "campaigns.rateMinuteShort": "мин", "campaigns.rawHTML": "Необработанный HTML", "campaigns.removeAltText": "Удалить альтернативное простое текстовое сообщение", "campaigns.richText": "Форматированный текст", "campaigns.schedule": "Запланировать компанию", "campaigns.scheduled": "Запланированные", "campaigns.send": "Отправить", "campaigns.sendLater": "Отправить позже", "campaigns.sendTest": "Отправить тестовое сообщение", "campaigns.sendTestHelp": "Нажмите Enter после ввода адреса, чтобы добавить нескольких получателей. Адреса должны принадлежать существующим подписчикам.", "campaigns.sendToLists": "Списки для отправки", "campaigns.sent": "Отправленные", "campaigns.start": "Запустить компанию", "campaigns.started": "\"{name}\" запущена", "campaigns.startedAt": "Запущенные", "campaigns.stats": "Статистика", "campaigns.status.cancelled": "Отменённые", "campaigns.status.draft": "Черновик", "campaigns.status.finished": "Завершена", "campaigns.status.paused": "Приостановлена", "campaigns.status.running": "Запущена", "campaigns.status.scheduled": "Запланирована", "campaigns.statusChanged": "\"{name}\" {status}", "campaigns.subject": "Тема", "campaigns.testEmails": "E-mails", "campaigns.testSent": "Тестовое сообщение отправлено", "campaigns.timestamps": "Метки времени", "campaigns.trackLink": "Ссылка на трек", "campaigns.views": "Просмотры", "dashboard.campaignViews": "Просмотров компании", "dashboard.linkClicks": "Кликов по ссылкам", "dashboard.messagesSent": "Отправлено сообщений", "dashboard.orphanSubs": "Подписчиков не в писках", "email.data.info": "Копия всех записанных на вас данных прилагается в виде файла в формате JSON. Его можно просмотреть в текстовом редакторе.", "email.data.title": "Ваши данные", "email.optin.confirmSub": "Подтвердить подписку", "email.optin.confirmSubHelp": "Подтвердите подписку нажатием кнопки ниже.", "email.optin.confirmSubInfo": "Вы были добавлены в следующие листы:", "email.optin.confirmSubTitle": "Подтверждение подписки", "email.optin.confirmSubWelcome": "Привет", "email.optin.privateList": "Приватный список", "email.status.campaignReason": "Причина", "email.status.campaignSent": "Отправлена", "email.status.campaignUpdateTitle": "Обновление компании", "email.status.importFile": "<PERSON>а<PERSON><PERSON>", "email.status.importRecords": "Записи", "email.status.importTitle": "Обновление импорта", "email.status.status": "Статус", "email.unsub": "Отписаться", "email.unsubHelp": "Не хотите получать эти письма?", "email.viewInBrowser": "Просмотреть в браузере", "forms.formHTML": "Форма HTML", "forms.formHTMLHelp": "Используйте следующий HTML-код, чтобы показать форму подписки на внешней веб-странице. Форма должна иметь поле электронной почты и одно или несколько полей `l` (список UUID). Поле имени необязательно.", "forms.noPublicLists": "Для генерации формы нет публичных списков.", "forms.publicLists": "Публичные списки", "forms.publicSubPage": "Публичная страница подписки", "forms.selectHelp": "Выберите списки для добаления в форму.", "forms.title": "Формы", "globals.buttons.add": "Добавить", "globals.buttons.addNew": "Добавить", "globals.buttons.back": "Вернуться", "globals.buttons.cancel": "Отменить", "globals.buttons.clone": "Клонировать", "globals.buttons.close": "Закрыть", "globals.buttons.continue": "Продолжить", "globals.buttons.delete": "Удалить", "globals.buttons.deleteAll": "Удалить все", "globals.buttons.edit": "Изменить", "globals.buttons.enabled": "Включено", "globals.buttons.insert": "Вставить", "globals.buttons.learnMore": "Подпробней", "globals.buttons.more": "<PERSON><PERSON><PERSON>", "globals.buttons.new": "Новое", "globals.buttons.ok": "ОК", "globals.buttons.remove": "Удалить", "globals.buttons.save": "Сохранить", "globals.buttons.saveChanges": "Сохранить изменения", "globals.days.0": "Вс", "globals.days.1": "Вс", "globals.days.2": "Пн", "globals.days.3": "Вт", "globals.days.4": "Ср", "globals.days.5": "Чт", "globals.days.6": "Пт", "globals.days.7": "Сб", "globals.fields.createdAt": "Создано", "globals.fields.description": "Описание", "globals.fields.id": "ID", "globals.fields.name": "Имя", "globals.fields.status": "Статус", "globals.fields.type": "Тип", "globals.fields.updatedAt": "Обновлено", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Уверены?", "globals.messages.confirmDiscard": "Отказаться от изменений?", "globals.messages.created": "\"{name}\" создано", "globals.messages.deleted": "\"{name}\" удалено", "globals.messages.deletedCount": "{name} ({num}) удалено", "globals.messages.done": "Готово", "globals.messages.emptyState": "Ничего нет", "globals.messages.errorCreating": "Ошибка создания {name}: {error}", "globals.messages.errorDeleting": "Ошибка удаления {name}: {error}", "globals.messages.errorFetching": "Ошибка получения {name}: {error}", "globals.messages.errorInvalidIDs": "Указан один или более неверных ID: {error}", "globals.messages.errorUUID": "Ошибка генерации UUID: {error}", "globals.messages.errorUpdating": "Ошибка обновления {name}: {error}", "globals.messages.internalError": "Внутренняя ошибка сервера", "globals.messages.invalidData": "Неверные данные", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Неверный ID", "globals.messages.invalidUUID": "Неверный UUID", "globals.messages.missingFields": "Отсутствующее поле (поля): {name}", "globals.messages.notFound": "{name} не найдено", "globals.messages.passwordChange": "Введите значение для изменения", "globals.messages.updated": "\"{name}\" обновлено", "globals.months.1": "<PERSON><PERSON><PERSON>", "globals.months.10": "Окт", "globals.months.11": "Ноя", "globals.months.12": "<PERSON><PERSON><PERSON>", "globals.months.2": "<PERSON><PERSON><PERSON>", "globals.months.3": "<PERSON><PERSON><PERSON>", "globals.months.4": "<PERSON><PERSON><PERSON>", "globals.months.5": "<PERSON><PERSON><PERSON>", "globals.months.6": "<PERSON><PERSON><PERSON>", "globals.months.7": "<PERSON><PERSON><PERSON>", "globals.months.8": "Авг", "globals.months.9": "Сен", "globals.states.off": "Выкл.", "globals.terms.all": "Все", "globals.terms.analytics": "Аналитика", "globals.terms.bounce": "Отскок | Отскоки", "globals.terms.bounces": "Отскоки", "globals.terms.campaign": "Компания | Компании", "globals.terms.campaigns": "Компании", "globals.terms.dashboard": "Панель", "globals.terms.day": "День | Дни", "globals.terms.hour": "Час | Час", "globals.terms.list": "Список | Списки", "globals.terms.lists": "Списки", "globals.terms.media": "Медиа | Медиа", "globals.terms.messenger": "Мессенджер | Мессенджеры", "globals.terms.messengers": "Мессенджеры", "globals.terms.minute": "Минута | Минуты", "globals.terms.month": "Месяц | Месяцы", "globals.terms.second": "Секунда | Секунды", "globals.terms.settings": "Параметры", "globals.terms.subscriber": "Подписчик | Подписчики", "globals.terms.subscribers": "Подписчики", "globals.terms.subscriptions": "Подписка | Подписки", "globals.terms.tag": "Тег | Теги", "globals.terms.tags": "Теги", "globals.terms.template": "Шаблон | Шаблоны", "globals.terms.templates": "Шаблоны", "globals.terms.tx": "Транзакционный | Транзакционный", "globals.terms.year": "Год | Годы", "import.alreadyRunning": "Импорт уже выполняется. Подождите, пока он закончит, или остановите его, прежде чем пытаться снова. ", "import.blocklist": "Список блокировки", "import.csvDelim": "Разделитель CSV", "import.csvDelimHelp": "Разделитель по умолчанию - запятая.", "import.csvExample": "Пример необработанного CSV", "import.csvFile": "Файл CSV или ZIP", "import.csvFileHelp": "Кликните или перетащите сюда файл CSV или ZIP", "import.errorCopyingFile": "Ошибка копирования файла: {error}", "import.errorProcessingZIP": "Ошибка обработки файла ZIP: {error}", "import.errorStarting": "Ошибка запуска импорта: {error}", "import.importDone": "Готово", "import.importStarted": "Импорт запущен", "import.instructions": "Инструкции", "import.instructionsHelp": "Загрузите CSV-файл или ZIP-файл с одним CSV-файлом для массового импорта подписчиков. Файл CSV должен иметь следующие заголовки с точными названиями столбцов. Атрибуты (необязательно) должны быть допустимой строкой JSON с двойными кавычками.", "import.invalidDelim": "Разделителем должен быть один символ.", "import.invalidFile": "Неверный файл: {error}", "import.invalidMode": "Неверный режим", "import.invalidParams": "Неверные параметры: {error}", "import.invalidSubStatus": "Неверный статус подписки", "import.listSubHelp": "Списки для подписки.", "import.mode": "Режим", "import.overwrite": "Перезаписать?", "import.overwriteHelp": "Перезаписать имя или атрибуты существующих подписчиков?", "import.recordsCount": "{num} / {total} записей", "import.stopImport": "Остановить импорт", "import.subscribe": "Подписаться", "import.title": "Импорт подписчиков", "import.upload": "Выгрузить", "lists.confirmDelete": "Уверены? Это не удалит подписчиков.", "lists.confirmSub": "Подтвердить подписку(и) на {name}", "lists.invalidName": "Неверное имя", "lists.newList": "Новый список", "lists.optin": "Подтверждение", "lists.optinHelp": "\"Двойное подтверждение\" отправляет подписчику электронное письмо с запросом подтверждения. Для списков с двойным подтверждением кампании отправляются только подтвержденным подписчикам", "lists.optinTo": "Подтвердить подписку на {name}", "lists.optins.double": "Двойное подтверждение", "lists.optins.single": "Одиночное подтверждение", "lists.sendCampaign": "Отправить компанию", "lists.sendOptinCampaign": "Отправить компанию с подтверждением подписки", "lists.type": "Тип", "lists.typeHelp": "Публичные списки открыты для всех, и их имена могут появляться на общедоступных страницах, таких как страница управления подпиской.", "lists.types.private": "Приватный", "lists.types.public": "Публичный", "logs.title": "Логи", "maintenance.help": "Некоторые действия могут занять продолжительное время в зависимости от объёма данных.", "maintenance.maintenance.unconfirmedOptins": "Неподтверждённые подписки", "maintenance.olderThan": "Старше чем", "maintenance.title": "Обслуживание", "maintenance.unconfirmedSubs": "Неподтверждённые подписки старше чем {name} дней.", "media.errorReadingFile": "Ошибка чтения файла: {error}", "media.errorResizing": "Ошибка изменения размера изображения: {error}", "media.errorSavingThumbnail": "Ошибка сохранения миниатюры: {error}", "media.errorUploading": "Ошибка выгрузки файла: {error}", "media.invalidFile": "Неверный файл: {error}", "media.title": "Медиа", "media.unsupportedFileType": "Неподдерживаемый тип файла ({type})", "media.upload": "Выгрузить", "media.uploadHelp": "Кликните или перетащите сюда одно или более изображений", "media.uploadImage": "Выгрузить изображение", "menu.allCampaigns": "Все компании", "menu.allLists": "Все списки", "menu.allSubscribers": "Все подписчики", "menu.dashboard": "Панель", "menu.forms": "Формы", "menu.import": "Импорт", "menu.logs": "Логи", "menu.maintenance": "Обслуживание", "menu.media": "Медиа", "menu.newCampaign": "Создать новую", "menu.settings": "Параметры", "public.archiveEmpty": "Нет архивированных сообщений.", "public.archiveTitle": "Архив списка рассылки", "public.blocklisted": "Отписанные насовсем.", "public.campaignNotFound": "Письмо не было найдено.", "public.confirmOptinSubTitle": "Подтверждение подписки", "public.confirmSub": "Подтвердить подписку", "public.confirmSubInfo": "Вы были добавлены в следующие списки:", "public.confirmSubTitle": "Подверждение", "public.dataRemoved": "Ваши подписки и все данные были удалены.", "public.dataRemovedTitle": "Данные удалены", "public.dataSent": "Ваши данные были отправлены Вам письмом с вложением.", "public.dataSentTitle": "Данные отправлены письмом", "public.errorFetchingCampaign": "Ошибка получения письма.", "public.errorFetchingEmail": "Письмо не найдено", "public.errorFetchingLists": "Ошибка получения списков. Пожалуйста, повторите.", "public.errorProcessingRequest": "Ошибка обработки запроса. Пожалуйста, повторите.", "public.errorTitle": "Ошибка", "public.invalidFeature": "Эта функция недоступна.", "public.invalidLink": "Неверная ссылка", "public.managePrefs": "Параметры письма", "public.managePrefsUnsub": "Снимите отметки со списков, чтобы отписаться от них.", "public.noListsAvailable": "Нет доступных списков для подписки.", "public.noListsSelected": "Для подписки не выбраны действительные списки.", "public.noSubInfo": "Нет подписок для подтверждения.", "public.noSubTitle": "Нет подписок", "public.notFoundTitle": "Не найдено", "public.prefsSaved": "Ваши параметры сохранены.", "public.privacyConfirmWipe": "Вы уверены, что хотите навсегда удалить все данные о подписке?", "public.privacyExport": "Экспортировать Ваши данные", "public.privacyExportHelp": "Копия Ваших данных будет отправлена Вам письмом", "public.privacyTitle": "Конфиденциальность и данные", "public.privacyWipe": "Стереть Ваши данные", "public.privacyWipeHelp": "Удалит все Ваши подписки и связанные данные из базы данных без возможности восстановления. ", "public.sub": "Подписаться", "public.subConfirmed": "Успешно подписано.", "public.subConfirmedTitle": "Подтверждено", "public.subName": "Имя (необязательно)", "public.subNotFound": "Подписка не найдена.", "public.subOptinPending": "Для подтверждения подписки(ок) Вам было отправлено письмо.", "public.subPrivateList": "Приватный список", "public.subTitle": "Подписаться", "public.unsub": "Отписаться", "public.unsubFull": "Также отписаться от всех будущих писем.", "public.unsubHelp": "Хотите отписаться от этих списков рассылки?", "public.unsubTitle": "Отписаться", "public.unsubbedInfo": "Вы были отписаны.", "public.unsubbedTitle": "Отписано", "public.unsubscribeTitle": "Отписаться от списков рассылки", "settings.appearance.adminHelp": "Пользовательский CSS для применения к пользовательскому интерфейсу администратора.", "settings.appearance.adminName": "Администратор", "settings.appearance.customCSS": "Пользовательский CSS", "settings.appearance.customJS": "Пользовательский JavaScript", "settings.appearance.name": "Вн<PERSON><PERSON>ний вид", "settings.appearance.publicHelp": "Пользовательские CSS и JavaScript для применения к публичным страницам.", "settings.appearance.publicName": "Общественность", "settings.bounces.action": "Действие", "settings.bounces.blocklist": "Блок-лист", "settings.bounces.count": "Количество отскоков", "settings.bounces.countHelp": "Количество отказов на одного абонента", "settings.bounces.delete": "Удалить", "settings.bounces.enable": "Включить обработку отказов", "settings.bounces.enableMailbox": "Включить почтовый ящик с отскоком", "settings.bounces.enableSES": "Включить SES", "settings.bounces.enableSendgrid": "Включить SendGrid", "settings.bounces.enableWebhooks": "Включить веб-крючки отскока", "settings.bounces.enabled": "Включено", "settings.bounces.folder": "Папка", "settings.bounces.folderHelp": "Имя папки IMAP для сканирования. Например: Входящие.", "settings.bounces.invalidScanInterval": "Интервал сканирования скачков должен составлять минимум 1 минуту.", "settings.bounces.name": "Отскоки", "settings.bounces.scanInterval": "Интервал сканирования", "settings.bounces.scanIntervalHelp": "Интервал, с которым почтовый ящик должен сканироваться на наличие отказов (с - секунда, м - минута).", "settings.bounces.sendgridKey": "<PERSON><PERSON><PERSON><PERSON> SendGrid", "settings.bounces.type": "Тип", "settings.bounces.username": "Имя пользователя", "settings.confirmRestart": "Убедитесь, что запущенные кампании приостановлены. Запустить снова?", "settings.duplicateMessengerName": "Повторяющееся имя мессенджера: {name}", "settings.errorEncoding": "Настройки кодирования ошибок: {error}", "settings.errorNoSMTP": "Должен быть включён минимум один блок SMTP", "settings.general.adminNotifEmails": "Письма с уведомлениями для администратора", "settings.general.adminNotifEmailsHelp": "Список адресов электронной почты, разделенных запятыми, на которые следует отправлять уведомления администратора, такие как обновления импорта, завершение кампании, сбой и т.д. ", "settings.general.checkUpdates": "Проверьте наличие обновлений", "settings.general.checkUpdatesHelp": "Периодически проверяйте новые выпуски приложений и уведомляйте об этом.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Публиковать компании с включённым архивированием на общедоступном сайте.", "settings.general.enablePublicSubPage": "Включить публичную страницу подписки", "settings.general.enablePublicSubPageHelp": "Показать страницу общедоступной подписки со всеми общедоступными списками, на которые можно подписаться.", "settings.general.faviconURL": "URL-адрес фавикона", "settings.general.faviconURLHelp": "(Необязательно) полный URL на favicon, который будет отображён, например, на странице отписки", "settings.general.fromEmail": "Адрес`from` по умолчанию", "settings.general.fromEmailHelp": "Адрес `from` по умолчанию для отображения в исходящих письмах компании. Можно изменить для каждой компании.", "settings.general.language": "Язык", "settings.general.logoURL": "URL логотипа", "settings.general.logoURLHelp": "(Необязательно) полный URL на логотип, который будет отображён, например, на странице отписки.", "settings.general.name": "Основное", "settings.general.rootURL": "Базовый URL", "settings.general.rootURLHelp": "Публичный URL текущего портала (без конечного слэша).", "settings.general.sendOptinConfirm": "Отправьте подтверждение об отказе от участия", "settings.general.sendOptinConfirmHelp": "Когда новые подписчики подписываются или добавляются через форму администратора, отправьте письмо с подтверждением подписки.", "settings.general.siteName": "Название сайта", "settings.invalidMessengerName": "Неверное имя мессенджера.", "settings.mailserver.authProtocol": "Протокол авторизации", "settings.mailserver.host": "Хо<PERSON>т", "settings.mailserver.hostHelp": "Адрес сервера SMTP.", "settings.mailserver.idleTimeout": "Таймаут простоя", "settings.mailserver.idleTimeoutHelp": "Время ожидания новой активности в соединении перед тем, как закрыть и удалить его из пула (s, m соотвественно секунды и минуты).", "settings.mailserver.maxConns": "Максимальное количество соединений", "settings.mailserver.maxConnsHelp": "Максимальное количество одновременных соединений к серверу SMTP.", "settings.mailserver.password": "Пароль", "settings.mailserver.passwordHelp": "Для изменения введите", "settings.mailserver.port": "Порт", "settings.mailserver.portHelp": "Порт сервера SMTP.", "settings.mailserver.skipTLS": "Пропустить проверку TLS", "settings.mailserver.skipTLSHelp": "Не проверять имя хоста в сертификате TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Включить STARTTLS.", "settings.mailserver.username": "Имя пользователя", "settings.mailserver.waitTimeout": "Таймаут ожидания", "settings.mailserver.waitTimeoutHelp": "Время ожидания новой активности в соединении перед тем, как закрыть и удалить его из пула (s, m соттветственно секунды и минуты)", "settings.media.provider": "Провайдер", "settings.media.s3.bucket": "<PERSON>а<PERSON><PERSON><PERSON>", "settings.media.s3.bucketPath": "Путь bucket", "settings.media.s3.bucketPathHelp": "Путь внутри bucket для выгрузки файлов. По умолчанию - /", "settings.media.s3.bucketType": "Тип bucket", "settings.media.s3.bucketTypePrivate": "Приватный", "settings.media.s3.bucketTypePublic": "Публичный", "settings.media.s3.key": "Ключ доступа AWS", "settings.media.s3.publicURL": "Пользовательский публичный URL (необязательно)", "settings.media.s3.publicURLHelp": "Пользовательский домен S3, используемый для ссылок на изображения вместо URL бэкенда S3 по умолчанию.", "settings.media.s3.region": "Регион", "settings.media.s3.secret": "Секретаня фраза AWS", "settings.media.s3.uploadExpiry": "Срок жизни выгрузки", "settings.media.s3.uploadExpiryHelp": "(Необязательно) Укажите TTL (в секундах) сгенерированного подписанного URL. Применимо только для приватных bucket (s, m, h, d соответствует секундам, минутам, часам и дням).", "settings.media.s3.url": "URL бэкенда S3", "settings.media.s3.urlHelp": "Измените только в случае использования бэкенда, совместимого с S3, например, Minio.", "settings.media.title": "Выгрузки медиа", "settings.media.upload.path": "Путь для выгрузок", "settings.media.upload.pathHelp": "Путь до каталога, куда будут выгружаться медиа-файлы.", "settings.media.upload.uri": "URI выгрузок", "settings.media.upload.uriHelp": "URI выгрузок, который будет видим снаружи. Медиа-файлы, выгруженные в upload_path, будут доступны публично через {root_url}, например, https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "Максимальное число соединений", "settings.messengers.maxConnsHelp": "Максимальное число одновременных соединений к серверу.", "settings.messengers.messageSaved": "Параметры сохранены. Перезагружаем приложение...", "settings.messengers.name": "Мессенджеры", "settings.messengers.nameHelp": "Напр.: my-sms. Цифры буквы / тире.", "settings.messengers.password": "Пароль", "settings.messengers.retries": "Повторные попытки", "settings.messengers.retriesHelp": "Число повторных попыток после ошибки отправки сообщения.", "settings.messengers.skipTLSHelp": "Не проверять мя хоста в сертификате TLS.", "settings.messengers.timeout": "Таймаут простоя", "settings.messengers.timeoutHelp": "Время ожидания новой активности в соединении перед тем, как закрыть и удалить его из пула (s, m соотвественно секунды и минуты)", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Базовый URL сервера постбэк.", "settings.messengers.username": "Имя пользователя", "settings.needsRestart": "Параметры изменены. Приостановите все запущенные компании и перезапустите приложение", "settings.performance.batchSize": "Размер партии", "settings.performance.batchSizeHelp": "Количество подписчиков, которые нужно извлечь из базы данных за одну итерацию. Каждая итерация извлекает подписчиков из базы данных, отправляет им сообщения, а затем переходит к следующей итерации, чтобы получить следующую партию. В идеале это должно быть выше максимально достижимой пропускной способности (concurrency * message_rate). ", "settings.performance.concurrency": "Параллельное выполнение", "settings.performance.concurrencyHelp": "Максимальное число одновременно работающих процессов, которые будут пытаться одновременно отправить сообщения.", "settings.performance.maxErrThreshold": "Порог максимального числа ошибок", "settings.performance.maxErrThresholdHelp": "Число ошибок (наприм<PERSON>р, таймауты SMTP во время отправки писем), после которого запущенная компания должна быть приостановлена для изучения или вмешательства.", "settings.performance.messageRate": "Скорость сообщений", "settings.performance.messageRateHelp": "Максимальное количество сообщений, отправляемых одним рабочим процессом в секунду. Если concurrency = 10 и message_rate = 10, то до 10x10 = 100 сообщений могут выталкиваться каждую секунду. Этот параметр, наряду с параллельным выполнением, следует настроить так, чтобы количество отправляемых сообщений в секунду не вышло за рамки ограничений скорости (если таковые имеются) целевых серверов SMTP.", "settings.performance.name": "Производительность", "settings.performance.slidingWindow": "Включить ограничение скользящего окна", "settings.performance.slidingWindowDuration": "Длительность", "settings.performance.slidingWindowDurationHelp": "Длительность периода скользящего окна (m, h соотвественно минуты и часы)", "settings.performance.slidingWindowHelp": "Ограничить количество сообщений, которые будут отправлены в указанный период. По достижении этого ограничения, сообщения будут задержаны до очистки временного окна.", "settings.performance.slidingWindowRate": "Максимальное количество сообщений", "settings.performance.slidingWindowRateHelp": "Максимальное количество сообщений, которые будут отправлены в течение временного окна.", "settings.privacy.allowBlocklist": "Разрешить блокировку", "settings.privacy.allowBlocklistHelp": "Позволить подписчикам отписываться от всех списков рассылки и помечать себя заблокированными?", "settings.privacy.allowExport": "Разрешить экспорт", "settings.privacy.allowExportHelp": "Разрешить подписчикам экспортировать собранные на них данные?", "settings.privacy.allowPrefs": "Разрешить изменение параметров", "settings.privacy.allowPrefsHelp": "Разрешить подписчикам менять такие параметры, как их имя и подписки.", "settings.privacy.allowWipe": "Разрешить удаление", "settings.privacy.allowWipeHelp": "Разрешить подписчикам удалять себя (включая их подписки и иные данные) из базы данных. Просмотры кампании и клики по ссылкам также удаляются, в то время как просмотры и счетчики кликов остаются (без привязанного к ним подписчика), так что это не влияет на статистику и аналитику.", "settings.privacy.domainBlocklist": "Блокирующий список доменов", "settings.privacy.domainBlocklistHelp": "Адреса электронной почты с такими доменами не допускаются к подписке. Введите один домен в строке, например: somesite.com", "settings.privacy.individualSubTracking": "Отслеживание каждого подписчика", "settings.privacy.individualSubTrackingHelp": "Отслеживать просмотры и клики на уровне каждого подписчика. Если отключено, просмотры и клики отслеживаются без привязки к конкретным подписчикам.", "settings.privacy.listUnsubHeader": "Включать заголовок `List-Unsubscribe`", "settings.privacy.listUnsubHeaderHelp": "Включать заголовок отписки", "settings.privacy.name": "Конфиденциальност", "settings.restart": "Перезапустить", "settings.smtp.customHeaders": "Настраиваемые заголовки", "settings.smtp.customHeadersHelp": "Необязательный массив заголовков e-mail, которые будут включены во все письма, отправляемые с этого сервера. Например: [{\"X-Custom\": \"значение\"}, {\"X-Custom2\": \"значение\"}]", "settings.smtp.enabled": "Включено", "settings.smtp.heloHost": "Имя хоста HELO", "settings.smtp.heloHostHelp": "Необязательно. Некоторые серверы SMTP требуют FQDN в имени хоста. По умолчанию команды HELO идут с `localhost`. Укажите, если должно использоваться собственное имя хоста.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Повторные попытки", "settings.smtp.retriesHelp": "Количество повторных попыток после ошибки отправки сообщения.", "settings.smtp.sendTest": "Отправить электронное письмо", "settings.smtp.setCustomHeaders": "Установка настраиваемых заголовков", "settings.smtp.testConnection": "Тестовое подключение", "settings.smtp.testEnterEmail": "Введите пароль для проверки", "settings.smtp.toEmail": "По e-mail", "settings.title": "Параметры", "settings.updateAvailable": "Доступна новая версия: {version}.", "subscribers.advancedQuery": "Дополнительно", "subscribers.advancedQueryHelp": "Частичное выражение SQL для запроса атрибутов подписчика", "subscribers.attribs": "Атрибуты", "subscribers.attribsHelp": "Атрибуты определны, как сопоставление JSON, например:", "subscribers.blocklistedHelp": "Заблокированные подписчики никогда не получат ни одного письма.", "subscribers.confirmBlocklist": "Заблокировать {num} подписчика(ов)?", "subscribers.confirmDelete": "Удалить {num} подписчика(ов)?", "subscribers.confirmExport": "Экспортировать {num} подписчика(ов)?", "subscribers.domainBlocklisted": "Домен электронной почты занесен в список блокировки.", "subscribers.downloadData": "Загрузить данные", "subscribers.email": "E-mail", "subscribers.emailExists": "E-mail существует.", "subscribers.errorBlocklisting": "Ошибка блокировки подписчиков: {error}", "subscribers.errorNoIDs": "Не указано ни одного ID.", "subscribers.errorNoListsGiven": "Не указано ни одного списка.", "subscribers.errorPreparingQuery": "Ошибка подготовки запроса подписчиков: {error}", "subscribers.errorSendingOptin": "Ошибка отправки письма подтверждения.", "subscribers.export": "Экспорт", "subscribers.invalidAction": "Неверное действие.", "subscribers.invalidEmail": "Неверное письмо.", "subscribers.invalidJSON": "Неверный JSON в атрибутах.", "subscribers.invalidName": "Неверное имя.", "subscribers.listChangeApplied": "Изменения списка применены.", "subscribers.lists": "Списки", "subscribers.listsHelp": "Списки, от которых подписчики сами отписались, не могут быть удалены.", "subscribers.listsPlaceholder": "Списки для подписки", "subscribers.manageLists": "Управление списками", "subscribers.markUnsubscribed": "Ометить, как отписанный", "subscribers.newSubscriber": "Новый подписчик", "subscribers.numSelected": "{num} подписчика(ов) выбрано", "subscribers.optinSubject": "Подтвердить подписку", "subscribers.preconfirm": "Предварительное подтверждение подписки", "subscribers.preconfirmHelp": "Не отправляйте электронные письма с правом отказа и помечайте все подписки на список как 'подписанные'.", "subscribers.query": "Запрос", "subscribers.queryPlaceholder": "E-mail или имя", "subscribers.reset": "Сброс", "subscribers.selectAll": "Выбрать все {num}", "subscribers.sendOptinConfirm": "Отправьте подтверждение об отказе от участия", "subscribers.sentOptinConfirm": "Отправка подтверждения об участии", "subscribers.status.blocklisted": "Заблокирован", "subscribers.status.confirmed": "Подтверждён", "subscribers.status.enabled": "Вкл<PERSON><PERSON>ён", "subscribers.status.subscribed": "Под<PERSON>и<PERSON><PERSON>н", "subscribers.status.unconfirmed": "Неподтверждён", "subscribers.status.unsubscribed": "От<PERSON><PERSON><PERSON><PERSON>н", "subscribers.subscribersDeleted": "{num} подписчика(ов) удалено", "templates.cantDeleteDefault": "Нельзя удалить шаблон по умолчанию", "templates.default": "По умолчанию", "templates.dummyName": "Пустая компания", "templates.dummySubject": "Рустая тема письма", "templates.errorCompiling": "Ошибка компиляции шаблона: {error}", "templates.errorRendering": "Ошибка рендеринга сообщения: {error}", "templates.fieldInvalidName": "Неверная длина имени.", "templates.makeDefault": "Установить по умолчанию", "templates.newTemplate": "Новый шаблон", "templates.placeholderHelp": "Заполнитель {placeholder} должен присутствовать в шаблоне в одном экземпляре.", "templates.preview": "Предпросмотр", "templates.rawHTML": "Необработанный HTML", "templates.subject": "Тема", "users.login": "Вход в систему", "users.logout": "Выход из системы"}