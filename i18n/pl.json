{"_.code": "pl", "_.name": "<PERSON><PERSON> (pl)", "admin.errorMarshallingConfig": "Błąd przerabiania konfiguracji: {error}", "analytics.count": "Liczba", "analytics.fromDate": "Od", "analytics.invalidDates": "Invalid `from` or `to` dates.", "analytics.isUnique": "Zliczenia są unikalne dla każdego subskrybenta.", "analytics.links": "<PERSON><PERSON>", "analytics.nonUnique": "Zliczenia nie są unikalne, ponieważ indywidualne śledzenie subskrybentów jest wyłączone.", "analytics.title": "Analityka", "analytics.toDate": "Do", "bounces.source": "Źródła", "bounces.unknownService": "Nieznane usługi.", "bounces.view": "Zobacz odbicia", "campaigns.addAltText": "Dodaj alternatywną wiadomość jako plain text", "campaigns.archive": "Archiwizacja", "campaigns.archiveEnable": "Opublikuj do publicznego archiwum", "campaigns.archiveHelp": "Opublikuj (w trakcie, zatrzymane, zakończone) treść kampanii do publicznego archiwum.", "campaigns.archiveMeta": "<PERSON><PERSON><PERSON>", "campaigns.archiveMetaHelp": "Dane podstawioneko subskrybenta do użycia w publicznym archiwum. W tym nazwa, email, i dowolne opcjonalne atrybuty użyte w szablonie kampanii.", "campaigns.cantUpdate": "Nie można aktualizować aktywnej ani zakończonej kampanii", "campaigns.clicks": "Kliknięcia", "campaigns.confirmDelete": "<PERSON><PERSON><PERSON> {name}", "campaigns.confirmSchedule": "Ta kampania rozpocznie się automatyczne i zadanej dacie  czasie. <PERSON><PERSON> zaplanować teraz?", "campaigns.confirmSwitchFormat": "T<PERSON>ść może utracić formatowanie. Kontynuować?", "campaigns.content": "<PERSON><PERSON><PERSON><PERSON>", "campaigns.contentHelp": "<PERSON><PERSON><PERSON><PERSON> tutaj", "campaigns.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.copyOf": "<PERSON><PERSON> {name}", "campaigns.customHeadersHelp": "Tablica niestandardowych nagłówków do dołączenia do wiadomości wychodzących. np: [{\"X-Custom\": \"wartosc\"}, {\"X-Custom2\": \"wartosc\"}]", "campaigns.dateAndTime": "Data i czas", "campaigns.ended": "Zakończona", "campaigns.errorSendTest": "Błąd wysyłania testu: {error}", "campaigns.fieldInvalidBody": "Błąd kompilacji treści kampanii: {error}", "campaigns.fieldInvalidFromEmail": "Nieprawidłowy `from_email`.", "campaigns.fieldInvalidListIDs": "Nieprawidłowa lista identyfikatorów (IDs)", "campaigns.fieldInvalidMessenger": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> {name}.", "campaigns.fieldInvalidName": "Nieprawidłowa długość dla nazwy,", "campaigns.fieldInvalidSendAt": "Zaplanowana data powinna być w przyszłości,", "campaigns.fieldInvalidSubject": "Nieprawidłowa długość tytułu", "campaigns.formatHTML": "Format HTML", "campaigns.fromAddress": "Adres od", "campaigns.fromAddressPlaceholder": "<PERSON><PERSON> <<EMAIL>>", "campaigns.invalid": "Nieprawidłowa kampania", "campaigns.invalidCustomHeaders": "Nieprawidłowe niestandardowe nagłówki: {error}", "campaigns.markdown": "<PERSON><PERSON>", "campaigns.needsSendAt": "Kampania wymaga daty w celu zaplanowania.", "campaigns.newCampaign": "Nowa kampania", "campaigns.noKnownSubsToTest": "Brak znanych subskrybentów do testów.", "campaigns.noOptinLists": "Nie znaleziono list typu opt-in do stworzenia kampanii.", "campaigns.noSubs": "Nie ma subskrybentów w wybranej liście w celu stworzenia kampanii.", "campaigns.noSubsToTest": "Brak subskrybentów do wyboru.", "campaigns.notFound": "Kampania nieznaleziona.", "campaigns.onlyActiveCancel": "Tylko aktywne kampanie mogą by<PERSON> anulowane.", "campaigns.onlyActivePause": "Tylko aktywne kampanie mogą by<PERSON> pauzo<PERSON>e.", "campaigns.onlyDraftAsScheduled": "<PERSON><PERSON>o szkice kampanii mogą być planowane.", "campaigns.onlyPausedDraft": "Tylko kampanie pauzowane i szkice mogą być startowane.", "campaigns.onlyScheduledAsDraft": "Tylko planowane kampanie mogą być zapisane jako s<PERSON>.", "campaigns.pause": "<PERSON><PERSON>", "campaigns.plainText": "Plain text", "campaigns.preview": "Podgląd", "campaigns.progress": "Postęp", "campaigns.queryPlaceholder": "<PERSON><PERSON><PERSON> lub temat", "campaigns.rateMinuteShort": "min.", "campaigns.rawHTML": "Raw HTML", "campaigns.removeAltText": "Usuń alternatywną treść typu plain text", "campaigns.richText": "Wzbogacony format tekstowy (Rich text)", "campaigns.schedule": "Zaplan<PERSON>j kamp<PERSON>ę", "campaigns.scheduled": "Zaplanowana", "campaigns.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "campaigns.sendLater": "Wyślij później", "campaigns.sendTest": "<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON> testow<PERSON>", "campaigns.sendTestHelp": "Naciśnij Enter po wypisaniu adresu w celu dodania kolejnych odbiorców. Adresy muszą należeć do istniejących subskrybentów.", "campaigns.sendToLists": "Listy do których wysłać", "campaigns.sent": "Wysłana", "campaigns.start": "<PERSON>ystar<PERSON><PERSON>", "campaigns.started": "\"{name}\" wystarto<PERSON>", "campaigns.startedAt": "Wystartowana", "campaigns.stats": "Statystyki", "campaigns.status.cancelled": "Anulowana", "campaigns.status.draft": "Szkic", "campaigns.status.finished": "Zakończona", "campaigns.status.paused": "Spauzowana", "campaigns.status.running": "<PERSON> trakcie", "campaigns.status.scheduled": "Zaplanowana", "campaigns.statusChanged": "\"{name}\" jest {status}", "campaigns.subject": "<PERSON><PERSON>", "campaigns.testDisabled": "Enter password to test", "campaigns.testEmails": "E-maile", "campaigns.testSent": "Wiadomość testowa wysłana", "campaigns.timestamps": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "campaigns.trackLink": "Track link", "campaigns.views": "Wyświetlenia", "dashboard.campaignViews": "Wyświetlenia kampanii", "dashboard.linkClicks": "Kliknięcia linków", "dashboard.messagesSent": "Wiadomości wysłane ", "dashboard.orphanSubs": "Porzucone", "email.data.info": "Kopia wszystkich zarejestrowanych danych o Tobie jest dołączona jako plik w formacie JSON. Może zostać otworzona w edytorze tekstu.", "email.data.title": "<PERSON><PERSON> dane", "email.optin.confirmSub": "Potwierdź subskrypcję", "email.optin.confirmSubHelp": "Potwierdź subskrypcję naciskając przycisk poniżej.", "email.optin.confirmSubInfo": "Zostałeś dodany(a) do następujących list:", "email.optin.confirmSubTitle": "Potwierdź subskrypcję", "email.optin.confirmSubWelcome": "<PERSON><PERSON><PERSON><PERSON>", "email.optin.privateList": "Lista prywatna", "email.status.campaignReason": "<PERSON><PERSON><PERSON><PERSON>", "email.status.campaignSent": "<PERSON><PERSON>ła<PERSON>", "email.status.campaignUpdateTitle": "Aktualizacja kampanii", "email.status.importFile": "Plik", "email.status.importRecords": "Rekordy", "email.status.importTitle": "Importuj aktualizacjię", "email.status.status": "Status", "email.unsub": "Odsubskrybuj", "email.unsubHelp": "<PERSON>e ch<PERSON> otrzymywać tych maili?", "email.viewInBrowser": "Zobacz w przeglądarce", "forms.formHTML": "Formularz HTML", "forms.formHTMLHelp": "Użyj następującego kodu HTML w celu wyświetlenia formularza na zewnętrznej stronie. Formularz powinien mieć pole z adresem email i jedno lub więcej pól z `l` (UUID listy). Pole z nazwą jest opcjonalne.", "forms.noPublicLists": "Nie ma publicznych list do wygenerowania formularza.", "forms.publicLists": "<PERSON><PERSON><PERSON> listy", "forms.publicSubPage": "<PERSON>zna strona subskrypcji", "forms.selectHelp": "Wybierz listy do dodania do formularza", "forms.title": "Formularze", "globals.buttons.add": "<PERSON><PERSON><PERSON>", "globals.buttons.addNew": "<PERSON><PERSON><PERSON>e", "globals.buttons.back": "Wstecz", "globals.buttons.cancel": "<PERSON><PERSON><PERSON>", "globals.buttons.clone": "K<PERSON><PERSON><PERSON>", "globals.buttons.close": "Zamknij", "globals.buttons.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.buttons.delete": "Usuń", "globals.buttons.deleteAll": "Delete all", "globals.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.enabled": "Włą<PERSON><PERSON>", "globals.buttons.insert": "Insert", "globals.buttons.learnMore": "Dowiedz się więcej", "globals.buttons.more": "<PERSON><PERSON><PERSON><PERSON>j", "globals.buttons.new": "Nowa", "globals.buttons.ok": "Ok", "globals.buttons.remove": "Usuń", "globals.buttons.save": "<PERSON><PERSON><PERSON><PERSON>", "globals.buttons.saveChanges": "Zapisz zmiany", "globals.days.0": "<PERSON><PERSON>", "globals.days.1": "Pon", "globals.days.2": "Wt", "globals.days.3": "<PERSON>r", "globals.days.4": "Czw", "globals.days.5": "Pt", "globals.days.6": "Sob", "globals.days.7": "Sat", "globals.fields.createdAt": "Utworzone", "globals.fields.description": "Description", "globals.fields.id": "ID", "globals.fields.name": "Nazwa", "globals.fields.status": "Status", "globals.fields.type": "<PERSON><PERSON>", "globals.fields.updatedAt": "Zak<PERSON>ali<PERSON>wan<PERSON>", "globals.fields.uuid": "UUID", "globals.messages.confirm": "Na pewno?", "globals.messages.confirmDiscard": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>?", "globals.messages.created": "\"{name}\" ut<PERSON><PERSON><PERSON>", "globals.messages.deleted": "\"{name}\" <PERSON><PERSON><PERSON><PERSON>", "globals.messages.deletedCount": "{name} ({num}) us<PERSON>ęto", "globals.messages.done": "Done", "globals.messages.emptyState": "<PERSON>c tutaj nie ma", "globals.messages.errorCreating": "<PERSON><PERSON><PERSON><PERSON> podczas tworzenia {name}: {error}", "globals.messages.errorDeleting": "<PERSON><PERSON><PERSON><PERSON> podczas usuwania {name}: {error}", "globals.messages.errorFetching": "<PERSON>ł<PERSON>d podczas pobierania {name}: {error}", "globals.messages.errorInvalidIDs": "Podano jeden lub więcej nieprawidłowy ID: {error}", "globals.messages.errorUUID": "Błąd podczas generowania UUID: {error}", "globals.messages.errorUpdating": "Bł<PERSON>d podczas aktualizacji {name}: {error}", "globals.messages.internalError": "Internal server error", "globals.messages.invalidData": "Invalid data", "globals.messages.invalidFields": "Invalid fields: {name}", "globals.messages.invalidID": "Nieprawidłowy iD", "globals.messages.invalidUUID": "Nieprawidłowy UUID", "globals.messages.missingFields": "Missing field(s): {name}", "globals.messages.notFound": "{name} nie znaleziono", "globals.messages.passwordChange": "Podaj wartość do zmiany", "globals.messages.updated": "\"{name}\" z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globals.months.1": "Sty", "globals.months.10": "<PERSON><PERSON>", "globals.months.11": "<PERSON><PERSON>", "globals.months.12": "Gru", "globals.months.2": "Lut", "globals.months.3": "Mar", "globals.months.4": "<PERSON><PERSON><PERSON>", "globals.months.5": "Maj", "globals.months.6": "<PERSON><PERSON>", "globals.months.7": "Lip", "globals.months.8": "<PERSON><PERSON>", "globals.months.9": "Wrz", "globals.states.off": "Wyłączone", "globals.terms.all": "All", "globals.terms.analytics": "Analityka", "globals.terms.bounce": "Odbicie | Obicia", "globals.terms.bounces": "Odbicia", "globals.terms.campaign": "Kampania | Kampanie", "globals.terms.campaigns": "<PERSON><PERSON>ani<PERSON>", "globals.terms.dashboard": "Przegląd", "globals.terms.day": "Dzień | Dni", "globals.terms.hour": "<PERSON><PERSON><PERSON> | Godzin", "globals.terms.list": "Lista | Listy", "globals.terms.lists": "Listy", "globals.terms.media": "Media | Media", "globals.terms.messenger": "Komunikator | Komunikatory", "globals.terms.messengers": "Komunikatory", "globals.terms.minute": "Minuta | Minut", "globals.terms.month": "Miesiąc | Miesięcy", "globals.terms.second": "Sekunda | Sekundy", "globals.terms.settings": "Ustawienia", "globals.terms.subscriber": "Subskrypcja | Subskrypcje", "globals.terms.subscribers": "Subskrypcje", "globals.terms.subscriptions": "Subskrypcja | Subskrypcje", "globals.terms.tag": "Tag | Tagi", "globals.terms.tags": "Tagi", "globals.terms.template": "Szablon | Szablony", "globals.terms.templates": "S<PERSON><PERSON><PERSON><PERSON>", "globals.terms.tx": "Transakcyjne | Transakcyjne", "globals.terms.year": "Rok | Lat", "import.alreadyRunning": "Importowanie jest już uruchomio<PERSON>. <PERSON><PERSON><PERSON><PERSON>, aż <PERSON> z<PERSON>, albo zatrzymaj je przed ponowną próbą.", "import.blocklist": "<PERSON>a zablokowanych", "import.csvDelim": "Separator CSV", "import.csvDelimHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON> separatorem jest przecinek.", "import.csvExample": "Przykładowy \"surowy\" CSV.", "import.csvFile": "Plik CSV lub ZIP", "import.csvFileHelp": "Naciśnij lub przerzuć plik CSV lub ZIP w to miejsce.", "import.errorCopyingFile": "Błąd kopiowania pliku: {error}", "import.errorProcessingZIP": "Błąd procesowania pliku ZIP: {error}", "import.errorStarting": "Błąd rozpoczynania importu: {error}", "import.importDone": "Zrobione", "import.importStarted": "Import rozpoczęty", "import.instructions": "Instrukcje", "import.instructionsHelp": "Wrzuć plik CSV lub ZIP z pojedynczym plikiem CSV w celu masowego importowania subskybentów. Plik CSV powinien posiadać wskazane nagłówki kolumn z dokładnie tymi nazwami. Atrybuty (opcjonalne) powinny być zapisane w poprawnym formacje JSON z podwójnie escapowanymi cudzysłowami.", "import.invalidDelim": "Separator pow<PERSON>en być pojedynczym znakiem.", "import.invalidFile": "Nieprawidłowy plik: {error}", "import.invalidMode": "Nieprawidłowy tryp", "import.invalidParams": "Nieprawidłowe parametry: {error}", "import.invalidSubStatus": "Invalid subscription status", "import.listSubHelp": "Listy do subskrybowania.", "import.mode": "<PERSON><PERSON>", "import.overwrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "import.overwriteHelp": "Nadpisać nazwy i atrybuty istniejących subskrybentów?", "import.recordsCount": "{num} / {total} rekordów", "import.stopImport": "Zatrzymaj import", "import.subscribe": "Subskrypcje", "import.title": "Importuj subskrypcje", "import.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lists.confirmDelete": "<PERSON><PERSON><PERSON> pewny(a)? To nie usunie subskrybcji.", "lists.confirmSub": "Potwierdź subskrypcję dla  {name}", "lists.invalidName": "Nieprawidłowa nazwa", "lists.newList": "Nowa lista", "lists.optin": "Opt-in", "lists.optinHelp": "Podwójny opt-in wysyła e-mail do subskrybenta z zapytaniem o potwierdzenie. W listach z podwójnym opt-in kampanie są wysyłane tylko do potwierdzonych subskrybentów.", "lists.optinTo": "Opt-in do {name}", "lists.optins.double": "Podwójny opt-in", "lists.optins.single": "<PERSON><PERSON><PERSON><PERSON><PERSON> opt-in", "lists.sendCampaign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lists.sendOptinCampaign": "<PERSON><PERSON><PERSON><PERSON><PERSON> kamp<PERSON>ę opt-in", "lists.type": "<PERSON><PERSON>", "lists.typeHelp": "Publiczne listy są otwarte do świata i każdy może się zapisać. Nazwy są widoczne np. na stronie do zarządzania subskrypcją.", "lists.types.private": "<PERSON><PERSON><PERSON><PERSON>", "lists.types.public": "Publiczna", "logs.title": "<PERSON><PERSON>", "maintenance.help": "Niektóre akcje mogą zaj<PERSON> dłu<PERSON>, w zależności od ilości danych.", "maintenance.maintenance.unconfirmedOptins": "Niepotwierdzone subskrypcje opt-in.", "maintenance.olderThan": "<PERSON><PERSON>", "maintenance.title": "Ko<PERSON>r<PERSON><PERSON><PERSON>", "maintenance.unconfirmedSubs": "Niepotwierdzone subskrypcje starsze niż {name} dni.", "media.errorReadingFile": "Błąd odczytu pliku: {error}", "media.errorResizing": "Błąd zmiany rozmiaru obrazu: {error}", "media.errorSavingThumbnail": "Błąd zapisywania miniaturki: {error}", "media.errorUploading": "Błąd wgrywania pliku: {error}", "media.invalidFile": "Nieprawidłowy plik: {error}", "media.title": "Media", "media.unsupportedFileType": "Niewspierany typ pliku ({type})", "media.upload": "Wysyłanie", "media.uploadHelp": "Kliknij lub przeciągnij jeden lub więcej plików tutaj", "media.uploadImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> obraz", "menu.allCampaigns": "Wszystkie kampanie", "menu.allLists": "Wszystkie listy", "menu.allSubscribers": "Wszyscy subskrybenci", "menu.dashboard": "Przegląd", "menu.forms": "Formularze", "menu.import": "Import", "menu.logs": "<PERSON><PERSON>", "menu.maintenance": "Maintenance", "menu.media": "Media", "menu.newCampaign": "Utwórz nową", "menu.settings": "Ustawienia", "public.archiveEmpty": "No archived messages yet.", "public.archiveTitle": "Mailing list archive", "public.blocklisted": "Permanently unsubscribed.", "public.campaignNotFound": "W<PERSON><PERSON><PERSON><PERSON>ć email nie została znaleziona.", "public.confirmOptinSubTitle": "Potwierdź subskrypcję", "public.confirmSub": "Potwierdź subskrypcję", "public.confirmSubInfo": "Zost<PERSON>ł<PERSON>ś(aś) dodany(a) do następujących listy:", "public.confirmSubTitle": "Potwierdź", "public.dataRemoved": "Twoja subskrypcja i wszystkie powiązane dane została usunięta.", "public.dataRemovedTitle": "<PERSON>", "public.dataSent": "Twoje dane został przesłane do Ciebie mailem w formie załącznika.", "public.dataSentTitle": "Dane przesłanie mailem", "public.errorFetchingCampaign": "Błąd pobierania wiadomości email.", "public.errorFetchingEmail": "Wiadomość email nie została znaleziona", "public.errorFetchingLists": "Błąd pobierania list. Spróbuj ponownie.", "public.errorProcessingRequest": "Błąd przetwarzania żądania. Spróbuj ponownie.", "public.errorTitle": "Błąd", "public.invalidFeature": "Ta funkcjonalność jest niedostępna.", "public.invalidLink": "Nieprawidłowy liny.", "public.managePrefs": "Manage preferences", "public.managePrefsUnsub": "Uncheck lists to unsubscribe from them.", "public.noListsAvailable": "Brak list do subkskrybowania.", "public.noListsSelected": "Brak prawidłowych list wybranych do subskrybowania.", "public.noSubInfo": "Brak subskrypcji do potwierdzenia.", "public.noSubTitle": "Brak subskrypcji ", "public.notFoundTitle": "Nie znaleziono", "public.prefsSaved": "Your preferences have been saved.", "public.privacyConfirmWipe": "<PERSON><PERSON> j<PERSON> p<PERSON>(a), <PERSON>e ch<PERSON>z usunąć wszystkie swoje dane?", "public.privacyExport": "Eksportuj swoje dane", "public.privacyExportHelp": "Kopia twoich danych zostanie przesłana do ciebie mailem.", "public.privacyTitle": "Prywatność i dane", "public.privacyWipe": "<PERSON><PERSON>ń swoje dane", "public.privacyWipeHelp": "Usuń wszystkie swoje subskrypcje i dane z nimi związanie permanentnie z bazy danych.", "public.sub": "Subskrybuj", "public.subConfirmed": "Pomyślnie zasubskrybowano.", "public.subConfirmedTitle": "Potwierdzono", "public.subName": "Nazwa (opcjonalnie)", "public.subNotFound": "Subskrypcja nie została znaleziona", "public.subOptinPending": "Została wysłana wiadomość w celu potwierdzenia subskrypcji.", "public.subPrivateList": "Lista prywatna", "public.subTitle": "Subskrybuj", "public.unsub": "Odsubskrybuj", "public.unsubFull": "Również odsubskrybuj od wszystkich przyszłych maili.", "public.unsubHelp": "<PERSON><PERSON> ch<PERSON>z się wypisać z tej listy mailowej?", "public.unsubTitle": "W<PERSON><PERSON>z się", "public.unsubbedInfo": "Pomyślnie odsubskrybowano", "public.unsubbedTitle": "Ods<PERSON><PERSON><PERSON><PERSON><PERSON>", "public.unsubscribeTitle": "Wypisz się z listy mailingowej", "settings.appearance.adminHelp": "Niestandardowy CSS do interfejsu admina.", "settings.appearance.adminName": "Admin", "settings.appearance.customCSS": "Niestandardowy CSS", "settings.appearance.customJS": "Niestandardowy JavaScript", "settings.appearance.name": "Wygląd", "settings.appearance.publicHelp": "Niestandardowy CSS i JavaScript do publicznych stron.", "settings.appearance.publicName": "Publiczne", "settings.bounces.action": "<PERSON><PERSON><PERSON><PERSON>", "settings.bounces.blocklist": "<PERSON>a zablokowanych", "settings.bounces.count": "Liczba odbić", "settings.bounces.countHelp": "Liczba odbić na subskrybenta", "settings.bounces.delete": "Usuń", "settings.bounces.enable": "Włącz procesowanie odbić", "settings.bounces.enableMailbox": "Włącz skrzynkę pocztową z odbiciami", "settings.bounces.enableSES": "Włącz SES", "settings.bounces.enableSendgrid": "Włącz SendGrid", "settings.bounces.enableWebhooks": "Włącz webhooki odbić", "settings.bounces.enabled": "Włą<PERSON><PERSON>", "settings.bounces.folder": "Folder", "settings.bounces.folderHelp": "Nazwa folderu IMAP do skanowania. Np: Inbox.", "settings.bounces.invalidScanInterval": "Interwał czasu powinien być minimum 1 minuta.", "settings.bounces.name": "Odbicia", "settings.bounces.scanInterval": "Interwał skanowania", "settings.bounces.scanIntervalHelp": "Interwał czasu przeszukiwania skrzynki w poszkukiwaniu odbić (s dla sekund, m dla minut).", "settings.bounces.sendgridKey": "Klucz SendGrid", "settings.bounces.type": "<PERSON><PERSON>", "settings.bounces.username": "Nazwa użytkownika", "settings.confirmRestart": "<PERSON><PERSON><PERSON><PERSON>, że uruchomione kampanie są zapauzowane. <PERSON><PERSON>art<PERSON>ć?", "settings.duplicateMessengerName": "Powtórzona nazwa komunikatora: {name}", "settings.errorEncoding": "Błąd szyfrowania ustawień: {error}", "settings.errorNoSMTP": "Co najmniej jeden blok SMTP powinien być aktywowany", "settings.general.adminNotifEmails": "Adres email do powiadomień admina", "settings.general.adminNotifEmailsHelp": "Lista maili oddzielona przecinkami do adminów, którym przesyłać informacje o importach, zakończonych kampaniach, błędach itd. ", "settings.general.checkUpdates": "Sprawdź czy są aktualizacje", "settings.general.checkUpdatesHelp": "Regularnie sprawdzaj czy są aktualizacje i powiadamiaj o tym.", "settings.general.enablePublicArchive": "Enable public mailing list archive page", "settings.general.enablePublicArchiveHelp": "Publish campaigns on which archiving is enabled on the public website.", "settings.general.enablePublicSubPage": "Włącz publiczną stronę subskrypcji", "settings.general.enablePublicSubPageHelp": "Pokaż publiczną stronę do zapisu na subskrypcje publicznych list.", "settings.general.faviconURL": "URL Favicony", "settings.general.faviconURLHelp": "(Opcjonalnie) pełny URL do statycznej favicony. Będzie używana na takich stronach jak np strona do wypisania się ze subskrypcji.", "settings.general.fromEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON> email `od`", "settings.general.fromEmailHelp": "Dom<PERSON><PERSON><PERSON><PERSON> email `od` do pokazania w wychodzących kampaniach emailowych. Może zostać zmienione w kampanii.", "settings.general.language": "Język", "settings.general.logoURL": "URL loga", "settings.general.logoURLHelp": "(Opcjonalne) pełny URL do statycznego loga. Będzie używana na takich stronach jak np strona do wypisania się ze subskrypcji.", "settings.general.name": "Ogólne", "settings.general.rootURL": "Bazowy URL", "settings.general.rootURLHelp": "Publiczny URL instalacji (bez slasha na końcu)", "settings.general.sendOptinConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> potwierdzenie opt-in", "settings.general.sendOptinConfirmHelp": "Gdy nowi subskrybenci się zapiszą albo zostaną dodanie przez formularz admina wysyłaj maila opt-in z żądaniem potwierdzenia.", "settings.general.siteName": "Site name", "settings.invalidMessengerName": "Nieprawidłowa nazwa komunikatora.", "settings.mailserver.authProtocol": "Protokół autoryzacji", "settings.mailserver.host": "Host", "settings.mailserver.hostHelp": "Adres serwera SMTP.", "settings.mailserver.idleTimeout": "<PERSON><PERSON>", "settings.mailserver.idleTimeoutHelp": "<PERSON>zas czekania na nową aktywność na połączeniu przed jej zamknięciem i usunięciem z puli (s dla sekud, m dla minut).", "settings.mailserver.maxConns": "Maksymalna liczba połączeń", "settings.mailserver.maxConnsHelp": "Maksymalna liczba jednoczesnych połączeń do serwera SMTP.", "settings.mailserver.password": "<PERSON><PERSON><PERSON>", "settings.mailserver.passwordHelp": "Wpisz w celu zmiany", "settings.mailserver.port": "Port", "settings.mailserver.portHelp": "Port serwera SMTP.", "settings.mailserver.skipTLS": "Pomiń weryfikację TLS", "settings.mailserver.skipTLSHelp": "Pomiń sprawdzanie nazwy hosta dla certyfikatu TLS.", "settings.mailserver.tls": "TLS", "settings.mailserver.tlsHelp": "Włącz STARTTLS.", "settings.mailserver.username": "Nazwa użytkownika", "settings.mailserver.waitTimeout": "<PERSON>zas oczekiwania", "settings.mailserver.waitTimeoutHelp": "<PERSON>zas czekania na nową aktywność na połączeniu przed jej zamknięciem i usunięciem z puli (s dla sekund, m dla minut).", "settings.media.provider": "Dostawca", "settings.media.s3.bucket": "<PERSON><PERSON><PERSON> (Bucket)", "settings.media.s3.bucketPath": "Ścieżka komory (Bucket path)", "settings.media.s3.bucketPathHelp": "Ścieżka w komorze do której wrzucać pliki. Domyślna to /", "settings.media.s3.bucketType": "Typ komory (Bucket type)", "settings.media.s3.bucketTypePrivate": "<PERSON><PERSON><PERSON><PERSON>", "settings.media.s3.bucketTypePublic": "Publiczny", "settings.media.s3.key": "Klucz dostępu AWS", "settings.media.s3.publicURL": "Niestandardowy publiczny URL (opcjonalnie)", "settings.media.s3.publicURLHelp": "Niestandardowa domena S3 do użycia dla linków do obrazów zamiast domyślnego adresu URL backendu S3.", "settings.media.s3.region": "Region", "settings.media.s3.secret": "<PERSON><PERSON><PERSON> dostępu AWS", "settings.media.s3.uploadExpiry": "Wygaśnięcie przesyłania", "settings.media.s3.uploadExpiryHelp": "(Opcjonalne) Zdefiniuj TTL (w sekundach) dla wygenerowanego podpisanego URL. Tylko dla prywatnych komór (bucketów) (s, m, h, d dla sekund, minut, godzin, dni).", "settings.media.s3.url": "S3 backend URL", "settings.media.s3.urlHelp": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ko, j<PERSON><PERSON><PERSON> używasz niestandardowego backendu kompatybilnego z S3, takiego jak <PERSON>.", "settings.media.title": "Wysyłka mediów", "settings.media.upload.path": "Ścieżka do wysyłki", "settings.media.upload.pathHelp": "Ścieżka do folderu do którego media będą wrzucane.", "settings.media.upload.uri": "URI wysyłki", "settings.media.upload.uriHelp": "URI do wysyłki jest widoczna dla świata zewnętrznego. Wrzucone media do upload_path będą publicznie dostępne pod {root_url} np https://listmonk.yoursite.com/uploads.", "settings.messengers.maxConns": "Maksymalna liczba połąćzeń", "settings.messengers.maxConnsHelp": "Maksymalna liczba jednoczesnych połączeń do serwera.", "settings.messengers.messageSaved": "Ustawienia zapisane. Przeładowuję aplikację...", "settings.messengers.name": "Komunikatory", "settings.messengers.nameHelp": "np: my-sms. Alfanumeryczne / myślnik.", "settings.messengers.password": "<PERSON><PERSON><PERSON>", "settings.messengers.retries": "Ponowne próby", "settings.messengers.retriesHelp": "Liczba ponownych prób przed niepowodzeniem.", "settings.messengers.skipTLSHelp": "Pomiń sprawdzanie nazwy hosta w certyfikacie TLS.", "settings.messengers.timeout": "<PERSON><PERSON>", "settings.messengers.timeoutHelp": "<PERSON>zas czekania na nową aktywność na połączeniu przed jej zamknięciem i usunięciem z puli (s dla sekud, m dla minut)", "settings.messengers.url": "URL", "settings.messengers.urlHelp": "Bazowy URL serwera Postback.", "settings.messengers.username": "Nazwa użytkownika", "settings.needsRestart": "Ustawienia zmienione. Zatrzymaj wszystkie aktywne kampanie i uruchom ponownie aplikację", "settings.performance.batchSize": "<PERSON><PERSON><PERSON><PERSON>", "settings.performance.batchSizeHelp": "Liczba subskrybentów do pobrania z bazy danych przy jednej iteracji. Każda iteracja pobiera subskrybentów z bazy danych, wysyła do nich wiadomości, a następnie przechodzi do następnej iteracji. W idealnym przypadku powinno to być większe niż maksymalna przepustowość (liczba wątków * prędkość wysyłania wiadomości)", "settings.performance.concurrency": "Wielow<PERSON><PERSON><PERSON><PERSON><PERSON>ć", "settings.performance.concurrencyHelp": "Maks<PERSON>alna liczba jednoczesnych workerów (wątków), która będzie wysyłała wiadomości jednocześnie.", "settings.performance.maxErrThreshold": "Maksymalny prób błędu", "settings.performance.maxErrThresholdHelp": "Liczba błędów (np: SMTP timeout), która będzie tolerowana przez aktywną kampanię. Po jej przekroczeniu zostanie zatrzymana w celu sprawdzenia przyczyny. Ustaw 0, ż<PERSON>y nigdy nie przerywać.", "settings.performance.messageRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć wysyłania wiadomości", "settings.performance.messageRateHelp": "Maximum number of messages to be sent out per second per worker in a second. If concurrency = 10 and message_rate = 10, then up to 10x10=100 messages may be pushed out every second. This, along with concurrency, should be tweaked to keep the net messages going out per second under the target message servers rate limits if any.", "settings.performance.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.performance.slidingWindow": "Włącz limit dla okna czasowego", "settings.performance.slidingWindowDuration": "Czas trwania", "settings.performance.slidingWindowDurationHelp": "<PERSON>zas trwania okna czasowego (m dla minut, h dla godzin).", "settings.performance.slidingWindowHelp": "Ustaw ograniczenie dla wia<PERSON>, które są wysyłane w danym okresie czasu. Po osiągnięciu limitu wiadomości zostaną w<PERSON>rzymane, aż okno czasowe stanie się znowu dostępne.", "settings.performance.slidingWindowRate": "Maks<PERSON>alna liczba wiadomości", "settings.performance.slidingWindowRateHelp": "Maksymalna liczba wiadomości podczas okna czasowego.", "settings.privacy.allowBlocklist": "Zezwól na blokowanie", "settings.privacy.allowBlocklistHelp": "<PERSON><PERSON> <PERSON> subskrybentom na wypisywanie się z wszystkich list mailowych i oznaczenie siebie jako zablokowanych?", "settings.privacy.allowExport": "Zezwól na eksportowanie danych", "settings.privacy.allowExportHelp": "<PERSON><PERSON> z<PERSON><PERSON> subskrybentom na eksportowanie danych zebranych o nich?", "settings.privacy.allowPrefs": "Allow preference changes", "settings.privacy.allowPrefsHelp": "Allow subscribers to change preferences such as their names and multiple list subscriptions.", "settings.privacy.allowWipe": "Zezwól na czyszczenie danych", "settings.privacy.allowWipeHelp": "<PERSON><PERSON> z<PERSON><PERSON><PERSON> subskrybentom na usuwanie ich samych razem z wszystkimi ich danymi? Wyświetlenia i liczba kliknięć zostaną zachowane, ale zostaną z nich usunięte informacje kto wykonał tę akcję.", "settings.privacy.domainBlocklist": "Lista zablokowanych domen", "settings.privacy.domainBlocklistHelp": "Adresy e-mail z tymi domenami nie mogą subskrybować. Wprowadź jedną domenę w każdym wierszu, np.: domena.com", "settings.privacy.individualSubTracking": "Śledzenie indywidualnych subskrybentów", "settings.privacy.individualSubTrackingHelp": "Śledź dane wyświetleń i kliknięć na poziomie pojedynczego subskrybenta. Je<PERSON>li wyłączone dane będą nadal zbierane, ale niepowiązane ze subskrybentami.", "settings.privacy.listUnsubHeader": "Dodawaj nagłówek `List-Unsubscribe`", "settings.privacy.listUnsubHeaderHelp": "Dodaj nagłówki do wypisania się z subskrypcji. Niektóre programy pocztowe umożliwiają wypisanie się jednym kliknięciem.", "settings.privacy.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.restart": "<PERSON><PERSON>", "settings.smtp.customHeaders": "Niestandardowe nagłówki", "settings.smtp.customHeadersHelp": "Opcjonalna lista nagłówków do zamieszczania w wiadomościach we wszystkich wiadomościach wysłanych z tego serwera. np: [{\"X-Custom\": \"value\"}, {\"X-Custom2\": \"value\"}]", "settings.smtp.enabled": "Włą<PERSON><PERSON>", "settings.smtp.heloHost": "Nazwa hosta HELO", "settings.smtp.heloHostHelp": "Opcjonalne. Niektóre serwery SMTP wymagają FQDN w nazwie hosta. Domyślnie HELLO korzystają z `localhost`. Ustaw jeśli inny host powinien zostać użyty.", "settings.smtp.name": "SMTP", "settings.smtp.retries": "Ponowne próby", "settings.smtp.retriesHelp": "Liczba ponownych prób przy niepowodzeniu", "settings.smtp.sendTest": "Wyślij e-mail", "settings.smtp.setCustomHeaders": "Ustaw niestandardowe nagłówki", "settings.smtp.testConnection": "Przetestuj połączenie", "settings.smtp.testEnterEmail": "Wpisz hasło w celu przetestowania", "settings.smtp.toEmail": "Adres e-mail odbiorcy", "settings.title": "Ustawienia", "settings.updateAvailable": "<PERSON>a wersja {version} jest dost<PERSON>.", "subscribers.advancedQuery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribers.advancedQueryHelp": "Częściowe zapytania SQL w celu pobrania atrybutów subskrybentów", "subscribers.attribs": "Atrybuty", "subscribers.attribsHelp": "Atrybuty są definiowane jako mapa w JSON, np:", "subscribers.blocklistedHelp": "Zablokowani subskrybenci nigdy nie dostaną żadnego emaila.", "subscribers.confirmBlocklist": "<PERSON><PERSON> {num} subskrybentów?", "subscribers.confirmDelete": "<PERSON><PERSON><PERSON><PERSON> {num} subskrybentów?", "subscribers.confirmExport": "Wyeksportować {num} subskrybentów?", "subscribers.domainBlocklisted": "<PERSON><PERSON> adresu e-mail jest zab<PERSON>ko<PERSON>.", "subscribers.downloadData": "<PERSON><PERSON><PERSON> dane", "subscribers.email": "Email", "subscribers.emailExists": "Email j<PERSON>.", "subscribers.errorBlocklisting": "Błąd blokowania subskrybentów: {error}", "subscribers.errorNoIDs": "<PERSON>e podano identyfikatorów.", "subscribers.errorNoListsGiven": "<PERSON><PERSON> podano list.", "subscribers.errorPreparingQuery": "Błąd przygotowywania zapytania o subskrypcje: {error}", "subscribers.errorSendingOptin": "Błąd wysyłania maila opt-in.", "subscribers.export": "Eksport", "subscribers.invalidAction": "Nieprawidłowa akcja.", "subscribers.invalidEmail": "Nieprawidłowy email.", "subscribers.invalidJSON": "Nieprawidłowy JSON w atrybutach.", "subscribers.invalidName": "Nieprawidłowa nazwa.", "subscribers.listChangeApplied": "Zmiana listy wykonana.", "subscribers.lists": "Listy", "subscribers.listsHelp": "Listy z których subskrybenci wypisali się sami nie mogą zostać usunięte.", "subscribers.listsPlaceholder": "Listy do subskrypcji", "subscribers.manageLists": "Zarządzaj listami", "subscribers.markUnsubscribed": "Oznacz jako o<PERSON><PERSON><PERSON>", "subscribers.newSubscriber": "Nowy subskrybent", "subscribers.numSelected": "Wybrano {num} subskrypcji", "subscribers.optinSubject": "Potwierdź subskrypcję", "subscribers.preconfirm": "Wstępnie zatwierdzaj subskrypcje", "subscribers.preconfirmHelp": "Nie wysyłaj maili z potwierdzeniem subskrybcji i oznacz wszystkie zapisy jako 'zasubskrybowane'.", "subscribers.query": "Zapytanie", "subscribers.queryPlaceholder": "E-mail lub nazwa", "subscribers.reset": "<PERSON><PERSON><PERSON><PERSON>", "subscribers.selectAll": "<PERSON><PERSON><PERSON><PERSON> wszystkich {num}", "subscribers.sendOptinConfirm": "<PERSON>yś<PERSON>j potwierdzenie opt-in", "subscribers.sentOptinConfirm": "Potwierdzenie opt-in wysłane", "subscribers.status.blocklisted": "Zablokowany", "subscribers.status.confirmed": "Potwierdzony", "subscribers.status.enabled": "Aktywny", "subscribers.status.subscribed": "Subskrybuje", "subscribers.status.unconfirmed": "Niepotwierdzony", "subscribers.status.unsubscribed": "Odsubskrybowany", "subscribers.subscribersDeleted": "Usunięto {num} subskrybentów", "templates.cantDeleteDefault": "<PERSON><PERSON> można us<PERSON> domyślnego szablonu", "templates.default": "Domyślny", "templates.dummyName": "Fikcyjna kampania", "templates.dummySubject": "<PERSON><PERSON>", "templates.errorCompiling": "Błąd kompilacji szablonu: {error}", "templates.errorRendering": "Błąd renderowania wiadomości: {error}", "templates.fieldInvalidName": "Nieprawidłowa długość dla nazwy.", "templates.makeDefault": "Ustaw jako <PERSON>", "templates.newTemplate": "<PERSON><PERSON> s<PERSON>", "templates.placeholderHelp": "Symbol zastępczy {placeholder} powinien występować dokładnie raz w szablonie.", "templates.preview": "Podgląd", "templates.rawHTML": "Surowy HTML", "templates.subject": "<PERSON><PERSON>", "users.login": "<PERSON><PERSON><PERSON><PERSON>", "users.logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}