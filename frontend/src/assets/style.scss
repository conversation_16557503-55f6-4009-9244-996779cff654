/* Import Bulma to set variables */
@import "~bulma/sass/utilities/_all";

/* import inter-regular */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  src: local(''),
       url('assets/fonts/Inter-Regular.woff2') format('woff2'),
}

/* import inter-600 */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  src: local(''),
       url('assets/fonts/Inter-Bold.woff2') format('woff2'),
}

$body-family: "Inter", "Helvetica Neue", sans-serif;
$body-size: 15px;
$background: $white-bis;
$body-background-color: $white-bis;
$primary: #0055d4;
$green: #0db35e;
$turquoise: $green;
$red: #FF5722;

$link: $primary;
$input-placeholder-color: $grey-light;
$grey-lightest: #eaeaea;

$colors: map-merge($colors, (
    "turquoise": ($green, $green-invert),
    "green": ($green, $green-invert),
    "success": ($green, $green-invert),
    "danger": ($red, $green-invert),
));

$sidebar-box-shadow: none;
$sidebar-width: 240px;
$menu-item-active-background-color: $white-bis;
$menu-item-active-color: $primary;

/* Buefy */
$modal-background-background-color: rgba(0, 0, 0, .30);

/* Import full Bulma and Buefy */
@import "~bulma";
@import "~buefy/src/scss/buefy";

/* Custom style overrides */
html, body {
  height: 100%;
}

code {
  color: $grey;
}

pre {
  background: none;
  border: 1px solid $grey-lightest;
}

ul.no {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.relative {
  position: relative;
}

.content pre {
  white-space: pre-wrap;
}

section {
  &.wrap {
    max-width: 1100px;
  }
}

.spinner.is-tiny {
  display: inline-block;
  height: 10px;
  width: 10px;
  position: relative;

  .loading-overlay {
    .loading-background {
      background: none;
    }
    .loading-icon::after {
      width: 10px;
      height: 10px;
      top: 0;
      left: 0;
      position: static;
    }
  }
}

.is-disabled {
  opacity: 0.30;
}

.box {
  background: $white;
  box-shadow: 2px 2px 0 #f3f3f3;
  border: 1px solid #e6e6e6;
}
  .box hr {
    background-color: #efefef;
  }


/* Two column sidebar+body layout */
#app {
  min-height: 100%;
  .wrapper {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
    margin-top: 0px;
  }

  .sidebar {
    flex-shrink: 1;
    box-shadow: 0 0 3px $grey-lighter;
    background: $white;
  }

  .main {
    background: $white;
    margin-left: 15px;
    padding: 30px;
    flex-grow: 1;
    position: relative;
  }
}

.navbar {
  box-shadow: 0 0 3px $grey-lighter;
}
.navbar-brand {
  padding: 0 0 0 25px;
  .favicon {
    display: none;
  }
  .full {
    max-height: 17px;
    margin-top: 15px;
  }
  .favicon {
    margin-top: 12px;
    max-height: 24px;
  }
}

.b-sidebar {
  position: sticky;
  top: 75px;

  .sidebar-content {
    background: transparent;
  }
  .menu-list {
    .router-link-exact-active {
      border-right: 5px solid $primary;
      outline: 0 none;
    }
    li ul {
      margin-right: 0;
    }
    > li {
      margin-bottom: 10px;
      a {
        padding-left: 25px;
      }
    }
    a {
      border-radius: 0;
    }
  }
}

/* Fix for sidebar jumping on modals */
body.is-noscroll {
  position: static;
  overflow-y: visible;
  width: auto;
}

/* Global notices */
.global-notices {
  margin-bottom: 30px;
}
.notification {
  padding: 10px 15px;
  border-left: 5px solid #eee;

  &.is-danger {
    background: $white-ter;
    color: $black;
    border-left-color: $red;
  }
  &.is-success {
    background: $white-ter;
    color: $black;
    border-left-color: $green;
  }
}

/* WYSIWYG / HTML code editor */
.html-editor {
  position: relative;
  width: 100%;
  min-height: 250px;
  height: 65vh;
  border: 1px solid $grey-lighter;
  border-radius: 2px;
}

.plain-editor textarea {
  height: 65vh;
}

.alt-body textarea {
  height: 30vh;
}


.editor {
  margin-bottom: 30px;

  .tox-tinymce {
    box-shadow: 2px 2px 0 #f3f3f3;
    border: 1px solid #e6e6e6;
    border-radius: 3px;
  
    .tox-toolbar__primary {
      border-color: #e6e6e6 !important;
    }
  }
  .tox-statusbar__branding {
    display: none;
  }
  .tox .tox-statusbar {
    border: 0;
  }

  .tox-tinymce--toolbar-sticky-on .tox-editor-header {
    padding-top: 48px !important;
  }

}


.tox.tox-silver-sink {
  z-index: 850;

  .tox-dialog-wrap {
    z-index: 900;
  }
  .tox-dialog {
    @extend .box;
  }

  .tox-track-link {
    display: block;
    cursor: pointer;

    margin: 5px 0 10px 0;
    input {
      margin-right: 5px;
    }
  }

  .tox-button {
    border-radius: 4px;
  }
  .tox-button:not(.tox-button--secondary):not(.tox-button--naked) {
    background: $primary;
  }

  .tox-textfield {
    box-shadow: 2px 2px 0 $white-ter;
    border: 1px solid $grey-lighter;
  }
}

.tox, .tox-tinymce, .tox-tinymce-aux, .tox .tox-button, .tox .tox-button-link, .tox .tox-dialog__title,
  .tox .tox-textfield,
  .tox .tox-toolbar-textfield,
  .tox .tox-listboxfield .tox-listbox--select,
  .tox .tox-textarea,
  .tox .tox-selectfield select {
    /* All TinyMCE components */
    font-family: $body-family !important;
  }

/* Table colors and padding */
.main table {
  thead th {
    background: $white-bis;
    border-bottom: 1px solid $grey-lighter;
  }
  thead th, tbody td {
    padding: 15px 10px;
    border-color: $grey-lightest;
  }

  .actions a, .actions .a {
    margin: 0 10px;
    display: inline-block;
  }
  .actions a[data-disabled],
  .actions .icon[data-disabled] {
    pointer-events: none;
    cursor: not-allowed;
    color: $grey-light;
  }

  td {
    .fields {
      font-size: $size-7;

      p {
        line-height: 0.775rem;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      label {
        font-weight: bold;
        text-align: left;
        padding-bottom: 6px;
        min-width: 100px;
      }
      span {
        display: inline-block;
      }
      [class*=" mdi-"]::before {
        margin-left: 0;
      }
    }

  }
}

/* Modal */
.modal {
  z-index: 950;

  &.has-overflow {
    overflow: auto !important;
    .modal-content {
      overflow: visible !important;
    }
    .modal-card {
      overflow: visible !important;
    }
    .modal-card-body {
      overflow: visible !important;
    }
  }
}
.modal-background {
  background: rgba(255, 255, 255, 0.7);
}
.modal-content, .modal.dialog .modal-card {
  background: $white;
  box-shadow: 2px 2px 3px #e4e4e4;
  border: 1px solid #e5e5e5;
  padding: 0;
}
.modal-card-head {
  display: block;
}
.modal .modal-card-foot {
  justify-content: flex-end;
}
.modal .modal-close.is-large {
  display: none;
}


/* Table */
.b-table .level-left {
  min-width: 60%;
  display: block;

  .actions .a {
    display: inline-block;
    margin-right: 30px;
  }
}


/* Fix for input colours */
.button {
  &.is-primary {
    background: $primary;

    &:hover {
      background: darken($primary, 15%);
    }
    &:disabled {
      background: $grey-light;
    }
  }

  &:not(.is-small) {
    height: auto;
    padding: 10px 20px;
  }
}

.has-addons {
  .controls .button.is-primary {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .input:hover {
    box-shadow: none;
  }
}

.autocomplete {
  .dropdown-content {
    background-color: $white-bis;
    border: 1px solid $primary;
  }
  a.dropdown-item {
    font-size: $size-6;
    &:hover, &.is-hovered {
      background-color: $grey-lightest;
      color: $primary;
    }
  }
  .dropdown-menu {
    top: 92%;
  }
  .dropdown-menu.is-opened-top {
    top: auto;
    bottom: 92%;
  }
}

.input, .taginput .taginput-container.is-focusable, .textarea {
  box-shadow: 2px 2px 0 $white-ter;
  border: 1px solid $grey-lighter;
}

.select:not(.is-multiple) {
  height: auto;
}

.input, .select select {
  height: auto;
  padding: 10px 12px;
}
  .control.has-icons-left .icon.is-left {
    height: 3rem;
  }

.taginput .taginput-container .autocomplete {
  input {
    padding: 16px 32px 17px 32px;
  }

  .control.has-icons-left .icon.is-left {
    height: 2rem;
  }
}

/* Form fields */
.field {
  &:not(:last-child) {
    margin-bottom: 2rem;
  }

  .control {
    position: relative;

    .help.counter {
      position: absolute;
      top: -20px;
      right: 0;
    }
  }

  label {
    color: $grey-dark;
  }

  .help {
    color: $grey;
  }
}
.has-numberinput .field, .field.is-grouped {
  margin-bottom: 0;
}
.b-numberinput.field.is-grouped div.control {
  flex-grow: 0;
}

/* Tabs */
.b-tabs .tab-content {
  padding-top: 3rem;
}

/* Tags */
.tag {
  min-width: 85px;

  &.is-small {
    font-size: 0.65rem;
    background: $white-ter;
    border: 1px solid $white-ter;
    padding: 3px 5px;
    min-width: auto !important;
  }

  &:not(body) {
    font-size: 0.85em;
    $color: $grey-lighter;
    border: 1px solid $color;
    box-shadow: 1px 1px 0 $color;
    color: $grey;
  }

  &.private, &.scheduled, &.paused, &.tx {
    $color: #ed7b00;
    color: $color;
    background: #fff7e6;
    border: 1px solid lighten($color, 37%);
    box-shadow: 1px 1px 0 lighten($color, 37%);
  }
  &.public, &.running, &.list, &.campaign {
    $color: $primary;
    color: lighten($color, 20%);;
    background: #e6f7ff;
    border: 1px solid lighten($color, 42%);
    box-shadow: 1px 1px 0 lighten($color, 42%);
  }
  &.finished, &.enabled {
    $color: $green;
    color: $color;
    background: #f6ffed;
    border: 1px solid lighten($color, 45%);
    box-shadow: 1px 1px 0 lighten($color, 45%);
  }
  &.blocklisted, &.cancelled {
    $color: $red;
    color: $color;
    background: #fff1f0;
    border: 1px solid lighten($color, 30%);
    box-shadow: 1px 1px 0 lighten($color, 30%);
  }

  sup {
    font-weight: $weight-semibold;
    letter-spacing: 0.03em;
  }
  &.unsubscribed sup {
    color: #fa8c16;
  }
  &.confirmed sup {
    color: #52c41a;
  }

  &:not(body) .icon:first-child:last-child {
    margin-right: 1px;
  }
}

/* Page header */
.page-header {
  .buttons {
    justify-content: flex-end;
  }
}

.page-404 h1 {
  font-size: 100px;
}

/* Dashboard */
section.dashboard {
  .title {
    margin-bottom: 0.5rem;
  }

  .tile.notification {
    @extend .box;
    padding: 10px;
  }

  .counts .column {
    padding: 30px;
  }

  .level-item {
    background-color: $white-bis;
    padding: 30px;
    margin: 10px;

    &:first-child, &:last-child {
      margin: 0;
    }
  }

  label {
    font-weight: bold;
    display: inline-block;
    min-width: 50px;
    text-align: right;
  }

  .charts {
    min-height: 200px;
  }

  .notification {
    border-width: 0;
  }
}

/* Lists page */
section.lists {
  td {
    .tag {
      min-width: 65px;
    }
  }
}

/* List selector */
.list-tags {
  margin-bottom: 1rem;
}

/* Subscribers page */
.subscribers {
  .subscribers-controls .buttons {
    margin-top: 15px;
  }

  .toggle-advanced {
    margin-top: 10px;
  }

  .b-table {
    margin-top: 25px;
  }
}

.bounces {
  pre {
    padding: 5px 10px;
  }
}

/* Import page */
section.import {
  .delimiter input {
    max-width: 100px;
  }
  .status {
    padding: 60px;
  }
  .log-view .lines {
    max-height: 240px;
    text-align: left;
  }
}

/* Campaigns page */
section.campaigns {
  table tbody {
    .spinner {
      margin-left: 10px;
      .loading-overlay .loading-icon::after {
        border-bottom-color: lighten(#1890ff, 30%);
        border-left-color: lighten(#1890ff, 30%);
      }
    }

    tr.running {
      background: lighten(#1890ff, 43%);
      td {
        border-bottom: 1px solid lighten(#1890ff, 30%);
      }
    }

    td {
      .tags {
        margin-top: 5px;
      }

      &.lists ul {
        // font-size: $size-7;
        list-style-type: circle;
      }

      &.draft {
        color: $grey-lighter;
      }

      .progress-wrapper {
        .progress.is-small {
          height: 0.4em;
        }
        display: inline-block;
      }
    }
  }
}

section.analytics {
  .charts {
    position: relative;
    min-height: 100px;
  }

  .chart {
    margin-bottom: 45px;
  }

  .donut-container {
    position: relative;
  }
  .donut {
    bottom: 0px;
    right: 0px;
    position: absolute !important;
  }
}

/* Campaign / template preview popup */
.preview {
  padding: 0;

  /* Contain the spinner background in the content area. */
  position: relative;

  #iframe {
    border: 0;
    width: 100%;
    height: 100%;
    min-height: 500px;
    padding: 0;
    margin: 0 0 -5px 0;
  }
}

/* Media gallery */
.media-files {
  .thumbs {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    flex-flow: row wrap;

    .thumb {
      margin: 10px;
      max-height: 90px;
      overflow: hidden;

      position: relative;

      .caption {
        background-color: rgba($white, .70);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 2px 5px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .actions {
        background-color: rgba($white, .70);
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        padding: 2px 5px;
        display: none;

        a {
          margin-left: 10px;
        }
      }

      &:hover .actions {
        display: block;
      }
    }

    .box {
      padding: 10px;
    }
  }
}

/* Template form */
.templates {
  td .tag {
    min-width: 100px;
  }
}
.template-modal {
  .template-modal-content {
    height: 95vh;
    max-height: none;
  }
  .textarea {
    max-height: none;
    height: 55vh;
  }
}

/* Settings */
.settings {
  .disabled {
    opacity: 0.30;
  }
  .box {
    margin-bottom: 30px;
  }
  .html-editor {
    height: auto;
    min-height: 350px;
  }
  .smtp-shortcuts a {
    margin-right: 15px;
    display: inline-block;
  }
}

/* Logs */
.log-view {
  .lines {
    height: 70vh;
    overflow-y: scroll;
    font-family: monospace;
    font-size: 0.90rem;

    padding: 15px;
    border: 1px solid $grey-lightest;

    .line {
      display: block;
      margin-bottom: 2px;
    }

    .timestamp {
      color: $primary;
      display: inline-block;
      min-width: 175px;
      margin-right: 5px;
    }

    .line:hover {
      background: $white-bis;
    }
  }
}

/* C3 charting lib */
.c3 {
  .c3-text.c3-empty {
    font-family: $body-family;
    font-size: $size-6;
  }
  .c3-chart-lines .c3-line {
    stroke-width: 2px;
  }
  .c3-axis-x .tick line,
  .c3-axis-y .tick line {
    display: none;
  }
  text {
    fill: $grey;
    font-family: $body-family;
    font-size: 11px;
  }
  .c3-axis path, .c3-axis line {
    stroke: #eee;
  }

  .c3-tooltip {
    @extend .box;
    padding: 10px;
    empty-cells: show;
    opacity: 0.95;

    tr {
      border: 0;
    }
    th {
      background: $white;
    }
  }
}

/* Vue animations */
.slide-enter-active, .slide-leave-active {
  transition: opacity 50ms;
  max-height: none;
}
.slide-enter, .slide-leave-to {
  transition: opacity 50ms;
  opacity: 0;
  max-height: none;
}
.slide-leave-active, .slide-leave-to {
  transition: none;
  display: none;
}


/* Toasts */
.notices {
  .toast {
    @extend .box;
    border-left: 15px solid $grey;
    border-radius: 3px;
    padding: 20px;

    &.is-danger {
      background: $white;
      border-left-color: $red;
      color: $grey-dark;
    }
    &.is-success {
      background: $white;
      border-left-color: $green;
      color: $grey-dark;
    }
  }
}

@media screen and (max-width: 1500px) {
  .b-table {
    .top.level {
      display: block;
    }
  }
  .b-table .level-left {
    display: block;
    width: 100%;
    min-width: 0;
  }
}

@media screen and (max-width: 1100px) {
  html, body {
    overflow-x: auto;
  }

  #app .main {
    margin-left: 5px;
    padding: 30px 20px 30px 20px;
  }

  .navbar-brand {
    .full {
      display: none;
    }
    .favicon {
      display: block;
    }
    padding-left: 10px;
  }

  .b-sidebar {
    top: 30px;
  }

  /* Hide sidebar menu captions on mobile */
  .b-sidebar .sidebar-content.is-mini-mobile {
    max-width: 90px;
    .menu-list {
      li {
        margin-bottom: 30px;

        span:nth-child(2) {
          display: none;
        }
        .icon.is-small {
          scale: 1.4;
        }
      }
      > li {
        a {
          padding-left: 15px;
        }
      }
    }
  }

  td .tags {
    .tag:not(:last-child) {
      margin-right: 0;
    }
  }

  section.dashboard label {
    min-width: auto;
  }

  /* Tabs */
  nav.tabs.is-boxed ul {
    display: block;
    text-align: left;
    flex-grow: 1;

    li {
      a {
        justify-content: left;
        border: 1px solid $grey-lighter !important;
      }

      &.is-active a {
        border-bottom: 1px solid $grey-lighter !important;
        padding-left: 30px;
      }
    }
  }

  /* Table top-left controls */
  .b-table .level-left {
    .actions .a {
      display: block;
      margin: 0 0 5px 0;
    }
  }
}

@media screen and (max-width: 850px) {
  .page-header .buttons {
    display: block;
  }

  .b-tabs .tab-content {
    padding: 15px 0 0 0;
  }
}

/* On big sizes, keep the header buttons small and non-expanded. */
@media screen and (min-width: 769px) {
  .page-header .button {
    display: inline-block;
    width: auto;
  }
}
