{"name": "listmonk", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-report": "vue-cli-service build --report", "lint": "vue-cli-service lint"}, "dependencies": {"@tinymce/tinymce-vue": "^3", "axios": "^0.27.2", "buefy": "^0.9.10", "c3": "^0.7.20", "codeflask": "^1.4.1", "core-js": "^3.12.1", "dayjs": "^1.10.4", "indent.js": "^0.3.5", "qs": "^6.10.1", "textversionjs": "^1.1.3", "tinymce": "^5.10.7", "turndown": "^7.0.0", "vue": "^2.6.12", "vue-i18n": "^8.22.2", "vue-router": "^3.2.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-plugin-eslint": "~4.5.13", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "~4.5.13", "@vue/eslint-config-airbnb": "^5.3.0", "babel-eslint": "^10.1.0", "cypress": "10.10.0", "cypress-file-upload": "^5.0.2", "eslint": "^7.27.0", "eslint-plugin-import": "^2.23.3", "eslint-plugin-vue": "^7.9.0", "sass": "^1.34.0", "sass-loader": "^10.2.0", "vue-template-compiler": "^2.6.12"}}