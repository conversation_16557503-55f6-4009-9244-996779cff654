name: goreleaser

on:
  push:
    tags:
      - "v*" # Will trigger only if tag is pushed matching pattern `v*` (Eg: `v0.1.0`)

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.17

      - name: Login to Docker Registry
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Prepare Dependencies
        run: |
          make dist

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v2
        with:
          version: latest
          args: --parallelism 1 --rm-dist --skip-validate
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
