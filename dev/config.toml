# IMPORTANT: This configuration is meant for development only
### DO NOT USE IN PRODUCTION ###

[app]
# Interface and port where the app will run its webserver.  The default value
# of localhost will only listen to connections from the current machine. To
# listen on all interfaces use '0.0.0.0'. To listen on the default web address
# port, use port 80 (this will require running with elevated permissions).
address = "0.0.0.0:8082"

# BasicAuth authentication for the admin dashboard. This will eventually
# be replaced with a better multi-user, role-based authentication system.
# IMPORTANT: Leave both values empty to disable authentication on admin
# only where an external authentication is already setup.
admin_username = "listmonk"
admin_password = "listmonk"

# Database.
[db]
host = "localhost"
port = 5432
user = "postgres"
password = "password@123"
query_timeout = 100000

# Ensure that this database has been created in Postgres.
database = "hdfc-uat"

ssl_mode = "disable"
max_open = 25
max_idle = 25
max_lifetime = "300s"

# Optional space separated Postgres DSN params. eg: "application_name=listmonk gssencmode=disable"
params = ""



[serverConfig]
natsUrl = "nats://localhost:4222"
natsReplica = 1
natsMaxBytes=52428800
natsMaxAge="10d"

skipSSLCheck = true

maxConcurrentCampaigns=4
serverUrl = "https://uat-4.mintoak.com/"
userTerminalInfoApiUrl = "https://uat-4.mintoak.com/HDFCMOB/user-terminal-info"
oneAppAuthUrl = "https://uat-4.mintoak.com/"
segmentServiceUrl= "https://uat-4.mintoak.com/"
soundboxServiceApiUrl="https://uat-4.mintoak.com/soundbox/internal/linked-devices"
mqttEnabled=false

redisHost = "localhost:6379"
isLocal = true
redisPoolSize = 10
maxVideoSizeInMb = 1
maxAudioSizeInMb = 2

firebaseFilePath = "/home/<USER>/work/go/projects/serviceAccount.json"

aesAccessKey = "${aws.access.key}"
aesSecretKey = "${aws.secretkey}"
aesKeyId = "${aws.kms.key.id}"
bucketName = "hdfccommunicationuat"
region = "ap-south-1"

testEnv = false
changeLogEnabled = true
[gatewayConfig]
sms=["acl"]
email=["acl"]
[gatewayConfig.property.sms.acl]
appid = "hbalt4"
userId = "hbalt4"
pass = "hbalt_44"
