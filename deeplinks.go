package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

// Get All CTA Deep Links
func handleGetAllDeepLinks(c echo.Context) error {
	var (
		app           = c.Get("app").(*App)
		logger        = c.Get("logger").(push.Logger)
		deeplinkId, _ = strconv.Atoi(c.Param("id"))
		out           models.PageResults
	)
	single := false
	if deeplinkId > 0 {
		single = true
	}
	if single {
		out, err := app.core.GetDeepLink(deeplinkId, logger)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, okResp{out})
	}
	res, err := app.core.GetAllDeepLinks(logger)
	if err != nil {
		return err
	}
	if single && len(res) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.notFound", "name", "{globals.terms.deeplinks}"))
	}
	if single {
		return c.JSON(http.StatusOK, okResp{res[0]})
	}
	out.Results = res
	return c.JSON(http.StatusOK, out)
}

// Create New CTA Deep Link
// func handleCreateCTADeepLink(c echo.Context) error {

// }

// Update Existing CTA Deep Link
// func handleUpdateCTADeepLink(c echo.Context) error {

// }

// Delete CTA Deep Link
// func handleDeleteCTADeepLink(c echo.Context) error {

// }

func handleImportDeeplinks(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)

	file, err := c.FormFile("file")
	ctx := tracer.WrapEchoContextLogger(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid file: "+err.Error())
	}

	src, err := file.Open()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "error opening file: "+err.Error())
	}
	defer src.Close()

	var deeplinks []models.DeepLinks
	if strings.HasSuffix(file.Filename, ".xlsx") {
		deeplinks, err = processExcelFile(src, logger, ctx)
	} else if strings.HasSuffix(file.Filename, ".csv") {
		deeplinks, err = processCSVFile(src, logger)
	} else {
		return echo.NewHTTPError(http.StatusBadRequest, "Unsupported file format. Please use .xlsx or .csv")
	}

	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Error processing file: "+err.Error())
	}

	results, err := app.core.ImportClientDeepLinks(deeplinks, logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{results})
}

func processExcelFile(file io.Reader, logger push.Logger, ctx context.Context) ([]models.DeepLinks, error) {
	// Save to a temporary file since excelize requires a file path
	tmpFile, err := ioutil.TempFile("", "*.xlsx")
	if err != nil {
		return nil, err
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// Copy the uploaded file to the temp file
	if _, err := io.Copy(tmpFile, file); err != nil {
		return nil, err
	}
	tmpFile.Close() // Close before reading with excelize

	// Use the ReadExcel function from external package
	rows := external.ReadExcel(tmpFile.Name(), ctx)
	if rows == nil {
		return nil, fmt.Errorf("failed to read Excel file")
	}

	var deeplinks []models.DeepLinks
	for _, row := range rows {
		// Skip rows without required fields
		if row["DeeplinkName"] == "" || row["DeeplinkURL"] == "" {
			continue
		}

		dl := models.DeepLinks{
			Deeplink:     strings.TrimSpace(row["DeeplinkURL"]),
			DeeplinkName: strings.TrimSpace(row["DeeplinkName"]),
			Category:     strings.TrimSpace(row["Category"]),
			IsActive:     true, // Default to active
		}
		deeplinks = append(deeplinks, dl)
	}

	return deeplinks, nil
}

// Process CSV file and extract deeplinks
func processCSVFile(file io.Reader, logger push.Logger) ([]models.DeepLinks, error) {
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = -1 // Allow variable number of fields

	// Read header
	header, err := reader.Read()
	if err != nil {
		return nil, err
	}

	// Map header indices
	headerMap := make(map[string]int)
	for i, h := range header {
		headerMap[strings.ToLower(h)] = i
	}

	// Check required column
	required := []string{"deeplinkurl", "deeplinkname", "category"}
	for _, field := range required {
		if _, ok := headerMap[field]; !ok {
			return nil, fmt.Errorf("missing required column: %s", field)
		}
	}

	// Process rows
	var deeplinks []models.DeepLinks
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logger.Error().Msgf("Error reading CSV row: %v", err)
			continue
		}

		dl := models.DeepLinks{
			IsActive: true,
		}

		// Mandatory fields
		if idx, ok := headerMap["deeplinkurl"]; ok && idx < len(record) {
			dl.Deeplink = strings.TrimSpace(record[idx])
		}
		if idx, ok := headerMap["deeplinkname"]; ok && idx < len(record) {
			dl.DeeplinkName = strings.TrimSpace(record[idx])
		}

		if dl.Deeplink == "" || dl.DeeplinkName == "" {
			continue
		}

		// Optional
		if idx, ok := headerMap["category"]; ok && idx < len(record) {
			dl.Category = strings.TrimSpace(record[idx])
		}

		deeplinks = append(deeplinks, dl)
	}

	return deeplinks, nil
}
