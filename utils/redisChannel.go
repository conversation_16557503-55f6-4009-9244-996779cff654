package utils

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/phuslu/log"

	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	push "github.com/phuslu/log"
)

var (
	redisBroadCastSubject = "listmonk.redis.broadcast.config_updates"
)

func PublishBroadcastEvent(message models.BroadcastPayloadCustom, ctx context.Context) error {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	message.TraceId = tracer.GetTraceIdFromCtx(ctx)

	logger.Info().Msgf("publishing message to redis subject: %s", redisBroadCastSubject)

	jsonData, err := json.Marshal(message)

	if err != nil {
		return err
	}

	cmd := secondaryClient.Publish(ctx, redisBroadCastSubject, jsonData)

	if cmd == nil {
		return fmt.Errorf("failed to publish message: got nil cmd")
	}

	if cmd.Err() != nil {
		return cmd.Err()
	}

	logger.Info().Msgf("Broadcasted to redis channel '%s': %s", redisBroadCastSubject, jsonData)
	return nil
}

func RedisBroadcastConsumer() {
	pubsub := secondaryClient.Subscribe(ctx, redisBroadCastSubject)
	defer pubsub.Close()

	if _, err := pubsub.Receive(ctx); err != nil {
		log.Fatal().Msgf("subscribe error: %v", err)
	}
	log.Info().Msgf("subscribed to redis subject: %v", redisBroadCastSubject)

	ch := pubsub.Channel()
	for msg := range ch {
		var event models.BroadcastPayloadCustom

		if err := json.Unmarshal([]byte(msg.Payload), &event); err != nil {
			log.Error().Msgf("Failed to unmarshal JSON: %v", err)
			continue
		}

		ctx := tracer.GetCustomTracingContext(event.TraceId)
		broadCastLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
		broadCastLogger.Info().Msgf("RedisBroadcast Consumer = %s recieved messsage from nats: %v", redisBroadCastSubject, msg.Payload)

	}
}
