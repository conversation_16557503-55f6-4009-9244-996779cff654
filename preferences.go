package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/knadh/listmonk/models"
	"github.com/labstack/echo/v4"
	push "github.com/phuslu/log"
)

type Subscriber struct {
	ID   int64  `db:"id" json:"id"`
	Name string `db:"name" json:"name"`
	Type string `db:"type" json:"type"`
	UUID string `db:"uuid" json:"uuid"`
}

// 	E101 -> Sorry! Some internal Server Error occurred, Please try after some time
// E105 -> Session Expired
// E106 -> Decryption Failed
// S101 -> Success
// E109 -> Bank API gateway error
// P101 -> Invalid request

// handleCreatePrefences retrieves categories with additional metadata like subscriber counts. This may be slow.
func handleCreatePreferences(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		pref   = []models.PreferenceRequest{}
		logger = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&pref); err != nil {
		logger.Error().Msgf("error occured while marshalling the request %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}
	if err := handleCreatePreferencesBulk(app, c, pref, logger); err != nil {
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}
	return nil
}

func getSubscriberPref(subId int64, catId int, messenger string, value bool) models.SubscriberPreferences {
	return models.SubscriberPreferences{
		SubscriberId: subId,
		CategoryId:   catId,
		Channel:      messenger,
		Value:        value,
	}
}

func handleGetPreferences(c echo.Context) error {
	var (
		app     = c.Get("app").(*App)
		request = []models.PreferenceGetRequest{}
		logger  = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&request); err != nil {
		logger.Error().Msgf("error occured while marshalling the request %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}

	data, _ := json.Marshal(request)

	logger.Info().Msgf("got request for getsubscriber preferences: %s", string(data))
	data = nil

	uniqueSubscribers := make(map[string]bool)
	for _, singlePref := range request {
		//check for validations
		uniqueSubscribers[singlePref.Name+"_"+singlePref.Type] = true
	}

	if len(uniqueSubscribers) == 0 {
		logger.Info().Msgf("No subscribers found in request")
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Invalid request",
			StatusCode:  "P101",
		})
	}

	subsString := getSubQueryString(uniqueSubscribers)

	subscriberList, err := fetchSubscribers(subsString)

	if err != nil {
		logger.Error().Msgf("Error occurred while fetching the subscribers %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}

	subMap := make(map[string]int64)

	var subIds []int64

	for _, s := range subscriberList {
		subMap[s.Name+"_"+s.Type] = s.ID
		subIds = append(subIds, s.ID)
	}
	subPrefs := []models.SubscriberPreferences{}
	if len(subIds) != 0 {
		subPrefs, err = findSubscriberPreferences(subIds)
		if err != nil {
			logger.Error().Msgf("Error occured while fetching subscriber preferences %v", err)
			return c.JSON(http.StatusOK, prefResponse{
				Status:      "Failure",
				RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
				StatusCode:  "E101",
			})
		}
	}

	subcatMap := make(map[string]bool)

	for _, s := range subPrefs {
		subcatMap[fmt.Sprintf("%d_%d_%s", s.SubscriberId, s.CategoryId, s.Channel)] = s.Value
	}

	categories := app.manager.GetAllCatFromMap()

	if len(categories) == 0 {
		logger.Error().Msgf("Error occured while fetching categories %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}

	var response []models.PreferenceRequest

	defaultCat := getDefaults(categories)

	for _, singlePref := range request {

		subId, has := subMap[singlePref.Name+"_"+singlePref.Type]
		var pref []models.PreferenceDTO
		if !has {
			pref = defaultCat
			copy(pref, defaultCat)
		} else {
			for _, value := range categories {

				var channel models.RequestChannels
				isVisible := false

				if value.Channels.Email.IsVisible {
					isVisible = true
					subCat, p := subcatMap[fmt.Sprintf("%d_%d_%s", subId, value.ID, "email")]

					if !p {
						subCat = value.Channels.Email.Value
					}

					channel.Email = &models.ChannelOptions{
						IsVisible:    true,
						IsToggleable: value.Channels.Email.IsToggleable,
						Value:        subCat,
					}
				}
				if value.Channels.FCM.IsVisible {
					isVisible = true
					subCat, p := subcatMap[fmt.Sprintf("%d_%d_%s", subId, value.ID, "fcm")]

					if !p {
						subCat = value.Channels.FCM.Value
					}

					channel.FCM = &models.ChannelOptions{
						IsVisible:    true,
						IsToggleable: value.Channels.FCM.IsToggleable,
						Value:        subCat,
					}
				}
				if value.Channels.SMS.IsVisible {
					isVisible = true
					subCat, p := subcatMap[fmt.Sprintf("%d_%d_%s", subId, value.ID, "sms")]

					if !p {
						subCat = value.Channels.SMS.Value
					}

					channel.SMS = &models.ChannelOptions{
						IsVisible:    true,
						IsToggleable: value.Channels.SMS.IsToggleable,
						Value:        subCat,
					}
				}

				if isVisible {
					cat := models.PreferenceDTO{
						Name:     value.Name,
						ID:       value.ID,
						Channels: channel,
					}

					pref = append(pref, cat)
				}

			}
		}
		response = append(response, models.PreferenceRequest{
			Name:        singlePref.Name,
			Type:        singlePref.Type,
			Preferences: pref,
		})
	}

	return c.JSON(http.StatusOK, prefResponse{
		Status:      "Success",
		RespMessage: "Success",
		StatusCode:  "S101",
		Data:        response,
	})

}

func getDefaults(categories map[int]models.PreferenceDTO) []models.PreferenceDTO {
	var pref []models.PreferenceDTO

	for _, value := range categories {

		var channel models.RequestChannels
		isVisible := false
		if value.Channels.Email.IsVisible {
			isVisible = true
			channel.Email = value.Channels.Email
		}
		if value.Channels.SMS.IsVisible {
			isVisible = true
			channel.SMS = value.Channels.SMS
		}
		if value.Channels.FCM.IsVisible {
			isVisible = true
			channel.FCM = value.Channels.FCM
		}

		if isVisible {
			pref = append(pref, models.PreferenceDTO{
				Name:     value.Name,
				ID:       value.ID,
				Channels: channel,
			})
		}

	}
	return pref
}

func insertPreferences(requiredPrefs []models.SubscriberPreferences, logger push.Logger) error {

	tx, err := db.Beginx()
	if err != nil {
		logger.Error().Msgf("Error starting transaction to add preferences %v", err)
		return err
	}

	row, err := tx.NamedQuery(`INSERT INTO subscriber_preferences(subscriber_id, category_id, channel, value, created_at, updated_at) values(:subscriber_id, :category_id, :channel,:value,now(), now()) on conflict(subscriber_id, category_id,channel) do update set value =Excluded.value, updated_at = now() WHERE subscriber_preferences.value <> EXCLUDED.value returning subscriber_id`, requiredPrefs)
	if err != nil {
		logger.Error().Msgf("Error occured %v", err)
		tx.Rollback()
		return err
	}
	defer row.Close()

	if err := row.Err(); err != nil {
		logger.Error().Msgf("Error occured while inserting subscriber preferences %v", err)
		tx.Rollback()
		return err
	}

	tx.Commit()
	logger.Info().Msgf("Successfully added %d preferences ", len(requiredPrefs))
	return nil

}

func insertNewSubscribers(newSubscribers []Subscriber, logger push.Logger) ([]Subscriber, error) {

	newlyAdded := []Subscriber{}
	tx, err := db.Beginx()
	if err != nil {
		logger.Error().Msgf("Error starting transaction to add preferences %v", err)
		return newlyAdded, err
	}

	row, err := tx.NamedQuery(`INSERT INTO subscribers(uuid, name, type, status) values(:uuid, :name, :type,'enabled') on conflict(name, type) do nothing returning id, name, type`, newSubscribers)

	if err != nil {
		logger.Error().Msgf("Error occured %v", err)
		tx.Rollback()
		return newlyAdded, err
	}
	defer row.Close()

	if err := row.Err(); err != nil {
		logger.Error().Msgf("Error occured while inserting subscribers %v", err)
		tx.Rollback()
		return newlyAdded, err
	}

	for row.Next() {
		var res Subscriber
		row.StructScan(&res)
		newlyAdded = append(newlyAdded, res)

	}
	tx.Commit()
	logger.Info().Msgf("Successfully added %d subscribers ", len(newSubscribers))
	return newlyAdded, nil
}

func findSubscriberPreferences(subIds []int64) ([]models.SubscriberPreferences, error) {

	subPrefs := []models.SubscriberPreferences{}

	str := "("

	length := len(subIds) - 1

	for i, value := range subIds {
		str += strconv.FormatInt(value, 10)
		if i == length {
			str += ")"
		} else {
			str += ","
		}
	}

	err := db.Select(&subPrefs, "select * from subscriber_preferences where subscriber_id in "+str)

	if err != nil {
		return subPrefs, err
	}

	return subPrefs, nil

}

func getCategories(catIds []int, app *App) ([]models.PreferenceDTO, error) {
	categories := []models.PreferenceDTO{}
	for _, id := range catIds {
		pref := app.manager.GetCatFromMap(id)

		categories = append(categories, models.PreferenceDTO{
			Name: pref.Name,
			ID:   pref.ID,
			Channels: models.RequestChannels{
				FCM:   &pref.Channels.FCM,
				SMS:   &pref.Channels.SMS,
				Email: &pref.Channels.Email,
			},
		})
	}
	return categories, nil
}

func getSubQueryString(uniqueSubscribers map[string]bool) string {
	length := len(uniqueSubscribers)
	i := 1
	subsString := "(values"

	for key := range uniqueSubscribers {
		lastIndex := strings.LastIndex(key, "_")
		subsString += "('" + key[:lastIndex] + "'," + "'" + key[lastIndex+1:] + "')"
		if i == length {
			subsString += ") as k (name, type)"
		} else {
			subsString += ","
		}
		i++
	}
	return subsString
}

func getSubQueryStringV2(subs []string, memberType string) string {
	length := len(subs)
	i := 1
	subsString := "(values"

	for key := range subs {

		subsString += "('" + subs[key] + "'," + "'" + memberType + "')"
		if i == length {
			subsString += ") as k (name, type)"
		} else {
			subsString += ","
		}
		i++
	}
	return subsString
}

func handleGetPreferenceHistory(c echo.Context) error {
	var (
		request = models.PreferenceLogsRequest{}
		logger  = c.Get("logger").(push.Logger)
	)

	if err := c.Bind(&request); err != nil {
		logger.Error().Msgf("error occured while marshalling the request %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}

	data, _ := json.Marshal(request)

	logger.Info().Msgf("got request for getPreferenceLogs preferences: %s", string(data))
	data = nil

	uniqueSubscribers := make(map[string]bool)

	for _, singlePref := range request.Request {
		//check for validations
		uniqueSubscribers[singlePref.Name+"_"+singlePref.Type] = true
	}

	if len(uniqueSubscribers) == 0 {
		logger.Info().Msgf("No subscribers found in request")
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Invalid request",
			StatusCode:  "P101",
		})
	}

	subsString := getSubQueryString(uniqueSubscribers)

	subscriberList, err := fetchSubscribers(subsString)

	if err != nil {
		logger.Error().Msgf("Error occurred while fetching the subscribers %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}
	pageSize := request.PageSize

	if pageSize <= 0 {
		pageSize = 20
	}

	pageNo := request.PageNumber

	if pageNo <= 0 {
		pageNo = 1
	}

	if len(subscriberList) == 0 {
		return c.JSON(http.StatusOK, prefResponse{
			Data:        []string{},
			PageNo:      pageNo,
			PageSize:    pageSize,
			Status:      "Success",
			RespMessage: "Success",
			StatusCode:  "S101",
		})
	}

	subMap := make(map[int64]models.PreferenceGetRequest)

	var subIds []int64

	for _, s := range subscriberList {
		subMap[s.ID] = models.PreferenceGetRequest{
			Name: s.Name,
			Type: s.Type,
		}
		subIds = append(subIds, s.ID)
	}

	prefLogs, err := fetchPreferenceLogs(subIds, pageNo, pageSize)

	if err != nil {
		logger.Error().Msgf("Error occurred while fetching preferences %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}

	if len(prefLogs) == 0 {
		return c.JSON(http.StatusOK, prefResponse{
			Data:        []string{},
			PageNo:      pageNo,
			PageSize:    pageSize,
			Status:      "Success",
			RespMessage: "Success",
			StatusCode:  "S101",
		})
	}

	logMap := make(map[string]map[string]map[string]map[string]string)

	var result []map[string]interface{}

	var resultOrder []string

	for _, s := range prefLogs {

		value := "Disabled"

		if s.Value {
			value = "Enabled"
		}

		subIdAndName, has := subMap[s.SubscriberId]

		if !has {
			continue
		}

		logTime := s.FormatedDate.Time.Local().Format("02-01-06 15:04")

		subscriberMap, has := logMap[logTime]

		if !has {
			resultOrder = append(resultOrder, logTime)
			catMap := make(map[string]string)
			catMap[s.CategoryName] = value
			subscriberMap := make(map[string]map[string]map[string]string)
			channelMap := make(map[string]map[string]string)
			channelMap[s.Channel] = catMap
			subscriberMap[subIdAndName.Name] = channelMap
			logMap[logTime] = subscriberMap

		} else {

			sub, has := subscriberMap[subIdAndName.Name]

			if !has {
				catMap := make(map[string]string)
				catMap[s.CategoryName] = value

				channelMap := make(map[string]map[string]string)

				channelMap[s.Channel] = catMap

				subscriberMap[subIdAndName.Name] = channelMap

				logMap[logTime] = subscriberMap
			} else {

				chann, has := sub[s.Channel]

				if !has {
					chann = make(map[string]string)
					chann[s.CategoryName] = value
				} else {
					_, has := chann[s.CategoryName]
					if !has {
						chann[s.CategoryName] = value
					}

				}
				sub[s.Channel] = chann
				subscriberMap[subIdAndName.Name] = sub

			}
			logMap[logTime] = subscriberMap
		}
	}

	for _, value := range resultOrder {
		singleMap := map[string]interface{}{value: logMap[value]}
		result = append(result, singleMap)
	}

	return c.JSON(http.StatusOK, prefResponse{
		Data:        result,
		PageNo:      pageNo,
		PageSize:    pageSize,
		Status:      "Success",
		RespMessage: "Success",
		StatusCode:  "S101",
	})

}

func fetchPreferenceLogs(ids []int64, pageNo int, pageSize int) ([]models.SubscriberPreferenceLogs, error) {

	length := len(ids)
	i := 1
	subsString := "("

	for _, id := range ids {

		subsString += strconv.FormatInt(id, 10)
		if i == length {
			subsString += ") "
		} else {
			subsString += ","
		}
		i++
	}

	subscriberList := []models.SubscriberPreferenceLogs{}
	query := `with dd as (select distinct(created_at) as ddate from subscriber_preference_logs where subscriber_id in`
	query += subsString + `order by ddate desc limit ` + strconv.Itoa(pageSize) + ` offset ` + strconv.Itoa((pageNo-1)*pageSize) + `) `
	query += `select spl.subscriber_id, spl.category_id, c.name as category_name, spl.channel, spl.value, DATE_TRUNC('minute', spl.created_at) AS formatted_datetime `
	query += ` from subscriber_preference_logs spl join categories c on c.id = spl.category_id and c.is_default = false where spl.subscriber_id in `
	query += subsString + ` and spl.created_at in (select ddate from dd) order by spl.created_at desc`
	// query := `select spl.subscriber_id, spl.category_id, c.name as category_name, spl.channel, spl.value, DATE_TRUNC('minute', spl.created_at) AS formatted_datetime from subscriber_preference_logs spl join categories c on c.id = spl.category_id and c.is_default = false where spl.subscriber_id  in`
	// query += subsString + ` group by spl.subscriber_id,spl.category_id, c.name, spl.channel, spl.value, formatted_datetime order by formatted_datetime desc limit ` + strconv.Itoa(pageSize) + ` offset ` + strconv.Itoa((pageNo-1)*pageSize)

	err := db.Select(&subscriberList, query)

	if err != nil {
		return subscriberList, err
	}

	return subscriberList, nil
}

func handleUpdatePreferences(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
	)

	file, err := c.FormFile("file")
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("import.invalidFile", "error", err.Error()))
	}

	categoryID, err := strconv.Atoi(c.FormValue("categoryId"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("Invalid Category Id", "error", err.Error()))
	}

	batchSize := config.ServerConfig.BatchSize

	channel := c.FormValue("channel")
	value, err := strconv.ParseBool(c.FormValue("value"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("Value Parsing error", "error", err.Error()))
	}

	tidType := c.FormValue("type")
	if tidType == "" {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("Invalid tID type"))
	}

	// var batch []models.PreferenceRequest
	prefChannels := getPrefChannels(channel, value)

	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	reader := csv.NewReader(src)

	// Skip the header row
	_, err = reader.Read()
	if err != nil {
		fmt.Println("Error reading header row")
		return err
	}

	go processBatch(c, reader, batchSize, categoryID, tidType, prefChannels)

	return nil
}

func getPrefChannels(channel string, value bool) models.RequestChannels {

	switch channel {
	case "sms":
		return models.RequestChannels{
			SMS: &models.ChannelOptions{
				Value: value,
			},
		}
	case "email":
		return models.RequestChannels{
			Email: &models.ChannelOptions{
				Value: value,
			},
		}
	case "fcm":
		return models.RequestChannels{
			FCM: &models.ChannelOptions{
				Value: value,
			},
		}
	default:
		return models.RequestChannels{
			SMS: &models.ChannelOptions{
				Value: value,
			},
		}
	}

}

func getNextBatch(reader *csv.Reader, batchSize, categoryID int, tidType string, prefChannels models.RequestChannels) []models.PreferenceRequest {
	var batch []models.PreferenceRequest
	for i := 0; i < batchSize; i++ {
		record, err := reader.Read()
		if err == io.EOF {
			break
			// End of file
		}
		if err != nil {
			fmt.Println("Error reading record")
			return nil
		}
		terminalID := record[0] // Assuming terminal ID is in the first column

		pref := models.PreferenceDTO{
			ID:       categoryID,
			Channels: prefChannels,
		}

		item := models.PreferenceRequest{
			Name:        terminalID,
			Type:        tidType,
			Preferences: []models.PreferenceDTO{pref},
		}

		batch = append(batch, item)
	}
	return batch
}

func handleCreatePreferencesBulk(app *App, c echo.Context, pref []models.PreferenceRequest, logger push.Logger) error {

	// handleCreatePreferencesBulk(pref, push.Logger{})
	data, _ := json.Marshal(pref)

	logger.Info().Msgf("got request for subscriber preferences: %s", string(data))
	data = nil

	uniqueCategory := make(map[int]bool)
	uniqueSubscribers := make(map[string]bool)
	var catIds []int

	for _, singlePref := range pref {
		//check for validations
		uniqueSubscribers[singlePref.Name+"_"+singlePref.Type] = true
		for _, values := range singlePref.Preferences {
			_, has := uniqueCategory[values.ID]
			if !has {
				uniqueCategory[values.ID] = true
				catIds = append(catIds, values.ID)
			}
		}
	}

	if len(uniqueSubscribers) == 0 {
		logger.Info().Msgf("No subscribers found in request")
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Invalid request",
			StatusCode:  "P101",
		})
	}
	if len(catIds) == 0 {
		logger.Info().Msgf("No category found in request")
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Invalid request",
			StatusCode:  "P101",
		})
	}
	subsString := getSubQueryString(uniqueSubscribers)
	subscriberList, err := fetchSubscribers(subsString)

	if err != nil {
		logger.Error().Msgf("Error occurred while fetching the subscribers %v", err)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}

	subMap := make(map[string]Subscriber)

	for _, s := range subscriberList {
		subMap[s.Name+"_"+s.Type] = s
	}

	newSubscribers := []Subscriber{}

	for key := range uniqueSubscribers {
		if _, exists := subMap[key]; !exists {
			lastIndex := strings.LastIndex(key, "_")
			uu, err := uuid.NewV4()
			if err != nil {
				logger.Error().Msgf("Error while generating uuid for %v", key)
				continue
			}
			newSubscribers = append(newSubscribers, Subscriber{
				Name: key[:lastIndex],
				Type: key[lastIndex+1:],
				UUID: uu.String(),
			})
		}
	}

	var newlyAdded []Subscriber

	if len(newSubscribers) != 0 {
		newlyAdded, err = insertNewSubscribers(newSubscribers, logger)
		if err != nil {
			logger.Error().Msgf("error while creating new subscribers %v", err)
			return c.JSON(http.StatusOK, prefResponse{
				Status:      "Failure",
				RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
				StatusCode:  "E101",
			})
		}
	}

	if len(newlyAdded) != len(newSubscribers) {
		//rare case of concurrency to be handled later
		logger.Info().Msgf("Conflict occured while inserting the subscribers")
	}

	for _, s := range newlyAdded {
		subMap[s.Name+"_"+s.Type] = s
	}

	categories, err := getCategories(catIds, app)

	if err != nil {
		logger.Error().Msgf("Error occured while fetching the categories %v", categories)
		return c.JSON(http.StatusOK, prefResponse{
			Status:      "Failure",
			RespMessage: "Sorry! Some internal Server Error occurred, Please try after some time",
			StatusCode:  "E101",
		})
	}
	prefMap := make(map[int]models.PreferenceDTO)
	for _, s := range categories {
		prefMap[s.ID] = s
	}

	requiredPrefs := []models.SubscriberPreferences{}

	for _, singlePref := range pref {
		sub, exists := subMap[singlePref.Name+"_"+singlePref.Type]
		if !exists {
			continue
		}
		for _, val := range singlePref.Preferences {
			catPref, has := prefMap[val.ID]
			if !has {
				continue
			}

			if val.Channels.Email != nil && catPref.Channels.Email.IsToggleable {
				requiredPrefs = append(requiredPrefs, getSubscriberPref(sub.ID, val.ID, "email", val.Channels.Email.Value))
			}
			if val.Channels.SMS != nil && catPref.Channels.SMS.IsToggleable {
				requiredPrefs = append(requiredPrefs, getSubscriberPref(sub.ID, val.ID, "sms", val.Channels.SMS.Value))
			}
			if val.Channels.FCM != nil && catPref.Channels.FCM.IsToggleable {
				requiredPrefs = append(requiredPrefs, getSubscriberPref(sub.ID, val.ID, "fcm", val.Channels.FCM.Value))
			}
		}
	}

	if len(requiredPrefs) != 0 {
		batchSize := 50
		for i := 0; i < len(requiredPrefs); i += batchSize {
			end := i + batchSize
			if end > len(requiredPrefs) {
				end = len(requiredPrefs)
			}
			insertPreferences(requiredPrefs[i:end], logger)
		}
	} else {
		logger.Info().Msgf("No preferences would be added for given request")
	}

	return c.JSON(http.StatusOK, prefResponse{
		Status:      "Success",
		RespMessage: "Successfully added preferences",
		StatusCode:  "S101",
	})
}
func processBatch(c echo.Context, reader *csv.Reader, batchSize, categoryID int, tidType string, prefChannels models.RequestChannels) error {
	// Read and process each line of the CSV
	for {
		batch := getNextBatch(reader, batchSize, categoryID, tidType, prefChannels)
		if batch == nil {
			break
		}
		handleCreatePreferencesBulk(app, c, batch, push.Logger{})
	}
	return nil
}
